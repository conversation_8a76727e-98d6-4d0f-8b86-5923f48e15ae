{
  
  "parser": "vue-eslint-parser",
  "parserOptions": {
    "parser": "@typescript-eslint/parser",
    "ecmaVersion": 2018,
    "sourceType": "module"
  },
  "extends": [
    "eslint:recommended",                     // 基础的 ESLint 规则
    "plugin:vue/vue3-recommended",            // Vue 3 项目的规则
    "prettier"                               // 关闭与 Prettier 冲突的规则
  ],
  "plugins": ["prettier"], // 使用 Prettier 插件
  "rules": {
    "prettier/prettier": ["error", {          // 强制遵循 Prettier 格式
      "endOfLine": "auto",                    // 处理换行符的问题，适应不同操作系统
      "singleQuote": false,                    // bu使用单引号
      "semi": true,                          // 不加分号
      "trailingComma": "es5",                 // 对象或数组最后加逗号
      "tabWidth": 2                           // 设置 tab 宽度
    }]
  }
}
