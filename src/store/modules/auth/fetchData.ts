import { IObj } from '@/types';
import { api } from '@/api';
import { $http } from '@tanzerfe/http';
import { ILoginRes, IsAdmin } from './type';

/**
 * 统一认证登录
 */
export function tyrzLogin(query: IObj<any>) {
  const url = api.getUrl(api.type.lease, api.sct.queryToken, query);
  return $http.post<ILoginRes>(url, { data: { _cfg: { showTip: true } } });
}

/**
 * 判断是否是管理员
 */
export function queryIsAdmin() {
  const url = api.getUrl(api.type.lease, api.name.queryIsAdmin);
  return $http.post<IsAdmin>(url, { data: { _cfg: { showTip: true } } });
}
