import { queryIsAdmin, tyrzLogin } from '@/store/modules/auth/fetchData';
import { defineStore } from 'pinia';
import { IAuthState } from './type';

export const useAuthStore = defineStore('auth-store', {
  persist: {
    key: 'scene_manage-auth-store',
    paths: ['userInfo', 'isAdmin'],
  },
  state: (): IAuthState => ({
    collapsedFlag: false, //组织树伸缩
    userInfo: {
      token: '',
      resourceVoList: [],
      systemLogo: '',
      systemName: '',
      id: '',
      userName: '',
      loginName: '',
      userTelphone: '',
      password: '',
      userEmail: '',
      photoUrl: '',
      userSex: '',
      userCardNo: '',
      dataRes: '',
      createUserId: '',
      sysCode: '',
      orgCode: '',
      deptCode: '',
      postCodes: '',
      roleIds: '',
      userType: '',
      userToken: '',
      userAuthId: '',
      unitId: '',
      uid: '',
      hasSupervise: '',
      userId: '',
      zhId: '',
      zhLogo: '',
      zhLogoUrl: '',
      zhName: '',
      zhPlatformUrl: '',
      topUnitId: '',
      associationTopUnitId: '',
    },
    isAdmin: false,
  }),
  getters: {
    // 统一认证token
    ticket() {
      const ticket = window.$_getUrlParams('token');
      return ticket || '';
    },
    // 是否登录
    isLogin(state) {
      return Boolean(state.userInfo.token);
    },
    menuList(state) {
      return state.userInfo.resourceVoList || [];
    },
  },
  actions: {
    loginTYRZ() {
      // 1成功 0失败
      return new Promise<0 | 1>((resolve) => {
        tyrzLogin({ token: this.ticket, sysCode: 'tenant_web' })
          .then((res: any) => {
            if ((res.code === 'success' || res.code === 200) && res?.data.token) {
              this.userInfo = res.data;
              // this.getAdminFlag();
              resolve(1);
            } else {
              resolve(0);
            }
          })
          .catch(() => {
            resolve(0);
          });
      });
    },
    getAdminFlag() {
      queryIsAdmin().then((res) => {
        this.isAdmin = res.data === '1';
      });
    },
    /** 重置auth状态 */
    resetAuthStore() {
      this.$reset();
    },
  },
});
