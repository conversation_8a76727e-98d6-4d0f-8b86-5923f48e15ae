interface IMenu {
  id: string;
  parentId: string;
  resOrder: number;
  resName: string;
  resAlias: string;
  resUrl: string;
  resType: string;
  resIdent: string;
  resRequestType: string;
  resIcon: string;
  sysCode: string;
  dataRes: string;
  createTime: string;
  createUserId: string;
  updateTime: string;
  updateUserId: string;
  isVisibled: string;
  isAdmin: string;
  delFlag: string;
  isChoose: string;
  childrens: IMenu[];
}

export interface ILoginRes {
  id: string;
  userName: string;
  loginName: string;
  userTelphone: string;
  photoUrl: string;
  userSex: string;
  password: string;
  expireTime: number;
  uid: string;
  token: string;
  sysCode: string;
  postId: string;
  postName: string;
  orgCode: string;
  orgName: string;
  deptId: string;
  deptName: string;
  unitId: string;
  unitName: string;
  userType: string;
  resourceVoList: IMenu[];
  roleIds: string;
  systemLogo: string;
  systemName: string;
  userEmail: string;
  userCardNo: string;
  dataRes: string;
  createUserId: string;
  deptCode: string;
  postCodes: string;
  userToken: string;
  userAuthId: string;
  hasSupervise: string;
  userId: string;
  zhId: string;
  zhLogo: string;
  zhLogoUrl: string;
  zhName: string;
  zhPlatformUrl: string;
  erecordUnitId: string;
  orgRes: string;
  roleMarks: string;
  topUnitId: string;
  associationTopUnitId: string;
}

export interface IAuthState {
  /** 用户信息 */
  userInfo: Partial<ILoginRes>;
  isAdmin: boolean;
  collapsedFlag: boolean;
}

export type IsAdmin = '1' | '0';
