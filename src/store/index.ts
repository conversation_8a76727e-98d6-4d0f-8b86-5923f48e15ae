import pkg from '../../package.json';
import type { IAuthState } from './type';
import { defineStore } from 'pinia';

export const useStore = defineStore(`${pkg.name}-store`, {
  persist: true,
  state: (): IAuthState => ({
    userInfo: {
      userToken: '',
    },
  }),
  getters: {
    // 登录状态
    isLogin(state) {
      return Boolean(state.userInfo.userToken);
    },
  },
  actions: {
    reset() {
      this.$reset();
    },
  },
});
