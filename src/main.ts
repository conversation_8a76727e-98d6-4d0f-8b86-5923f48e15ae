import 'hel-iso';
import * as Vue from 'vue';
// import { preFetchLib, bindVueRuntime } from 'hel-micro';
//
// bindVueRuntime({ Vue });

async function main() {
  // await preFetchLib('@tanzerfe/common-comps', {
  //   custom: {
  //     host: import.meta.env.DEV ? 'http://localhost:7001' : window.$SYS_CFG.commonCompsURL,
  //     enable: true,
  //   },
  // });

  await import('./loadApp');
}

main().catch(console.error);
