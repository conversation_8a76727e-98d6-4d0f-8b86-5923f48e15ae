import { RouteRecordRaw } from 'vue-router';
import MainLayout from '@/layouts/MainLayout.vue';
import Error404 from '@/views/error/ErrorPage.vue';
import Error403 from '@/views/error/ErrorPage403.vue';

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    name: 'topRoute',
    component: MainLayout,
    children: [
      {
        path: '/home',
        name: 'home',
        component: () => import('@/views/home/<USER>'),
      },
      {
        path: '/baseSet',
        name: 'baseSet',
        component: () => import('@/views/baseSet/index.vue'),
      },
      // 安全合规检查
      {
        path: '/safetyExamine',
        name: 'safetyExamine',
        component: () => import('@/views/safetyExamine/index.vue'),
      },
      // 安全合规检查
      {
        path: '/supervise',
        name: 'supervise',
        component: () => import('@/views/supervise/index.vue'),
      },
      {
        path: '/tenantry',
        name: 'tenantry',
        children: [
          {
            path: 'tenantManagement',
            name: 'tenantManagement',
            component: () => import('@/views/tenantry/tenantManagement/index.vue'),
          },
          {
            path: 'lesseeManage/:tenantryId',
            name: 'lesseeManage',
            component: () => import('@/views/lessee-manage/index.vue'),
          },
          {
            path: 'hazard-task-detail/:id?:tenantryId',
            name: 'hazardTaskDetail',
            component: () => import('@/views/lessee-manage/components/HazardAnalyze/detail/index.vue'),
          },
        ],
      },
    ],
  },
  {
    path: '/403',
    name: 'ERROR_403',
    component: Error403,
    meta: { noAuth: true },
  },
  {
    path: '/:catchAll(.*)*',
    name: 'ERROR_404',
    component: Error404,
    meta: { title: '404' },
  },
];

export default routes;
