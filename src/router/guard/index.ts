import type { Router } from 'vue-router';
import { useAuthStore } from '@/store/modules';
import { BridgeRemoteService } from '@/service/bridge/BridgeRemoteService.ts';
import { BRI_EVENT_TYPE, EVENT_TYPE } from '@/service/bridge/type.ts';

export function setupRouterGuard(router: Router) {
  createPageGuard(router);
}

/**
 * 页面路由权限
 * @param router
 */
export function createPageGuard(router: Router) {
  router.beforeEach(async (to, from, next) => {
    const auth = useAuthStore();

    // meta支持配置非授权页 { noAuth: true }
    if (to.meta?.noAuth) {
      return next();
    }
    if (!auth.isLogin) {
      const url = window.location.origin + window.location.pathname;
      if (auth.ticket) {
        const status = await auth.loginTYRZ();
        if (status) {
          next({ name: 'home' });
        } else {
          next({ name: 'ERROR_403' });
        }
      } else {
        next({ name: 'ERROR_403' });
      }
    } else {
      // 放行路由
      next();
      // 通知gis屏当前mis路由页面
      // setTimeout(() => {
      //   BridgeRemoteService.getIns().sendMessage(EVENT_TYPE.MESSAGE, {
      //     type: BRI_EVENT_TYPE.SET_MIS_PAGE,
      //     data: {
      //       name: to.name,
      //     },
      //   });
      // }, 100);
    }
  });
}
