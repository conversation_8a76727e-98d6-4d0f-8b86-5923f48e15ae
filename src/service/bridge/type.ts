/**
 * 通信协议消息类型
 */
export const enum EVENT_TYPE {
  MESSAGE = 'MESSAGE',
}

/**
 * 通信协议消息类型
 */
export const enum BRI_EVENT_TYPE {
  LOGIN = 'BRI_LOGIN',
  LOADED = 'BRI_LOADED',
  DESTROY = 'BRI_DESTROY',
  MESSAGE = 'BRI_MESSAGE',
}

/**
 * 消息体协议类型
 */
export interface ISender {
  type: BRI_EVENT_TYPE;
  data: ISenderData;
}

export interface ISenderData {
  type: BRI_EVENT_TYPE | string;
  data?: NonNullable<Record<string, any>>;
  params?: Record<string, any>;
}
