<template>
  <header class="com-header" :class="$style['header']">
    <div :class="$style['area']">
      <!--  left  -->
      <div :class="$style['area-l']" @click="goHome">
        <img class="w-[40px] h-[40px] mr-[10px]" :src="sysIcon" alt="" />
        <span :class="$style['title']">{{ sysName }}</span>
      </div>
      <!--  right  -->
      <div :class="$style['area-r']">
        <!-- <img class="w-[18px] cursor-pointer" :src="gisIcon" alt="gisIcon" @click="toGis" /> -->
        <avatar />
      </div>
    </div>
  </header>
</template>

<script lang="ts" setup>
import { useAuthStore } from '@/store/modules';
import { ref } from 'vue';
import Avatar from './comp/Avatar.vue';

const sysName = ref<string | undefined>('');
const sysIcon = ref<string | undefined>('');
const ui = useAuthStore();

function toGis() {
  window.open(window.$SYS_CFG.gisUrl, '_blank');
}

function getSysInfo() {
  sysName.value = ui.userInfo.zhName;
  sysIcon.value = ui.userInfo.zhLogoUrl + ui.userInfo.zhLogo + '.png';
  // document.title = res.data.zhName;
  // 修改浏览器页签图标
  setFavicon(ui.userInfo.zhLogo);
}

function goHome() {
  // window.open(ui.value.zhPlatformUrl, '_target')
  window.open(ui.userInfo.zhPlatformUrl, 'myWindow');
}
function setFavicon(sysIcon: string | undefined) {
  const icon = ui.userInfo.zhLogoUrl + ui.userInfo.zhLogo + '.png';
  // 创建新的 link 标签
  // document.title = ui.userInfo.zhName as string;
  const newLink = document.createElement('link');
  newLink.rel = 'icon';
  newLink.type = 'image/x-icon';
  newLink.href = icon;
  // 获取并移除原有的 favicon link
  const oldLinks = document.querySelector('link[rel="icon"]');
  if (oldLinks) oldLinks.parentNode?.removeChild(oldLinks);
  // 将新创建的 link 插入到 head 中
  document.head.appendChild(newLink);
}

getSysInfo();

defineOptions({ name: 'MisHeaderComp' });
</script>

<style module lang="scss">
.header {
  position: relative;
  overflow: hidden;
  background: #252843;
}

.area {
  position: relative;
  height: 100%;
  display: grid;
  grid-template-columns: 1fr auto;
  justify-content: space-between;
  align-items: center;
  z-index: 2;
}

.area-l {
  @apply flex items-center cursor-pointer;

  .title {
    font-family: 'youshe', serif;
    @apply text-[30px];
  }
}

.area-r {
  display: grid;
  grid-template-columns: auto 1fr;
  column-gap: 20px;
}

.title {
  color: #fff;
}
</style>
