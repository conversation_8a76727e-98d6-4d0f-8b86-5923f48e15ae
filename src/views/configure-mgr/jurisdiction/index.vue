<template>
  <div class="com-g-row-a1">
    <com-bread :data="breadData"></com-bread>
    <div class="com-g-row-a1 gap-y-[20px]">
      <filter-comp class="com-table-filter" @action="actionFn" />
      <table-comp class="com-table-container" ref="tableCompRef" @action="actionFn" />
    </div>

    <!-- aside -->
    <AsideComp v-model:show="isShowAside" :title="actionLabel" @action="actionFn" />
  </div>
</template>

<script lang="ts" setup>
import AsideComp from './comp/aside/index.vue';
import FilterComp from './comp/Filter.vue';
import TableComp from './comp/table/Table.vue';
import type { IActionData } from './type';
import { ACTION, ACTION_LABEL, PROVIDE_KEY } from './constant';
import { computed, provide, Ref, ref } from 'vue';
import { IBreadData } from '@/components/breadcrumb/type.ts';
import ComBread from '@/components/breadcrumb/ComBread.vue';

const breadData: IBreadData[] = [{ name: '配置管理' }, { name: '管辖范围配置' }];
const currentAction = ref<IActionData>({ action: ACTION.NONE, data: {} });
const actionLabel = computed(() => ACTION_LABEL[currentAction.value.action]);
const isShowAside = ref(false);
const tableCompRef = ref();

// provide
provide<Ref<IActionData>>(PROVIDE_KEY.currentAction, currentAction);

function actionFn(val: IActionData) {
  currentAction.value = val;

  if (val.action === ACTION.SEARCH) {
    handleSearch(val.data);
  } else {
    isShowAside.value = val.action === ACTION.EDIT;
  }
}

function handleSearch(data?: Record<string, any>) {
  if (data) {
    tableCompRef.value?.getTableDataWrap(data);
  } else {
    tableCompRef.value?.getTableData();
  }
}

defineOptions({ name: 'DemoJurisdictionIndex' });
</script>

<style module lang="scss"></style>
