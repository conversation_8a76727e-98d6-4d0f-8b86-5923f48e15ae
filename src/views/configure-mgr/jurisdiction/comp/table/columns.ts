import { DataTableColumn } from 'naive-ui';

export const cols: DataTableColumn[] = [
  {
    title: '序号',
    key: 'index',
    align: 'center',
    width: 65,
    render: (_: any, index: number) => {
      return index + 1;
    },
  },
  {
    title: '单位名称',
    key: 'unitName',
    align: 'center',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '单位类型',
    key: 'unitTypeName',
    align: 'center',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '行政辖区',
    key: 'areaName',
    align: 'center',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '是否建设物联网',
    key: 'isIotName',
    align: 'center',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '被管辖范围',
    key: 'jurisdictionName',
    align: 'center',
    ellipsis: {
      tooltip: true,
    },
  },
];
