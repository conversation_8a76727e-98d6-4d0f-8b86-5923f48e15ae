import type { IDetail, IPageDataRes } from './type';
import { $http } from '@tanzerfe/http';
import { api } from '@/api';
import { IObj } from '@/types';

// 获取分页
export function pageData(query: IObj<any>) {
  const url = api.getUrl(api.type.demo, api.name.demo.jurisdictionPageList, query);
  return $http.get<IPageDataRes>(url, { data: { _cfg: { showTip: true } } });
}

// 获取详情
export function getDetail(id: string) {
  const url = api.getUrl(api.type.demo, api.name.demo.jurisdictionDetail, { id });
  return $http.get<IDetail>(url, { data: { _cfg: { showTip: true } } });
}

// 更新
export function postUpdate(data: { id: string; jurisdiction: string }) {
  const url = api.getUrl(api.type.demo, api.name.demo.jurisdictionUpdate);
  return $http.post(url, { data: { _cfg: { showTip: true, showOkTip: true }, ...data } });
}
