<template>
  <div class="com-g-row-aa1">
    <ComBread :data="breadData" />
    <RadioTab :tabList="tabList" :tab="curTab" @change="handleChange" />
    <component :is="currentComp" />
  </div>
</template>

<script setup lang="ts">
import { computed, ref, Ref } from 'vue';
import RadioTab from '@/components/tab/ComRadioTabA.vue';
import ComBread from '@/components/breadcrumb/ComBread.vue';
import { ITabItem } from '@/components/tab/type';
import { useRoute, useRouter } from 'vue-router';
import CheckTemplate from './check-template/index.vue';
import CheckLibrary from './check-library/index.vue';
import { IBreadData } from '@/components/breadcrumb/type.ts';

const router = useRouter();
const route = useRoute();

// tabs
const tabList: Array<ITabItem & { comp: any }> = [
  { name: 'checkTemplate', label: '检查模板', comp: CheckTemplate },
  { name: 'checkLibrary', label: '检查项库', comp: CheckLibrary },
];

const curTab = ref<string>((route.query?.tab as string) || 'checkTemplate');
function handleChange(name: string) {
  curTab.value = name;
  router.push({
    query: { tab: name },
  });
}

const currentComp = computed(() => {
  return tabList.find((item) => item.name === curTab.value)?.comp || CheckTemplate;
});

const breadData: Ref<IBreadData[]> = computed(() => [
  { name: '配置管理' },
  { name: '监督检查清单配置' },
  {
    name: curTab.value === 'checkLibrary' ? '检查项库' : '检查模板',
    clickable: true,
    routeRaw: {
      name: 'checklistConfList',
      query: { tab: curTab.value === 'checkLibrary' ? 'checkLibrary' : 'checkTemplate' },
    },
  },
]);

defineOptions({ name: 'checklistConfComp' });
</script>

<style module lang="scss"></style>
