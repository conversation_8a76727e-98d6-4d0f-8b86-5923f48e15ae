<template>
  <div class="com-g-row-a1 gap-y-[20px]">
    <Filter class="com-table-filter" @action="actionFn" />
    <TableList class="com-table-container" ref="tableCompRef" @action="actionFn" />
  </div>
</template>

<script setup lang="ts">
import Filter from './comp/Filter.vue';
import TableList from './comp/table/Table.vue';
import { $dialog } from '@/common/shareContext/useDialogCtx.ts';
import { ACTION } from './constant';
import { deleteTemp } from './fetchData';
import { IActionData } from './type';
import { ICheckTempRow } from './type';
import { IObj } from '@/types';
import { ref } from 'vue';
import { useRouter } from 'vue-router';

const currentAction = ref<IActionData>({ action: ACTION.ADD, data: {} });

const router = useRouter();
function actionFn(val: IActionData) {
  currentAction.value = val;
  if (val.action === ACTION.ADD) {
    return router.push({ name: 'checklistConfModify' });
  }
  if (val.action === ACTION.SEARCH) {
    return handleSearch(val.data);
  }
  if (val.action === ACTION.DELETE) {
    return handleDelete(val.data as ICheckTempRow);
  }
}

const tableCompRef = ref();
function handleSearch(data?: IObj<any>) {
  if (data) {
    tableCompRef.value?.getTableDataWrap(data);
  } else {
    tableCompRef.value?.getTableData();
  }
}
// 删除
function handleDelete(data: ICheckTempRow) {
  $dialog.error({
    title: '删除模板',
    content: `确定删除该模板？`,
    positiveText: '确定',
    transformOrigin: 'center',
    onPositiveClick: async () => {
      deleteTemp({ templateId: data.id }).then(() => {
        handleSearch();
      });
    },
  });
}

defineOptions({ name: 'checkTemplateComp' });
</script>
<style module lang="scss"></style>
