<template>
  <div class="com-g-row-a1">
    <ComBread :data="breadData" />
    <div :class="$style['form-content']">
      <Header :class="$style['cus-header']" title="创建模板" :blueIcon="true" />
      <n-form
        class="com-g-row-aa1 gap-y-[24px] overflow-hidden"
        ref="formInstRef"
        :model="formData"
        :rules="rules"
        label-placement="left"
        label-align="left"
        label-width="110"
        require-mark-placement="left"
        :show-feedback="false"
      >
        <n-grid :cols="3" :x-gap="55" :y-gap="20">
          <n-form-item-gi label="模板名称：" path="templateName">
            <n-input v-model:value="formData.templateName" maxlength="30" placeholder="请输入，不超过30个字符" />
          </n-form-item-gi>
          <n-form-item-gi label="使用场景：" path="templateScene">
            <n-input v-model:value="formData.templateScene" maxlength="30" placeholder="请输入，不超过30个字符" />
          </n-form-item-gi>
        </n-grid>
        <n-form-item label="配置检查项：" path="templateItemList"></n-form-item>
        <CheckConf v-model:modelValue="formData.templateItemList" />
      </n-form>
      <div class="flex items-center justify-center">
        <n-button size="large" class="!mr-[20px]" @click="backToChecklist">取消</n-button>
        <n-button size="large" class=" " type="primary" @click="handleSubmit">确定</n-button>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { computed, onMounted, provide, ref } from 'vue';
import ComBread from '@/components/breadcrumb/ComBread.vue';
import Header from '@/components/header/ComHeaderB.vue';
import CheckConf from './comp/CheckConf.vue';
import { FormRules } from 'naive-ui';
import { useRoute, useRouter } from 'vue-router';
import { IFormData } from '../type';
import { queryUpdateTemplateDetail, saveTemplate, updateTemplate } from '../fetchData';
import { IBreadData } from '@/components/breadcrumb/type.ts';
import { $toast } from '@/common/shareContext/useToastCtx.ts';

const router = useRouter();
const route = useRoute();
const id = computed(() => route.params?.id || '');
const isEdit = computed(() => !!id.value);

const breadData: IBreadData[] = [
  { name: '配置管理' },
  { name: '监督检查清单配置' },
  {
    name: '检查模板',
    clickable: true,
    routeRaw: {
      name: 'checklistConfList',
      query: { tab: 'checkTemplate' },
    },
  },
  { name: !isEdit.value ? '创建模板' : '编辑模板' },
];

const rules: FormRules = {
  templateName: { required: true, message: '请输入模板名称', trigger: 'blur' },
  templateItemList: {
    required: true,
    message: '请至少选择一项',
    trigger: 'blur',
    type: 'array',
  },
};

const formData = ref<IFormData>(initForm()); // 表单数据
function initForm() {
  return {
    templateName: '',
    templateScene: '',
    templateItemList: [],
  };
}

// 取消
function backToChecklist() {
  router.push({ name: 'checklistConfList' });
}
// 确定
const formInstRef = ref();
function handleSubmit() {
  formInstRef.value?.validate(async (errors: any) => {
    if (!errors) {
      const _api = isEdit.value ? updateTemplate : saveTemplate;
      // 创建/更新 模板
      _api(formData.value).then(() => {
        router.push({ name: 'checklistConfList' });
      });
    } else {
      if (errors.length) {
        try {
          const first = errors[0][0];
          if (first.message) {
            $toast.error(first.message);
          }
        } catch (e) {}
      }
    }
  });
}

// 模板详情
function getTempDetail() {
  queryUpdateTemplateDetail({ templateId: id.value }).then((res) => {
    formData.value = res.data;
  });
}

const checkedItemKeys = computed(() => formData.value.templateItemList?.map((item: any) => item.itemId) || []);
provide('checkedItemKeys', checkedItemKeys);

onMounted(() => {
  isEdit.value ? getTempDetail() : '';
});

defineOptions({ name: 'checkTemplateModifyComp' });
</script>

<style module lang="scss">
.form-content {
  @apply w-full rounded-[4px] bg-[#F4F9FF] p-[24px] gap-y-[24px] overflow-hidden;
  display: grid;
  grid-template-rows: auto 1fr auto;
}

.cus-header {
  @apply text-[16px] font-bold;
}
</style>
