<template>
  <n-data-table
    remote
    striped
    :columns="columns"
    :data="tableData"
    :bordered="false"
    :loading="loading"
    :render-cell="useEmptyCell"
    :row-key="(row: any) => row.itemId"
    v-model:checked-row-keys="checkedKeys"
    @update:checked-row-keys="updateCheckedKeys"
  />
</template>

<script setup lang="ts">
import { inject, onMounted, ref, Ref } from 'vue';
import { DataTableColumns } from 'naive-ui';
import { cols } from './columns';
import { queryInspectTemplateItems } from '../../../fetchData';
import { ICheckItem } from '../../../type';
import { ACTION } from '../../../constant';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import { useEmptyCell } from '@/common/hooks/useEmptyCell.ts';

const [loading, search] = useAutoLoading(true);
const itemCategoryId = inject('itemCategoryId') as Ref<string>;

const emits = defineEmits(['action']);

const columns = ref<DataTableColumns>([]);
function setColumns() {
  columns.value.push(...cols);
}
setColumns();

const checkedItemKeys = inject('checkedItemKeys') as Ref<Array<string | number>>;
const checkedKeys = ref<Array<string | number>>(checkedItemKeys.value || []);

const tableData = ref<ICheckItem[]>([]);
async function getTableData() {
  await search(queryInspectTemplateItems({ itemCategoryId: itemCategoryId.value })).then((res) => {
    tableData.value = res.data;
  });
}

function updateCheckedKeys(
  keys: Array<string | number>,
  rows: ICheckItem[],
  meta: {
    row: object | undefined;
    action: 'check' | 'uncheck' | 'checkAll' | 'uncheckAll';
  }
) {
  if (['check', 'checkAll'].includes(meta.action)) {
    return emitsRows(ACTION.CHECK, rows);
  }
  if (meta.action === 'uncheck') {
    return emitsRows(ACTION.UN_CHECK, [meta.row as ICheckItem]);
  }
  if (meta.action === 'uncheckAll') {
    return emitsRows(ACTION.UN_CHECK, tableData.value);
  }
}

function emitsRows(action: ACTION, data: ICheckItem[]) {
  let _rows = data.filter((e) => e?.categoryId && e?.itemCategoryId && e?.itemId);
  _rows = _rows.map((e) => {
    return {
      categoryId: e.categoryId,
      categoryName: e.categoryName,
      itemCategoryId: e.itemCategoryId,
      itemCategoryName: e.itemCategoryName,
      itemId: e.itemId,
    };
  });
  return emits('action', { action, data: _rows });
}

onMounted(() => {
  getTableData();
});

defineOptions({ name: 'checkConfTableComp' });
</script>

<style module lang="scss"></style>
