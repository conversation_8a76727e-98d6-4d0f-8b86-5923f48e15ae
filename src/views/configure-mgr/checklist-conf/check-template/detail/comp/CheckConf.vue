<template>
  <div :class="$style['check-tem-conf']">
    <div :class="$style['major-type']">
      <p :class="$style['major-title']">检查大类</p>
      <div
        v-for="category in categoryTree"
        :key="category.categoryName"
        :class="[$style['type-item'], curCategory.categoryName === category.categoryName ? $style.active : '']"
        @click="checkCategory(category)"
        class="truncate"
      >
        {{ category.categoryName }}
      </div>
    </div>
    <div class="com-g-row-a1" :class="$style['check-items']">
      <p>
        <span class="text-[#0249B1] mr-[34px]">检查项</span>
        已选 <span>{{ props.categoryCount }}</span> 大类 <span>{{ props.itemCategoryCount }}</span> 个检查项
      </p>
      <div :class="$style['check-table']">
        <n-scrollbar>
          <n-collapse
            arrow-placement="right"
            :accordion="true"
            v-for="subType in curCategory.children"
            :key="subType.itemCategoryId"
            @update:expanded-names="expandType"
          >
            <n-collapse-item :name="subType.itemCategoryId">
              <template #header>
                <p class="flex-1 py-[12px]">检查小类：{{ subType.itemCategoryName }}</p>
              </template>
              <TableList ref="tableCompRef" />
            </n-collapse-item>
          </n-collapse>
        </n-scrollbar>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { inject, onMounted, provide, ref } from 'vue';
import TableList from './table/Table.vue';
import { detailCategoryTree } from '@/views/configure-mgr/checklist-conf/check-template/fetchData';
import { ICheckCategoryTree } from '@/views/configure-mgr/checklist-conf/check-template/type';

const categoryTree = ref<ICheckCategoryTree[]>([]);
const curCategory = ref<Partial<ICheckCategoryTree>>({});

const templateId = inject('templateId');

const props = withDefaults(defineProps<{ categoryCount?: number; itemCategoryCount?: number }>(), {
  categoryCount: () => 0,
  itemCategoryCount: () => 0,
});

function getCategoryTree() {
  detailCategoryTree({ templateId }).then((res) => {
    categoryTree.value = res.data;
    if (res.data.length) {
      curCategory.value = res.data[0];
    }
  });
}

// 切换大类
function checkCategory(category: ICheckCategoryTree) {
  curCategory.value = category;
}

// 当前展开的小类
const itemCategoryId = ref('');
provide('itemCategoryId', itemCategoryId);

function expandType(expandedNames: Array<string | number> | string | number | null) {
  if (!expandedNames) return;
  itemCategoryId.value = expandedNames as string;
}

/*const categoryIds = ref<string[]>([]);
const itemIds = ref<string[]>([]);*/

onMounted(() => {
  getCategoryTree();
});

defineOptions({ name: 'checkConfComp' });
</script>
<style module lang="scss">
.check-tem-conf {
  @apply w-full -mt-[16px] flex gap-x-[20px] box-border overflow-hidden;
  border: 1px solid #e4e5eb;

  .major-type {
    @apply min-w-[276px] px-[6px];

    .major-title {
      @apply text-[#0249B1] py-[20px] px-[18px];
    }

    .type-item {
      @apply w-[264px] px-[18px] h-[48px] leading-[48px] rounded-[6px]  mb-[6px];
      background: rgba(0, 0, 0, 0.04);
    }

    .active {
      @apply bg-[#0249B1] text-white;
    }
  }

  .check-items {
    @apply px-[24px] py-[20px] w-full gap-y-[16px] overflow-hidden;
    border-left: 1px solid #e4e5eb;

    .check-table {
      @apply w-full overflow-auto;
      border: 1px solid #e4e5eb;

      :global(.n-collapse-item__header) {
        @apply px-[24px];
      }

      :global(.n-collapse-item__content-inner) {
        padding-top: 0;
      }
    }
  }
}
</style>
