<template>
  <n-data-table
    class="h-full"
    remote
    striped
    :columns="columns"
    :data="tableData"
    :bordered="false"
    :loading="loading"
    :render-cell="useEmptyCell"
  />
</template>

<script setup lang="ts">
import { inject, onMounted, ref, Ref, watch } from 'vue';
import { DataTableColumns } from 'naive-ui';
import { cols } from './columns';
import { queryTemplateDetailItems } from '@/views/configure-mgr/checklist-conf/check-template/fetchData';
import { ICheckItem } from '@/views/configure-mgr/checklist-conf/check-template/type';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import { useEmptyCell } from '@/common/hooks/useEmptyCell.ts';

const [loading, search] = useAutoLoading(true);
const itemCategoryId = inject('itemCategoryId') as Ref<string>;

const templateId = inject('templateId');

const columns = ref<DataTableColumns>([]);
function setColumns() {
  columns.value.push(...cols);
}
setColumns();

const tableData = ref<ICheckItem[]>([]);
function getTableData() {
  search(queryTemplateDetailItems({ itemCategoryId: itemCategoryId.value, templateId })).then((res) => {
    tableData.value = res.data;
  });
}

onMounted(() => {
  watch(
    () => itemCategoryId.value,
    (nv) => {
      if (nv) {
        getTableData();
      }
    },
    { immediate: true }
  );
});

defineOptions({ name: 'checkConfTableComp' });
</script>
<style module lang="scss"></style>
