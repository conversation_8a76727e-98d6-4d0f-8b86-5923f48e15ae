import { DataTableColumn, NInput, NRadio, NRadioGroup, NSpace } from 'naive-ui';
import { InternalRowData } from 'naive-ui/es/data-table/src/interface';
import { ICheckOption } from '@/views/configure-mgr/checklist-conf/check-template/type';
import { h } from 'vue';

export const cols: DataTableColumn[] = [
  {
    title: '序号',
    key: 'index',
    width: 65,
    align: 'center',
    render: (_: any, index: number) => {
      return index + 1;
    },
  },
  {
    title: '检查项名称',
    key: 'itemName',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '检查项类型',
    key: 'itemTypeName',
    align: 'left',
  },
  {
    title: '选项',
    key: 'optionVoList',
    align: 'left',
    render: (row: InternalRowData) => {
      const optionVoList = row.optionVoList as ICheckOption[];

      if (row.itemType !== '1') {
        return h(NInput, { disabled: true, placeholder: '请输入，不超过50个字符' });
      }

      if (!optionVoList || !optionVoList.length) return '--';

      return h(NRadioGroup, { disabled: true }, () => [
        h(NSpace, { vertical: true }, () =>
          optionVoList.map((item: any) => {
            if (item.keyType !== '2') {
              return h(NRadio, {}, () => item.optionTitle);
            } else {
              return h(NSpace, { align: 'center' }, () => [
                h(NRadio, {}, () => item.optionTitle),
                h(NInput, { disabled: true }),
              ]);
            }
          })
        ),
      ]);
    },
  },
];
