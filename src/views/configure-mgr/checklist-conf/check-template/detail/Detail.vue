<template>
  <div class="com-g-row-a1">
    <ComBread :data="breadData" />
    <div :class="$style['form-content']">
      <Header :class="$style['cus-header']" title="模板详情" :blueIcon="true" />
      <n-form
        class="com-g-row-aa1 gap-y-[24px] overflow-hidden"
        ref="formInstRef"
        :model="formData"
        label-placement="left"
        label-align="left"
        label-width="110"
        require-mark-placement="left"
        :show-feedback="false"
      >
        <n-grid :cols="3" :x-gap="55" :y-gap="20">
          <n-form-item-gi label="模板名称：" path="templateName">
            <span>{{ formData.templateName }}</span>
          </n-form-item-gi>
          <n-form-item-gi label="使用场景：" path="templateScene">
            <span>{{ formData.templateScene }}</span>
          </n-form-item-gi>
        </n-grid>
        <n-form-item label="配置检查项：" path="templateItemList"></n-form-item>
        <CheckConf :categoryCount="formData.categoryCount" :itemCategoryCount="formData.itemCount" />
      </n-form>
      <div class="flex items-center justify-center">
        <n-button size="large" class="!mr-[20px]" @click="backToChecklist">返回</n-button>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import Header from '@/components/header/ComHeaderB.vue';
import CheckConf from './comp/CheckConf.vue';
import ComBread from '@/components/breadcrumb/ComBread.vue';
import { useRoute, useRouter } from 'vue-router';
import { computed, onMounted, provide, ref } from 'vue';
import { IFormData } from '../type';
import { getTemplateDetail } from '../fetchData';
import { IBreadData } from '@/components/breadcrumb/type.ts';

const breadData: IBreadData[] = [
  { name: '配置管理' },
  { name: '监督检查清单配置' },
  {
    name: '检查模板',
    clickable: true,
    routeRaw: {
      name: 'checklistConfList',
      query: { tab: 'checkTemplate' },
    },
  },
  { name: '模板详情' },
];

const router = useRouter();
const route = useRoute();
const templateId = computed(() => route.params?.id || '');
provide('templateId', templateId.value);

const formData = ref<IFormData>(initForm()); // 表单数据
function initForm() {
  return {
    templateName: '',
    templateScene: '',
    categoryCount: 0,
    itemCount: 0,
  };
}

// 取消
function backToChecklist() {
  router.push({ name: 'checklistConfList' });
}

// 详情
function getDetailData() {
  getTemplateDetail({ templateId: templateId.value }).then((res) => {
    formData.value = res.data;
  });
}

onMounted(() => {
  getDetailData();
});

defineOptions({ name: 'checkTemplateDetailComp' });
</script>
<style module lang="scss">
.form-content {
  @apply w-full rounded-[4px] bg-[#F4F9FF] p-[24px] gap-y-[24px] overflow-hidden;
  display: grid;
  grid-template-rows: auto 1fr auto;
}
.cus-header {
  @apply text-[16px] font-bold;
}
</style>
