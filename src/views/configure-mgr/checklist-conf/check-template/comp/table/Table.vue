<template>
  <n-data-table
    class="h-full"
    remote
    striped
    :columns="columns"
    :data="tableData"
    :bordered="false"
    :flex-height="true"
    :loading="loading"
    :pagination="pagination"
    :render-cell="useEmptyCell"
  />
</template>

<script setup lang="ts">
import { h, ref, VNode, toRaw } from 'vue';
import { DataTableColumns, NButton } from 'naive-ui';
import { cols } from '@/views/configure-mgr/checklist-conf/check-template/comp/table/columns';
import { ACTION } from '@/views/configure-mgr/checklist-conf/check-template/constant';
import { useRouter } from 'vue-router';
import { getTempPageList } from '@/views/configure-mgr/checklist-conf/check-template/fetchData';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import { useNaivePagination } from '@/common/hooks/useNaivePagination.ts';
import { useActionDivider } from '@/common/hooks/useActionDivider.ts';
import { IObj } from '@/types';
import { useEmptyCell } from '@/common/hooks/useEmptyCell.ts';

const router = useRouter();
const emits = defineEmits(['action']);

const [loading, search] = useAutoLoading(true);
const { pagination, updateTotal } = useNaivePagination(getTableData);

const columns = ref<DataTableColumns>([]);
const tableData = ref<any[]>([]);
function setColumns() {
  columns.value.push(...cols);
  // 添加操作栏 action
  columns.value.push({
    title: '操作',
    key: 'actions',
    align: 'center',
    width: 240,
    render(row) {
      return getActionBtn(row);
    },
  });
}
setColumns();

function getActionBtn(row: any) {
  const acList: [VNode, boolean?][] = [
    [
      h(
        NButton,
        {
          text: true,
          class: 'com-action-button',
          onClick: () => router.push({ name: 'checklistConfDetail', params: { id: row.id } }),
        },
        { default: () => '详情' }
      ),
    ],
    [
      h(
        NButton,
        {
          text: true,
          class: 'com-action-button',
          onClick: () => router.push({ name: 'checklistConfModify', params: { id: row.id } }),
        },
        { default: () => '编辑' }
      ),
      false, // 需求变更，去掉编辑功能
    ],
    [
      h(
        NButton,
        {
          text: true,
          class: 'com-action-button',
          onClick: () => emits('action', { action: ACTION.DELETE, data: toRaw(row) }),
        },
        { default: () => '删除' }
      ),
    ],
  ];
  return useActionDivider(acList);
}

let filterData: IObj<any> = {}; // 搜索条件
function getTableData() {
  const params = {
    pageNo: pagination.page,
    pageSize: pagination.pageSize,
    ...filterData,
  };

  search(getTempPageList(params)).then((res) => {
    tableData.value = res.data.rows || [];
    updateTotal(res.data.total || 0);
  });
}

function getTableDataWrap(data: IObj<any>) {
  filterData = Object.assign({}, data) || {};
  pagination.page = 1;
  getTableData();
}
defineOptions({ name: 'checkTampTableComp' });
defineExpose({ getTableDataWrap, getTableData });
</script>
<style module lang="scss"></style>
