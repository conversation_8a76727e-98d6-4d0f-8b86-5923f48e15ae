import { DataTableColumn } from 'naive-ui';

export const cols: DataTableColumn[] = [
  {
    title: '序号',
    key: 'index',
    width: 65,
    align: 'center',
    render: (_: any, index: number) => {
      return index + 1;
    },
  },
  {
    title: '模板名称',
    key: 'templateName',
    align: 'center',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '使用场景',
    key: 'templateScene',
    align: 'center',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '创建人',
    key: 'createUser',
    align: 'center',
    width: 80,
  },
  {
    title: '创建机构',
    key: 'orgName',
    align: 'center',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '创建时间',
    key: 'createTime',
    align: 'center',
    width: 170,
  },
];
