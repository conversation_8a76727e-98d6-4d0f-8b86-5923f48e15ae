<template>
  <div class="com-g-row-a1">
    <ComBread :data="breadData" />
    <n-scrollbar :class="$style.container" class="com-container-bg">
      <div class="flex mb-[24px]">
        <img class="pr-[10px]" src="../assets/title-icon.png" alt="" />
        <span class="text-[#1F2225] text-[16px] font-bold">{{ isEdit ? '编辑' : '创建' }}检查项</span>
      </div>
      <n-form ref="formRef" :model="formData" label-placement="left" label-width="100px">
        <div class="flex justify-between items-center">
          <n-form-item class="w-[300px]" label="所属类别:" path="categoryId" :rule="rules.categoryId">
            <n-cascader
              v-model:value="type"
              :options="sslbOpts"
              check-strategy="child"
              expand-trigger="click"
              clearable
              filterable
              children-field="children"
              label-field="text"
              value-field="id"
              @update:value="sslbChange"
            />
          </n-form-item>
        </div>
        <n-form-item label="检查项列表:" v-if="!isEdit">
          <n-button type="primary" ghost @click="addCheckItem" :disabled="formData.list.length > 9">
            新增检查项
          </n-button>
        </n-form-item>
        <div :class="$style.item" v-for="(form, i) in formData.list" :key="i">
          <n-form-item label="检查项类型" path="">
            <n-radio-group v-model:value="form.itemType">
              <n-space>
                <n-radio value="1">单选</n-radio>
                <n-radio value="2">填空</n-radio>
              </n-space>
            </n-radio-group>
          </n-form-item>
          <n-form-item label="检查项内容" :path="`list[${i}].itemName`" :rule="rules.itemName">
            <n-input
              v-model:value="form.itemName"
              class="!w-[700px]"
              maxlength="100"
              show-count
              clearable
              placeholder="请输入"
              type="textarea"
              :autosize="{
                minRows: 2,
                maxRows: 4,
              }"
            />
            <div v-if="form.itemType === '1'" class="ml-[24px] flex">
              <MdAddBox class="text-[20px] text-[#0249B1] mr-[10px] cursor-pointer" @click="addOption(form)" />
              <span class="text-[#333639] cursor-pointer" @click="addOption(form)">新增选项</span>
            </div>
          </n-form-item>
          <template v-if="form.itemType === '1'">
            <n-form-item
              :label="`选项${idx + 1}`"
              :path="`list[${i}].optionVoList[${idx}].optionTitle`"
              :rule="rules.optionTitle"
              v-for="(opt, idx) in form.optionVoList"
              :key="idx"
            >
              <n-input
                v-model:value="opt.optionTitle"
                class="!w-[700px]"
                maxlength="30"
                clearable
                placeholder="请输入不超过30个字符"
              />
              <div class="ml-[24px] flex items-center">
                <AkCircleMinusFill
                  class="text-[20px] text-[#0249B1] mr-[36px] cursor-pointer"
                  v-if="idx !== 0"
                  @click="removeOption(form, idx)"
                />
                <n-space item-style="display: flex;" align="center" :class="idx === 0 ? 'ml-[56px]' : ''">
                  <n-checkbox v-model:checked="opt.keyType" checked-value="2" unchecked-value="1"> 填空 </n-checkbox>
                  <n-checkbox v-model:checked="opt.keyOption" checked-value="1" unchecked-value="0">
                    该项为违法项
                  </n-checkbox>
                </n-space>
              </div>
            </n-form-item>
          </template>
          <n-button v-if="i !== 0" color="#EE1212" ghost :class="$style['del-btn']" @click="removeFormItem(i)">
            删除
          </n-button>
        </div>
      </n-form>
      <div :class="$style['controls']">
        <n-button @click="router.back()"> 取消 </n-button>
        <n-button type="primary" @click="save"> 确定 </n-button>
      </div>
    </n-scrollbar>
  </div>
</template>

<script setup lang="ts">
import ComBread from '@/components/breadcrumb/ComBread.vue';
import { $toast } from '@/common/shareContext/useToastCtx.ts';
import { AkCircleMinusFill, MdAddBox } from '@kalimahapps/vue-icons';
import { computed, ref, Ref } from 'vue';
import { FormInst } from 'naive-ui';
import { IBreadData } from '@/components/breadcrumb/type.ts';
import { IDict } from '@/types';
import { queryCategoryTree, queryInspectItemDetail, saveInspectItem, updateItem } from '../fetchData';
import { trimLastEmptyChild } from '@/utils/tree.ts';
import { useRoute, useRouter } from 'vue-router';

const router = useRouter();
const route = useRoute();
const breadData: Ref<IBreadData[]> = computed(() => [
  { name: '配置管理' },
  { name: '监督检查清单配置' },
  { name: '检查项库' },
]);

const isEdit = computed(() => !!route.query.id);
const formRef = ref<FormInst | null>();
const formData = ref({
  categoryId: '', // 大类
  itemCategoryId: '', // 小类
  list: [
    {
      categoryId: '',
      itemCategoryId: '',
      itemName: '',
      itemType: '1',
      optionVoList: [null, null, null].map(() => {
        return {
          itemCategoryId: '',
          keyOption: '0',
          keyType: '1',
          optionTitle: '',
        };
      }),
    },
  ],
});
const type = ref(null);
const sslbOpts = ref<any[]>([]);

const rules = {
  categoryId: {
    required: true,
    message: '请选择所属类别',
    trigger: ['input', 'blur'],
  },
  itemName: {
    required: true,
    message: '请输入检查项内容',
    trigger: ['input', 'blur'],
  },
  optionTitle: {
    required: true,
    message: '请输入选项内容',
    trigger: ['input', 'blur'],
  },
};
function sslbChange(v: string, options: IDict, path: IDict[]) {
  if (path) {
    const ids = path.map((item) => item.id);
    formData.value.categoryId = ids[0];
    formData.value.itemCategoryId = ids[1] || '';
  }
}

// 新增检查项
function addCheckItem() {
  formData.value.list.push({
    categoryId: '',
    itemCategoryId: '',
    itemName: '',
    itemType: '1',
    optionVoList: [null, null, null].map(() => {
      return {
        itemCategoryId: '',
        keyOption: '0',
        keyType: '1',
        optionTitle: '',
      };
    }),
  });
}

// 新增选项
function addOption(form: any) {
  if (form.optionVoList.length >= 8) return;
  form.optionVoList.push({
    itemCategoryId: '',
    keyOption: '0',
    keyType: '1',
    optionTitle: '',
  });
}

// 移除选项
function removeOption(form: any, idx: number) {
  form.optionVoList.splice(idx, 1);
}

// 移除检查项
function removeFormItem(idx: number) {
  formData.value.list.splice(idx, 1);
}

// 单选时判断必须选择一个选项为违法项
function checkOptions(list: any[]) {
  return list.some((item) => {
    return (
      item.itemType === '1' && item.optionVoList && item.optionVoList.every((option: any) => option.keyOption === '0')
    );
  });
}

function save() {
  formRef.value?.validate((errors) => {
    if (!errors) {
      const result = checkOptions(formData.value.list);
      if (result) return $toast.warning('检查项为单选时请选择一个违法项');
      const data = formData.value.list.map((item) => {
        item.categoryId = formData.value.categoryId;
        item.itemCategoryId = formData.value.itemCategoryId;
        if (item.itemType === '2') {
          item.optionVoList = [];
        } else {
          item.optionVoList.forEach((v) => {
            v.itemCategoryId = formData.value.itemCategoryId;
          });
        }
        return item;
      });
      if (isEdit.value) {
        updateItem(data[0]).then((res) => {
          console.log(res.data);
          router.back();
        });
      } else {
        saveInspectItem({ inspectItemVos: data }).then((res) => {
          console.log(res.data);
          router.back();
        });
      }
    }
  });
}

function getItemInfo() {
  queryInspectItemDetail({ itemId: route.query?.id }).then((res) => {
    type.value = res.data.itemCategoryId;
    formData.value.categoryId = res.data.categoryId;
    formData.value.itemCategoryId = res.data.itemCategoryId;
    formData.value.list = [res.data];
  });
}

function init() {
  queryCategoryTree().then((res) => {
    sslbOpts.value = trimLastEmptyChild(res.data, 'children').map((item) => {
      // 仅大类时禁止选择
      item.disabled = !item.hasParent && !item.children;
      return item;
    });
  });
  if (route.query?.id) getItemInfo();
}
init();
defineOptions({ name: 'CheckLibraryItemCreate' });
</script>

<style module lang="scss">
.container {
  @apply flex flex-col p-[20px] overflow-y-auto;
}
.item {
  @apply p-[16px] mb-[16px] relative;
  &:last-child {
    @apply mb-0;
  }
  .del-btn {
    @apply absolute top-[16px] right-[24px];
  }
  border: 1px solid #e4e5eb;
}

.controls {
  @apply flex justify-center mt-[50px];
  :global(.n-button) {
    @apply w-[82px] ml-[20px];
    :first-child {
      @apply ml-0;
    }
  }
}
</style>
