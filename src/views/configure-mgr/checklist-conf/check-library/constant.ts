import { IObj } from '@/types';

export const enum PROVIDE_KEY {
  currentAction = 'currentAction',
}

export const enum ACTION {
  SEARCH = 'SEARCH',
  SET = 'SET',
  DETAIL = 'DETAIL',
  ADD = 'ADD',
  EDIT = 'EDIT',
}

export const ACTION_LABEL: { [key in ACTION]: string } = {
  [ACTION.SEARCH]: '搜索',
  [ACTION.SET]: '设置小类',
  [ACTION.DETAIL]: '设备详情',
  [ACTION.ADD]: '创建检查类别',
  [ACTION.EDIT]: '编辑检查类别',
};

export interface IActionData {
  action: ACTION;
  data: IObj<any>;
}
