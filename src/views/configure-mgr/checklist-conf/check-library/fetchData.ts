import { ICategory, ICategoryTree, IInspectItems } from './type';
import { $http } from '@tanzerfe/http';
import { api } from '@/api';
import { IObj } from '@/types';

// 获取检查大类类别
export function queryParentCategory() {
  const url = api.getUrl(api.type.demo, api.name.demo.queryParentCategory);
  return $http.get<ICategory[]>(url, { data: { _cfg: { showTip: true } } });
}

// 创建检查类别
export function saveCategory(data: IObj<any>) {
  const url = api.getUrl(api.type.demo, api.name.demo.saveCategory);
  return $http.post(url, {
    data: { _cfg: { showTip: true, showOkTip: true, okTipContent: '类别创建成功' }, ...data },
  });
}

// 创建检查类别
export function updateCategory(data: IObj<any>) {
  const url = api.getUrl(api.type.demo, api.name.demo.updateCategory);
  return $http.post(url, {
    data: { _cfg: { showTip: true, showOkTip: true, okTipContent: '类别编辑成功' }, ...data },
  });
}

// 根据ID查询检查类别信息
export function queryCategoryInfoById(query: IObj<any>) {
  const url = api.getUrl(api.type.demo, api.name.demo.queryCategoryInfoById, { ...query });
  return $http.post<ICategory>(url, { data: { _cfg: { showTip: true } } });
}

// 获取检查项
export function queryInspectItems(query: IObj<any>) {
  const url = api.getUrl(api.type.demo, api.name.demo.queryInspectItems, { ...query });
  return $http.get<IInspectItems[]>(url, { data: { _cfg: { showTip: true } } });
}

// 检查模板分页
export function queryCategoryTree() {
  const url = api.getUrl(api.type.demo, api.name.demo.queryCategoryTree);
  return $http.get<ICategoryTree[]>(url, { data: { _cfg: { showTip: true } } });
}

// 保存检查项
export function saveInspectItem(data: IObj<any>) {
  const url = api.getUrl(api.type.demo, api.name.demo.saveInspectItem);
  return $http.post(url, {
    data: { _cfg: { showTip: true, showOkTip: true, okTipContent: '检查项创建成功' }, ...data },
  });
}

// 删除检查类别信息0-大类 1-小类 2-检查项
export function delInspectCategoryTree(query: IObj<any>) {
  const url = api.getUrl(api.type.demo, api.name.demo.delInspectCategoryTree, { ...query });
  return $http.get(url, { data: { _cfg: { showTip: true, showOkTip: true, okTipContent: '删除成功' } } });
}

// 获取检查项信息详情
export function queryInspectItemDetail(query: IObj<any>) {
  const url = api.getUrl(api.type.demo, api.name.demo.queryInspectItemDetail, { ...query });
  return $http.get<any>(url, { data: { _cfg: { showTip: true } } });
}

// 编辑检查项
export function updateItem(data: IObj<any>) {
  const url = api.getUrl(api.type.demo, api.name.demo.updateItem);
  return $http.post(url, {
    data: { _cfg: { showTip: true, showOkTip: true, okTipContent: '检查项编辑成功' }, ...data },
  });
}
