<template>
  <div :class="$style['check-items']">
    <div :class="$style.header">
      <span class="mr-auto">检查项</span>
      <n-button type="primary" @click="createCategory">创建检查类别 <template #icon> + </template></n-button>
      <n-button class="!ml-[16px]" type="primary" @click="createCheckItem">
        创建检查项 <template #icon> + </template>
      </n-button>
    </div>
    <n-scrollbar class="!h-[calc(100%-54px)]" :class="$style.container">
      <n-collapse
        arrow-placement="right"
        @item-header-click="handleExpand"
        accordion
        :trigger-areas="['main', 'arrow']"
      >
        <n-collapse-item v-for="item in categoryList" :key="item.id" :name="item.id">
          <template #header>
            <div :class="$style.category">检查小类：{{ item.text }}</div>
          </template>
          <template #header-extra>
            <div :class="$style['extra-btn']">
              <n-button text color="#0249b1" @click="handleCEdit(item)">编辑</n-button>
              <n-button text color="#0249b1" @click="handleCDelete(item)">删除</n-button>
            </div>
          </template>
          <n-data-table
            remote
            striped
            :columns="columns"
            :data="tableData"
            :bordered="false"
            :loading="loading"
            :render-cell="useEmptyCell"
          />
        </n-collapse-item>
      </n-collapse>
    </n-scrollbar>
  </div>
</template>

<script setup lang="ts">
import { h, ref, VNode } from 'vue';
import { DataTableColumns, NButton } from 'naive-ui';
import { cols } from './columns';
import { useRouter } from 'vue-router';
import { delInspectCategoryTree, queryInspectItems } from '../../fetchData';
import { ICategoryTree, IInspectItems } from '../../type';
import { ACTION } from '../../constant';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import { useActionDivider } from '@/common/hooks/useActionDivider.ts';
import { $dialog } from '@/common/shareContext/useDialogCtx.ts';
import { useEmptyCell } from '@/common/hooks/useEmptyCell.ts';

const router = useRouter();
const $emits = defineEmits(['action']);
const categoryList = ref<ICategoryTree[]>([]);
const columns = ref<DataTableColumns>([]);
const tableData = ref<IInspectItems[]>([]);
const [loading, search] = useAutoLoading(false);
const itemCategoryId = ref('');

function setColumns() {
  columns.value.push(...cols);
  // 添加操作栏 action
  columns.value.push({
    title: '操作',
    key: 'actions',
    align: 'center',
    width: 140,
    render(row) {
      return getActionBtn(row);
    },
  });
}
setColumns();
function getActionBtn(row: any) {
  const acList: [VNode, boolean?][] = [
    [
      h(
        NButton,
        {
          text: true,
          class: 'com-action-button',
          onClick: () => editCheckItem(row),
        },
        { default: () => '编辑' }
      ),
    ],
    [
      h(
        NButton,
        {
          text: true,
          class: 'com-action-button',
          onClick: () => handleDelete(row),
        },
        { default: () => '删除' }
      ),
    ],
  ];

  return useActionDivider(acList);
}

function handleExpand(data: { name: string; expanded: boolean }) {
  if (!data.expanded) return;
  itemCategoryId.value = data.name;
  getTableData();
}

function getTableData() {
  const params = { itemCategoryId: itemCategoryId.value };
  search(queryInspectItems(params)).then((res) => {
    tableData.value = res.data;
  });
}

// 删除检查项
function handleDelete(row: IInspectItems) {
  $dialog.error({
    title: '删除检查项',
    content: '确定删除该检查项？',
    positiveText: '确定',
    negativeText: '取消',
    transformOrigin: 'center',
    onPositiveClick: async () => {
      await delInspectCategoryTree({ id: row.itemId, type: '2' });
      getTableData();
    },
  });
}

// 编辑小类
function handleCEdit(data: ICategoryTree) {
  $emits('action', { action: ACTION.EDIT, data });
}

// 删除小类
function handleCDelete(data: ICategoryTree) {
  $dialog.error({
    title: '删除检查小类',
    content: '删除后该小类下的所有检查项也将同步删除，确定删除？',
    positiveText: '确定',
    negativeText: '取消',
    transformOrigin: 'center',
    onPositiveClick: async () => {
      await delInspectCategoryTree({ id: data.id, type: '1' });
      $emits('action', { action: ACTION.SEARCH });
    },
  });
}

// 创建检查类
function createCategory() {
  $emits('action', { action: ACTION.ADD });
}

// 创建检查项
function createCheckItem() {
  router.push({
    name: 'checkItemCreate',
  });
}

// 编辑检查项
function editCheckItem(data: IInspectItems) {
  router.push({
    name: 'checkItemCreate',
    query: { id: data.itemId },
  });
}

defineExpose({
  categoryList,
});

defineOptions({ name: 'CheckLibraryItems' });
</script>

<style module lang="scss">
.check-items {
  @apply px-[24px] pb-[24px] overflow-auto;
  border: 1px solid #e4e5eb;

  :global(.n-collapse-item__content-inner) {
    padding-top: 0 !important;
  }
  :global(.n-collapse .n-collapse-item) {
    margin: 0;
  }
  :global(.n-collapse .n-collapse-item .n-collapse-item__header) {
    padding-top: 0;
  }
}

.header {
  @apply h-[54px] flex items-center text-[#0249B1] font-bold;
}
.container {
  border: 1px solid #e4e5eb;
  .category {
    @apply px-[24px] h-[48px] leading-[48px] text-[#1F2225];
  }

  .extra-btn {
    @apply px-[16px];
    :first-child {
      @apply mr-[8px];
    }
  }
}
</style>
