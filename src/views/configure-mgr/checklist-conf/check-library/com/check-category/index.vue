<template>
  <div :class="$style['check-category']">
    <div :class="$style.header">检查大类</div>
    <n-scrollbar class="!h-[calc(100%-54px)] px-[6px]">
      <div
        :class="[$style['dl-item'], curId === item.id ? $style['dl-item_active'] : '']"
        v-for="item in categoryList"
        :key="item.id"
        @click="handleClick(item)"
      >
        <n-ellipsis class="flex-1 mr-[8px]">{{ item.text }}</n-ellipsis>
        <template v-if="curId === item.id">
          <img src="../../assets/edit_active.png" alt="edit" @click.stop="handleEdit(item)" />
          <img class="ml-[16px]" src="../../assets/delete_active.png" alt="delete" @click.stop="handleDelete(item)" />
        </template>
        <template v-else>
          <img src="../../assets/edit.png" alt="edit" @click.stop="handleEdit(item)" />
          <img class="ml-[16px]" src="../../assets/delete.png" alt="delete" @click.stop="handleDelete(item)" />
        </template>
      </div>
    </n-scrollbar>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { delInspectCategoryTree, queryCategoryTree } from '../../fetchData';
import { ICategoryTree } from '../../type';
import { ACTION } from '../../constant';
import { $dialog } from '@/common/shareContext/useDialogCtx.ts';

const emits = defineEmits(['action']);
const categoryList = ref<ICategoryTree[]>([]);
const curId = ref('');

function handleClick(data: ICategoryTree) {
  curId.value = data.id;
  emits('action', { action: ACTION.SET, data });
}

function handleEdit(data: ICategoryTree) {
  curId.value = data.id;
  emits('action', { action: ACTION.EDIT, data });
}

function handleDelete(data: ICategoryTree) {
  $dialog.error({
    title: '删除检查大类',
    content: '删除后该大类下的所有小类及检查项也将同步删除，确定删除？',
    positiveText: '确定',
    negativeText: '取消',
    transformOrigin: 'center',
    onPositiveClick: async () => {
      await delInspectCategoryTree({ id: data.id, type: '0' });
      getData();
    },
  });
}

function getData() {
  queryCategoryTree().then((res) => {
    categoryList.value = res.data;
    res.data[0] && handleClick(res.data[0]);
  });
}

getData();
defineExpose({
  getData,
});
defineOptions({ name: 'CheckLibraryCategory' });
</script>
<style module lang="scss">
.check-category {
  @apply w-[276px] h-full;
  border: 1px solid #e4e5eb;
}
.header {
  @apply h-[54px] px-[24px] flex items-center text-[#0249B1] font-bold;
}
.dl-item {
  @apply h-[48px] rounded flex items-center px-[16px] text-[#303133] mb-[6px] cursor-pointer;
  background: rgba(0, 0, 0, 0.04);
  &_active {
    @apply bg-[#0249B1] text-white;
  }
  img {
    @apply cursor-pointer w-[16px] h-[16px];
  }
}
</style>
