<template>
  <div :class="$style.wrap">
    <n-spin :show="loading" :delay="500">
      <n-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        label-placement="left"
        label-width="100"
        require-mark-placement="left"
      >
        <n-form-item label="创建类别：" path="type">
          <n-radio-group v-model:value="formData.type" size="medium" :disabled="isEdit">
            <n-radio value="1"> 大类 </n-radio>
            <n-radio value="2"> 小类 </n-radio>
          </n-radio-group>
        </n-form-item>
        <n-form-item label="类别名称：" path="name">
          <n-input v-model:value="formData.name" clearable maxlength="30" placeholder="请输入，不超过30个字符" />
        </n-form-item>
        <n-form-item label="所属大类：" path="parentId" v-if="formData.type === '2'">
          <n-select
            v-model:value="formData.parentId"
            :options="categoryOpt"
            label-field="name"
            value-field="id"
            :disabled="isEdit"
          />
        </n-form-item>
      </n-form>
    </n-spin>
  </div>
</template>

<script setup lang="ts">
import { computed, inject, ref, Ref } from 'vue';
import { FormInst, FormRules } from 'naive-ui';
import { queryCategoryInfoById, queryParentCategory, saveCategory, updateCategory } from '../../fetchData';
import { ACTION, IActionData, PROVIDE_KEY } from '../../constant';
import { ICategory } from '../../type';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';

const emits = defineEmits(['submitted']);
const currentAction = inject(PROVIDE_KEY.currentAction) as Ref<IActionData>; // inject
const actionData = computed(() => currentAction.value.data);
const isEdit = computed(() => currentAction.value.action === ACTION.EDIT);
const [loading, run] = useAutoLoading(false);

const formRef = ref<FormInst | null>();
const formData = ref<Partial<ICategory>>({
  name: '', // 分类名称
  parentId: null, // 上级分类编码
  type: '1', // 分类属性1-大类 2-小类
});

const rules: FormRules = {
  name: { required: true, message: '请输入' },
  parentId: { required: true, message: '请选择' },
  type: { required: true, message: '请选择' },
};
const categoryOpt = ref<ICategory[]>([]);

function getData() {
  const id = actionData.value?.id;
  if (id && isEdit.value) {
    run(queryCategoryInfoById({ id })).then((res) => {
      formData.value = res.data;
    });
  }
}

function handleSubmit() {
  formRef.value?.validate((errors) => {
    if (!errors) {
      if (isEdit.value) {
        run(updateCategory(formData.value)).then(() => {
          emits('submitted');
        });
      } else {
        run(saveCategory(formData.value)).then(() => {
          emits('submitted');
        });
      }
    }
  });
}

function getCategory() {
  queryParentCategory().then((res) => {
    categoryOpt.value = res.data;
  });
}

// init
getData();
getCategory();
defineExpose({
  handleSubmit,
});

defineOptions({ name: 'CheckLibraryEdit' });
</script>

<style module lang="scss">
.wrap {
  padding: 24px;
}
</style>
