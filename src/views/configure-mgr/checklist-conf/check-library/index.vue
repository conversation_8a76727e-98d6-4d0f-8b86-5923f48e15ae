<template>
  <div :class="$style['check-library']" class="com-container-bg">
    <check-category ref="checkCategoryRef" @action="actionFn" />
    <check-items class="flex-1 w-0" ref="checkItemsRef" @action="actionFn" />
    <!-- aside -->
    <AsideComp v-model:show="isShowAside" :title="actionLabel" @action="actionFn" />
  </div>
</template>

<script setup lang="ts">
import { computed, ref, provide, Ref } from 'vue';
import CheckItems from './com/check-items/index.vue';
import CheckCategory from './com/check-category/index.vue';
import { ICategoryTree } from './type';
import AsideComp from './com/aside/index.vue';
import { IActionData, ACTION, ACTION_LABEL, PROVIDE_KEY } from './constant';

const checkItemsRef = ref<InstanceType<typeof CheckItems>>();
const checkCategoryRef = ref<InstanceType<typeof CheckCategory>>();
const isShowAside = ref(false);
const currentAction = ref<IActionData>({ action: ACTION.ADD, data: {} });
const actionLabel = computed(() => ACTION_LABEL[currentAction.value.action]);

// provide
provide<Ref<IActionData>>(PROVIDE_KEY.currentAction, currentAction);

function actionFn(val: IActionData) {
  currentAction.value = val;
  if (val.action === ACTION.SEARCH) {
    checkCategoryRef.value?.getData();
  } else if (val.action === ACTION.SET) {
    if (checkItemsRef.value?.categoryList) {
      checkItemsRef.value.categoryList = val.data.children as ICategoryTree[];
    }
  } else if (val.action === ACTION.ADD || val.action === ACTION.EDIT) {
    isShowAside.value = true;
  }
}

defineOptions({ name: 'CheckLibraryComp' });
</script>
<style module lang="scss">
.check-library {
  @apply px-[24px] py-[20px] overflow-auto flex;
  border: 1px solid #e4e5eb;
}
</style>
