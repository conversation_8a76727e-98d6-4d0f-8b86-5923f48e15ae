<template>
  <n-data-table
    class="h-full"
    remote
    striped
    :columns="columns"
    :data="tableData"
    :bordered="false"
    :flex-height="true"
    :pagination="pagination"
    :loading="loading"
    :render-cell="useEmptyCell"
  />
</template>

<script lang="ts" setup>
import type { IPageData } from '../type';
import { ACTION, ACTION_LABEL } from '../constant';
import { cols } from './columns';
import { DataTableColumns, NButton } from 'naive-ui';
import { h, ref, toRaw, VNode, watch } from 'vue';
import { pageData } from '../fetchData';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import { useNaivePagination } from '@/common/hooks/useNaivePagination.ts';
import { IObj } from '@/types';
import { useActionDivider } from '@/common/hooks/useActionDivider.ts';
import { useEmptyCell } from '@/common/hooks/useEmptyCell.ts';

const props = defineProps({
  id: {
    type: String,
    default: '',
  },
});
const emits = defineEmits(['action']);

const [loading, search] = useAutoLoading(true);
const columns = ref<DataTableColumns>([]);
const tableData = ref<IPageData[]>([]);
const { pagination, updateTotal } = useNaivePagination(getTableData);

let filterData: IObj<any> = {}; // 搜索条件

function getTableData() {
  const params = {
    pageNo: pagination.page,
    pageSize: pagination.pageSize,
    ...filterData,
    projectManagementUnitId: props.id,
  };

  search(pageData(params)).then((res) => {
    tableData.value = res.data.rows || [];
    updateTotal(res.data.total || 0);
  });
}

function getTableDataWrap(data: IObj<any>) {
  filterData = Object.assign({}, data) || {};
  pagination.page = 1;
  getTableData();
}

function setColumns() {
  columns.value.push(...cols);

  // 添加操作栏 action
  columns.value.push({
    title: '操作',
    key: 'actions',
    width: 140,
    align: 'center',
    render(row) {
      return getActionBtn(row);
    },
  });
}

function getActionBtn(row: any) {
  const acList: [VNode, boolean?][] = [
    [
      h(
        NButton,
        {
          size: 'small',
          type: 'primary',
          ghost: true,
          class: 'com-action-button',
          onClick: () => {
            console.log('ACTION.DETAIL', ACTION.DETAIL);
            emits('action', { action: ACTION.DETAIL, data: toRaw(row) });
          },
        },
        { default: () => ACTION_LABEL.DETAIL }
      ),
    ],
  ];

  return useActionDivider(acList);
}

watch(
  () => props.id,
  (newVal: string) => {
    if (newVal) {
      getTableData();
    }
  },
  { immediate: true }
);

// on created
setColumns();

defineExpose({
  getTableDataWrap,
  getTableData,
});

defineOptions({ name: 'DemoJurisdictionTable' });
</script>

<style module lang="scss"></style>
