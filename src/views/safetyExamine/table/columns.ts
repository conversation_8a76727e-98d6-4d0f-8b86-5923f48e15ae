import { DataTableColumn } from 'naive-ui';

export const cols: DataTableColumn[] = [
  {
    title: '序号',
    key: 'index',
    align: 'center',
    width: 65,
    render: (_: any, index: number) => {
      return index + 1;
    },
  },
  {
    title: '客户名称',
    key: 'tenantryName',
    align: 'center',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '客户性质',
    key: 'tenantryNatureCodeName',
    align: 'center',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '客户负责人',
    key: 'tenantryManager',
    align: 'center',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '项目经营单位',
    key: 'projectManagementUnitName',
    align: 'center',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '项目名称',
    key: 'projectName',
    align: 'center',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '资源类型',
    key: 'resourceTypeCodeName',
    align: 'center',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '已完成检查任务数',
    key: 'finishTaskNum',
    align: 'center',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '发现隐患数',
    key: 'hazardNum',
    align: 'center',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '已整改数',
    key: 'disposeNum',
    align: 'center',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '未整改数',
    key: 'unDisposeNum',
    align: 'center',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '按期整改率',
    key: 'disposeRate',
    align: 'center',
    ellipsis: {
      tooltip: true,
    },
  },
];
