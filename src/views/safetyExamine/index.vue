<template>
  <div :class="{ [$style.videoWrap]: true }">
    <ComBread :data="breadData" />
    <div class="w-full h-full flex overflow-hidden">
      <div ref="siderRef" class="h-full bg-[#EEF7FF] rounded-[12px] mr-[16px]"
        :style="`width:${collapsed ? toVw(0) : toVw(346)};`">
        <n-layout has-sider style="height: 100%">
          <n-layout-sider bordered collapse-mode="width" :collapsed-width="0" width="95%" :collapsed="collapsed"
            @collapse="collapsed = true" @expand="collapsed = false">
            <TreeModle @treeChange="fn" />
          </n-layout-sider>
        </n-layout>
      </div>

      <div class="bg-[#EEF7FF] rounded-[12px] com-g-row-aaa1 relative"
        :style="`width: calc(100% - ${toVw(collapsed ? 15 : 346)} );`">
        <div class="com-g-row-a1 gap-y-[20px]">
          <filter-comp class="com-table-filter" @action="actionFn"></filter-comp>
          <Table class="com-table-container" ref="tableCompRef" @action="actionFn" :id="unitIds"></Table>
          <div class="img-switch" @click="collapsed = !collapsed"></div>
        </div>
      </div>
    </div>
    <AsideComp v-model:show="isShowAside" :title="actionLabel" @action="actionFn" />
  </div>
</template>
<script setup lang="ts">
import AsideComp from './aside/index.vue';
import ComBread from '@/components/breadcrumb/ComBread.vue';
import { IBreadData } from '@/components/breadcrumb/type.ts';
import filterComp from './Filter.vue';
import Table from './table/Table.vue';
import { ref, provide, Ref, watch, onMounted } from 'vue';
import { IActionData } from './type.ts';
import { ACTION, PROVIDE_KEY } from './constant.ts';
import { toVw, toVh } from '@/utils/fit';
import TreeModle from '@/views/tenantry/tenantManagement/TreeModle.vue';
import { useAuthStore } from '@/store/modules';

const breadData: IBreadData[] = [{ name: '安全合规检查' }];
const currentAction = ref<IActionData>({ action: ACTION.NONE, data: {} });
const tableCompRef = ref();
const actionLabel = '详情';
const isShowAside = ref(false);
const ui = useAuthStore();
const collapsed: any = ref(ui.collapsedFlag);
const unitIds = ref<string | undefined>(ui.userInfo.unitId);
const curTenantryId = ref('');
// provide
provide<Ref<IActionData>>(PROVIDE_KEY.currentAction, currentAction);
provide<any>(PROVIDE_KEY.tenantryId, curTenantryId);

function fn(val: string) {
  unitIds.value = val;
}
function actionFn(val: IActionData) {
  currentAction.value = val;
  console.log('val', val);
  if (val.action === ACTION.SEARCH) {
    handleSearch(val.data);
  } else {
    console.log('xiangqing--', val.data.tenantryId);
    curTenantryId.value = val.data.tenantryId;
    isShowAside.value = val.action === ACTION.DETAIL;
  }
}
function handleSearch(data?: Record<string, any>) {
  if (data) {
    tableCompRef.value?.getTableDataWrap(data);
  } else {
    tableCompRef.value?.getTableData();
  }
}

watch(
  () => collapsed.value,
  (nv) => {
    ui.collapsedFlag = nv;
  }
);
const siderRef: any = ref<HTMLElement | null>(null);
onMounted(async () => {
  // const trigger = siderRef.value.querySelector('.n-layout-toggle-button');
  // const svgPath = new URL(`@/assets/svg/ss.svg`, import.meta.url).href;
  // trigger.innerHTML = `<img  class='triggerImg' src="${svgPath}" alt="">`; // 替换为新的图标元素或类名
});

defineOptions({ name: 'safetyExamineName' });
</script>

<style module lang="scss">
.videoWrap {
  display: flex;
  flex-direction: column;

  .container {
    flex: 1;
    background: #eef7ff;
  }

  .padding {
    padding: 16px 24px;
  }
}
</style>
<style scoped lang="scss">
.w_bg {
  background-color: #dce4f4 !important;
}

.com-g-row-aaa1 {
  display: grid;

  .img-switch {
    cursor: pointer;
    width: 34px;
    height: 36px;
    position: absolute;
    left: 0;
    top: 47%;
    transform: translateX(-50%);
    background: url('/src/assets/expand.png') no-repeat;
    background-size: cover;
  }
}

:deep(.triggerImg) {
  width: 20px;
}

:deep(.n-layout-sider) {
  background-color: #eef7ff;
  max-width: 100% !important;
  width: 100% !important;
}

:deep(.n-layout .n-layout-scroll-container) {
  background-color: #eef7ff;
  border-radius: 12px;
}

:deep(.n-layout),
:deep(.n-layout-sider) {
  border-radius: 12px;
  overflow: hidden;
}

:deep(.n-layout-sider-border) {
  border-radius: 12px;
}
</style>
