<template>
  <n-form :show-feedback="false" label-placement="left">
    <n-flex :size="[20, 10]">
      <n-form-item label="资源类型:">
        <n-select
          v-model:value="filterForm.resourceTypeCode"
          :options="unitOptions"
          placeholder="请选择"
          clearable
          filterable
        />
      </n-form-item>
      <n-form-item label="搜索查询:">
        <n-input v-model:value="filterForm.searchQuery" placeholder="请输入关键词查询" />
      </n-form-item>
    </n-flex>
  </n-form>
</template>

<script lang="ts" setup>
import { onMounted, ref, watch } from 'vue';
import { trimObjNull } from '@/utils/obj.ts';
import { ACTION } from './constant.ts';
import { hazardDisposeSuperviseLevelGradeList } from '@/views/supervise/fetchData.ts';
import {
  hazardDisposeSuperviseGetOrgTree,
  hazardDisposeSuperviseQueryDictList,
} from '@/views/safetyExamine/fetchData.ts';
const emits = defineEmits(['action']);

const filterForm = ref({
  resourceTypeCode: null,
  projectManagementUnitId: '',
  searchQuery: '',
});
const unitOptions = ref([]);
const OrgTree = ref([]);

function getFilterForm() {
  return trimObjNull(filterForm.value);
}

const getListGrade = () => {
  hazardDisposeSuperviseQueryDictList({ dictType: 'zylx' }).then((res) => {
    console.log('res', res);
    unitOptions.value = res.data.map((item) => {
      return {
        label: item.paramValue,
        value: item.paramCode,
      };
    });
  });
};
const getOrgTree = () => {
  hazardDisposeSuperviseGetOrgTree({
    type: '2',
  }).then((res) => {
    OrgTree.value = res.data;
  });
};

function doHandle(action) {
  emits('action', {
    action: action,
    data: getFilterForm(),
  });
}
const handleUpdateValue = (value, option) => {
  console.log('value', value);
  console.log('option', option);
  filterForm.value.projectManagementUnitId = value;
};
getListGrade();
getOrgTree();
onMounted(() => {
  doHandle(ACTION.SEARCH);
});

watch(filterForm.value, () => {
  doHandle(ACTION.SEARCH);
});

defineOptions({ name: 'SafetyExamineFilter' });
</script>

<style module lang="scss"></style>
