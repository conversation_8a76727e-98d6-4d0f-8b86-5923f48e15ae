<template>
  <div class="wrap">
    <div class="top">
      <div class="top-left">
        <div class="top-left-title">检查任务执行情况</div>
        <div class="top-left-context">
          <div class="top-left-context-item">
            <div class="top-left-context-item-title">任务总数</div>
            <div class="top-left-context-item-number">1</div>
            <div class="top-left-context-item-context">

            </div>
          </div>
          <div class="top-left-context-item">
            <div class="top-left-context-item-title">逾期未结束任务</div>
            <div class="top-left-context-item-number">1</div>
            <div class="top-left-context-item-context">

            </div>
          </div>
          <div class="top-left-context-item">
            <div class="top-left-context-item-title">进行中任务</div>
            <div class="top-left-context-item-number">1</div>
            <div class="top-left-context-item-context">

            </div>
          </div>
          <div class="top-left-context-item">
            <n-progress type="circle" :percentage="percentage" status="default" />
          </div>
        </div>
      </div>
      <div class="top-right">
        <div class="top-right-title">检查任务执行情况</div>
        <div class="top-right-item">
          <div class="top-right-item-left">
            <div class="top-right-item-left-image"></div>
            <div class="top-right-item-left-info">
              <div class="top-right-item-left-info-title">隐患总数</div>
              <div class="top-right-item-left-info-number">1</div>
            </div>
          </div>
          <div class="top-right-item-mid">
            <div class="top-right-item-left-image"></div>
            <div class="top-right-item-left-info">
              <div class="top-right-item-left-info-title">隐患总数</div>
              <div class="top-right-item-left-info-number">1</div>
            </div>
          </div>
          <div class="top-right-item-right">
            <div class="top-right-item-left-image"></div>
            <div class="top-right-item-left-info">
              <div class="top-right-item-left-info-title">隐患总数</div>
              <div class="top-right-item-left-info-number">1</div>
            </div>
          </div>
        </div>
        <div class="top-right-item">
          <div class="top-right-item-left">
            <div class="top-right-item-left-image"></div>
            <div class="top-right-item-left-info">
              <div class="top-right-item-left-info-title">隐患总数</div>
              <div class="top-right-item-left-info-number">1</div>
            </div>
          </div>
          <div class="top-right-item-mid">
            <div class="top-right-item-left-image"></div>
            <div class="top-right-item-left-info">
              <div class="top-right-item-left-info-title">隐患总数</div>
              <div class="top-right-item-left-info-number">1</div>
            </div>
          </div>
          <div class="top-right-item-right">
            <div class="top-right-item-left-image"></div>
            <div class="top-right-item-left-info">
              <div class="top-right-item-left-info-title">隐患总数</div>
              <div class="top-right-item-left-info-number">1</div>
            </div>
          </div>
        </div>
        <div class="top-right-item">
          <div class="top-right-item-left">
            <div class="top-right-item-left-image"></div>
            <div class="top-right-item-left-info">
              <div class="top-right-item-left-info-title">隐患总数</div>
              <div class="top-right-item-left-info-number">1</div>
            </div>
          </div>
          <div class="top-right-item-mid">
            <div class="top-right-item-left-image"></div>
            <div class="top-right-item-left-info">
              <div class="top-right-item-left-info-title">隐患总数</div>
              <div class="top-right-item-left-info-number">1</div>
            </div>
          </div>
          <div class="top-right-item-right">
            <div class="top-right-item-left-image"></div>
            <div class="top-right-item-left-info">
              <div class="top-right-item-left-info-title">隐患总数</div>
              <div class="top-right-item-left-info-number">1</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <n-data-table
        :columns="columns"
        :data="data"
        :pagination="true"
        :bordered="false"
    />
  </div>
</template>

<script setup lang="ts">
import type { IActionData } from '../type';
import { computed, inject, ref, Ref ,h} from 'vue';
import { getDetail, postUpdate } from '../fetchData';
import { PROVIDE_KEY } from '../constant';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import { NButton, useMessage } from 'naive-ui'
const emits = defineEmits(['submitted']);
const currentAction = inject(PROVIDE_KEY.currentAction) as Ref<IActionData>; // inject
const actionData = computed(() => currentAction.value.data);
const [loading, run] = useAutoLoading(true);
const jurChecked = ref<string[]>([]);
const percentage = ref(10)
const columns = [
  {
    title: 'No',
    key: 'no'
  },
  {
    title: 'Title',
    key: 'title'
  },
  {
    title: 'Length',
    key: 'length'
  },
  {
    title: 'Action',
    key: 'actions',
    render(row) {
      return h(
          NButton,
          {
            strong: true,
            tertiary: true,
            size: 'small',
            onClick: () => {
              console.log(123)
            }
          },
          { default: () => 'Play' }
      )
    }
  }
]
const data= [
  { no: 3, title: 'Wonderwall', length: '4:18' },
  { no: 4, title: 'Don\'t Look Back in Anger', length: '4:48' },
  { no: 12, title: 'Champagne Supernova', length: '7:27' }
]
function getData() {
  const id = actionData.value.id;

  if (id) {
    // run(getDetail(id)).then((res) => {
    //   const jurStr = res.data?.jurisdiction || '';
    //   jurChecked.value = jurStr ? jurStr.split(',') : [];
    // });
  }
}

function handleSubmit() {
  const params = {
    id: actionData.value.id,
    jurisdiction: jurChecked.value.join(','),
  };

  run(postUpdate(params)).then(() => {
    emits('submitted');
  });
}

// init
getData();

defineExpose({
  handleSubmit,
});

defineOptions({ name: 'DemoJurisdictionEdit' });
</script>

<style scoped lang="scss">
.wrap {
  padding: 24px;
}

.checkbox-group {
  display: grid;
  grid-template-columns: 1fr 1fr;
  row-gap: 20px;
  padding: 0 10px;
  margin: 20px 0 30px;
  min-height: 107px;
}

.red {
  color: #a30014;
}
.top {
  width: 100%;
  display: flex;
  justify-content: space-between;
  .top-left {
    width: calc(60% - 20px);
    height: 380px;
    border: 1px solid #EBEEF5;
    border-radius: 10px;
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    .top-left-title {
      font-size: 16px;
      color: #222222;
      position: absolute;
      top: 20px;
      left: 20px;
    }
    .top-left-context {
      width: 100%;
      display: flex;
      justify-content: space-between;
      .top-left-context-item {
        width: 30%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        .top-left-context-item-title {
          color: #222222;
          font-size: 14px;
        }
        .top-left-context-item-number {
          font-size: 24px;
          color: #527CFF;
        }
        .top-left-context-item-context {
          width: 100px;
          height: 100px;
          background-color: red;
        }
      }
    }
  }
  .top-right {
    width: 40%;
    height: 380px;
    border: 1px solid #EBEEF5;
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    align-items: center;
    box-sizing: border-box;
    padding: 40px 20px 20px 20px;
    border-radius: 10px;
    .top-right-title {
      font-size: 16px;
      color: #222222;
      position: absolute;
      top: 20px;
      left: 20px;
    }
    .top-right-item {
      width: 100%;
      padding: 15px 0px;
      background: linear-gradient( 180deg, #FFFFFF 0%, #E5ECFF 100%);
      border-radius: 8px 8px 8px 8px;
      border: 1px solid #EBEEF5;
      display: flex;
      justify-content: space-around;
      .top-right-item-left ,.top-right-item-mid,.top-right-item-right{
        height: 48px;
        display: flex;
        .top-right-item-left-image {
          width: 48px;
          height: 48px;
          background-color: red;
        }
        .top-right-item-left-info {
          height: 48px;
          .top-right-item-left-info-title {
            height: 24px;
            line-height: 24px;
            font-size: 14px;
          }
          .top-right-item-left-info-number {
            height: 24px;
            line-height: 24px;
            font-size: 18px;
          }
        }
      }
    }
  }
}

</style>
