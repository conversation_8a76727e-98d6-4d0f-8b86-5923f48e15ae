<template>
  <ComDrawerA
      :autoFocus="false"
      :footerPaddingBottom="25"
      :maskClosable="false"
      :show-action="!isEdit"
      @handle-negative="handleClose"
      @handle-positive="handleSubmit"
      class="!w-[1400px]"
  >
<!--    <`Edit` ref="editRef"  @submitted="handleSubmitted" />-->
    <HazardAnalyze :searchShow="false" />
  </ComDrawerA>
</template>

<script lang="ts" setup>
import ComDrawerA from '@/components/drawer/ComDrawerA.vue';
// import Edit from './Edit.vue';
import HazardAnalyze from '@/views/lessee-manage/components/HazardAnalyze/index.vue'
import type { IActionData } from '../type';
import { ACTION, PROVIDE_KEY } from '../constant';
import { computed, useAttrs, inject, Ref, ref } from 'vue';

const emits = defineEmits(['action']);
const attrs = useAttrs();
const show = computed(() => !!attrs.show);
const currentAction = inject(PROVIDE_KEY.currentAction) as Ref<IActionData>; // inject
const isEdit = computed(() => currentAction.value.action === ACTION.DETAIL);
const editRef = ref();

function handleSubmit() {
  editRef.value?.handleSubmit();
}

function handleSubmitted() {
  emits('action', { action: ACTION.SEARCH });
  handleClose();
}

function handleClose() {
  emits('update:show' as any, false); // any-屏蔽组件内透传的emit类型
}

defineOptions({ name: 'DemoJurisdictionAside' });
</script>

<style module lang="scss"></style>
