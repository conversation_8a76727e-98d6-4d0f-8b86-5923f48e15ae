import type { IDetail, IPageDataRes } from './type';
import { $http } from '@tanzerfe/http';
import { api } from '@/api';
import { IObj, IPageRes } from '@/types';
import { ICategory } from '@/views/configure-mgr/checklist-conf/check-library/type.ts';
import { IHazardTask } from '@/views/lessee-manage/components/HazardAnalyze/type.ts';

// 获取分页
export function pageData(query: IObj<any>) {
  console.log('api.type.lease', api.type.lease);
  const url = api.getUrl(api.type.lease, api.name.demo.safeComplianceCheckPage);
  console.log('url==>', url);
  return $http.post<IPageRes<IHazardTask>>(url, { data: { _cfg: { showTip: true }, ...query } });
}

// 隐患数统计
export function hazardDisposeSuperviseStatistics(query: IObj<any>) {
  const url = api.getUrl(api.type.lease, api.name.demo.hazardDisposeSuperviseStatistics);
  return $http.post<IPageRes<IHazardTask>>(url, { data: { _cfg: { showTip: true }, ...query } });
}

// 等级统计
export function hazardDisposeSuperviseLevelStatistics(query: IObj<any>) {
  const url = api.getUrl(api.type.lease, api.name.demo.hazardDisposeSuperviseLevelStatistics);
  return $http.post<IPageRes<IHazardTask>>(url, { data: { _cfg: { showTip: true }, ...query } });
}

export function hazardDisposeSuperviseQueryDictList(query: IObj<any>) {
  const url = api.getUrl(api.type.lease, api.name.demo.hazardDisposeSuperviseQueryDictList, query);
  return $http.get(url, { data: { _cfg: { showTip: true } } });
}

export function hazardDisposeSuperviseGetOrgTree(query: IObj<any>) {
  const url = api.getUrl(api.type.lease, api.name.demo.hazardDisposeSuperviseGetOrgTree, query);
  return $http.get(url, { data: { _cfg: { showTip: true } } });
}
