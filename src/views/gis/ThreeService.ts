import { createApp, h, ref } from 'vue';

const base_url = window.$SYS_CFG.gisServiceURL;

export class ThreeService {
  static mapPopupList = ref<any[]>([]);

  static initThreeMap() {
    IndoorThree.init();

    // 单位平面图缓存
    window.CONST_GSCache = {
      adminCodeDicCache: new window.DicCache(50), //2D & 2.5D & 3DM
      indoorAreaDicCache: new window.DicCache(16), //2D & 2.5D & 3DM
      indoorAreaExtentDicCache: new window.DicCache(32), //2D & 2.5D & 3DM
      gridAreaDicCache: new window.DicCache(16), //2D & 2.5D & 3DM
      ovBuildAreaDicCache: new window.DicCache(16), //2D & 2.5D & 3DM
      ovUnitModelInfoDicCache: new window.DicCache(2), //2.5D & 3DM
      floorModelInfoDicCache: new window.DicCache(2), //2.5D & 3DM
      indoorArea3DMDicCache: new window.DicCache(4), //3DM
    };

    //常规线上服务配置
    window.CONST_GSOptions = {
      dbService: window.newIndoorService(base_url + '/api/v3/bw-svc-indoor-gis-service/indoorMap'),
      dbService_Record: window.newIndoorService(base_url + '/api/v3/bw-svc-indoor-gis-service/record'),
      dbService_GeoSpatial: window.newIndoorService(base_url + '/api/v3/bw-svc-indoor-gis-service/GeoSpatial'),
      dbService_Analysis: window.newIndoorService(base_url + '/api/v3/bw-svc-indoor-gis-service/indoorMapAnalysis'),
      unitUrlHeader: base_url + '/img1/floorImage',
      deviceIconUrlHeader: base_url + '/img1/deviceIcons/_v3.0',
      sky: false,
      deviceIconAlarmGifUrl: base_url + '/img1/deviceIcons/gif/alarm.gif',
      skyUrl: [
        base_url + '/img1/deviceIcons/z/sky/box_z/7/right.jpg',
        base_url + '/img1/deviceIcons/z/sky/box_z/7/left.jpg',
        base_url + '/img1/deviceIcons/z/sky/box_z/7/back.jpg',
        base_url + '/img1/deviceIcons/z/sky/box_z/7/front.jpg',
        base_url + '/img1/deviceIcons/z/sky/box_z/7/up.jpg',
        base_url + '/img1/deviceIcons/z/sky/box_z/7/down.jpg',
      ],
      ovUnitModelUrlHeader: base_url + '/img1/indoor',
      wmsURL: base_url + '/geoserver/GS/wms',
      deviceFieldNameState: 'priorityEventType', // eventType priorityEventType
      deviceFieldNameOnlineState: undefined,
      deviceStateValueConvertFun: window.CONST_Function_DeviceStateValueConvertFun_Default_3,
      gridLoad: false,

      //【3.0 使用该配置】
      videoBufferQueryVideoTypeCode: '25030000',

      // videoBufferQueryVideoTypeCode: '25030000',//【3.0 使用该配置】
      // deviceFieldNameState: 'priorityEventType', //【3.0 使用该配置】 eventType priorityEventType
      // deviceFieldNameOnlineState: undefined,//【3.0 使用该配置】
      // deviceStateValueConvertFun: CONST_Function_DeviceStateValueConvertFun_Default_3,//【3.0 使用该配置】
      deviceFieldNameX: 'mapX', //设备室内GIS模式X轴坐标字段，默认值：mapX（按实际情况填写）
      deviceFieldNameY: 'mapY', //设备室内GIS模式Y轴坐标字段，默认值：mapY（按实际情况填写）
      deviceFieldNameX0: 'mapX', //设备室内图纸模式X轴坐标字段，默认值：longitude（按实际情况填写）
      deviceFieldNameY0: 'mapY', //设备室内图纸模式Y轴坐标字段，默认值：latitude（按实际情况填写）
    };

    // 获取线上地图样式
    window.CONST_GSParams = {};
  }

  /**
   *  加载鸟瞰图
   *  indoor地图对象 unitId单位id mapType地图类型 Popup组件 subCenterCode运营中心code
   */
  static initOverView(
    indoor: any,
    unitId: string,
    subCenterCode: string,
    mapType: number,
    aerialviewImg?: string,
    cb?: any,
    popup?: any
  ) {
    // 去除所有popup
    ThreeService.mapPopupList.value.forEach((item: any) => item?.close());
    // modelType不为-1时  模型type赋值为-1
    const num = mapType || -1;
    indoor.showFloorData(
      num >= 0 ? window.IndoorMap.ViewType.OVUnitImage : num,
      unitId,
      undefined,
      undefined,
      aerialviewImg ? base_url + '/img1/floorImage/' + aerialviewImg : undefined, //鸟瞰图地址
      function (mapType: number, success: boolean) {
        // 加载模型数据
        if (mapType === window.IndoorMap.ViewType.OVUnitModel) {
          //模型请求成功
          indoor.showOVDataBuild(
            indoor.getIndoorDataState().source.unitModelInfo.map((i: any) => {
              return {
                ...i,
                mapX: i.modelInfoPointX,
                mapY: i.modelInfoPointY,
                mapZ: i.modelInfoPointZ,
              };
            }),
            'mapX',
            'mapY',
            'mapZ',
            function (data: any, markFieldNameX: string, markFieldNameY: string, markFieldNameZ: string) {
              if (
                typeof data[markFieldNameX] !== 'number' ||
                typeof data[markFieldNameY] !== 'number' ||
                typeof data[markFieldNameZ] !== 'number'
              )
                return true;
              // 设置mark的样式
              data.imageStyle = window.IndoorMap.StateType.Normal;
              if (popup) {
                ThreeService.mapPopupList.value.push(ThreeService.addPopup(indoor, data, popup));
              }
              if (cb && typeof cb === 'function') cb(data);
            },
            false,
            -2023857
          );
        } else if (mapType === window.IndoorMap.ViewType.OVUnitImage) {
          indoor.showOVDataBuildOnLoadGeoData(
            mapType,
            success,
            base_url + '/api/v3/bw-svc-enterprise-gis-service' + '/bitmap/queryDeviceNumsByUnitId',
            'subCenterCode=' + subCenterCode + '&ownerId=' + unitId,
            'POST',
            undefined,
            function (data: any, markFieldNameX: string, markFieldNameY: string, markFieldNameZ: string) {
              if (typeof data[markFieldNameX] !== 'number' || typeof data[markFieldNameY] !== 'number') return true;
              if (popup) {
                ThreeService.mapPopupList.value.push(ThreeService.addPopup(indoor, data, popup));
              }
              if (cb && typeof cb === 'function') cb(data);
            }
          );
        }
      }
    );
  }

  /**
   *  加载楼层平面图
   */
  static initPlanView(indoor: any, floorId: string, cb?: any) {
    // 去除所有popup
    // ThreeService.mapPopupList.value.forEach((item: any) => item?.close());
    if (!floorId) return;
    indoor.showFloorData(
      window.IndoorMap.ViewType.IndoorAreaVector, //数据类型
      undefined, //单位id
      undefined, //楼栋id
      floorId, //楼层id
      undefined,
      function (mapType: number, success: boolean) {
        if (cb) cb(mapType, success);
      }
    );
  }

  /**
   *  添加popup
   *  indoor地图对象 data点位数据 coordinate坐标 Popup组件
   */
  static addPopup(indoor: any, data: any, Popup: any) {
    const p = indoor.addPopup({
      element: '',
      autoPan: false,
      custom: true,
      offset: [0, -50],
      positioning: 'bottom-center',
    });
    const container = p.getDOM();
    const comp = h(Popup, {
      tplData: data,
    });
    const tplVue = createApp(comp);
    tplVue.mount(container);
    let coordinate = [];
    if (data.mapX && data.mapY) {
      coordinate = [data.mapX, data.mapY, data.mapZ];
      p.show(coordinate);
    } else {
      coordinate = new window.GISShare.SMap.Geometry.Point(data.longitude, data.latitude);
      p.show([coordinate.getX(), coordinate.getY(), 0]);
    }
    data.popupIns = p;
    return p;
  }

  /**
   * 地图聚焦中心
   */
  static focusMapCenter(indoor: any) {
    indoor.zoomToExtent();
  }
}
