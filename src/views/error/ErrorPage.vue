<template>
  <div :class="$style['page']">
    <div :class="$style['container']">
      <Empty />
      <div class="mb-[24px]">404</div>
      <div class="mb-[24px]">该页面不存在，请检查地址是否有误！</div>
      <div class="mb-[24px]">若地址正确，请联系本系统的管理员开设使用权限！</div>
      <!--      <n-button type="primary" @click="goHome">返回系统首页</n-button>-->
    </div>
  </div>
</template>

<script lang="ts" setup>
import { defineComponent } from 'vue';
import Empty from '@/components/empty/Empty.vue';

function goHome() {
  window.location.href = window.location.origin + window.location.pathname;
}

defineComponent({ name: 'ErrorPageComp' });
</script>

<style module lang="scss">
.page {
  width: 100%;
  height: 100vh;
  background: #aae0ff;
  display: grid;
  place-content: center;
}

.container {
  color: #fff;
  font-size: 20px;
  text-align: center;
}
</style>
