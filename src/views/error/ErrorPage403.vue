<template>
  <div :class="$style['page']">
    <div :class="$style['container']">
      <img src="" alt="" />
      <div class="mb-[35px]">暂无使用权限，请在平台系统开设权限！</div>
      <!--      <n-button type="primary" @click="goLogin()" size="large">返回本系统登录</n-button>-->
    </div>
  </div>
</template>

<script lang="ts" setup>
import { defineComponent } from 'vue';

function goLogin() {
  window.location.href = window.location.origin + window.location.pathname;
}

defineComponent({ name: 'ErrorPage403Comp' });
</script>

<style module lang="scss">
.page {
  width: 100%;
  height: 100vh;
  background: #aae0ff;
  display: grid;
  place-content: center;
}

.container {
  color: #111;
  font-size: 20px;
  text-align: center;
}
</style>
