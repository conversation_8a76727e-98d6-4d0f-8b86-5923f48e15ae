export const enum PROVIDE_KEY {
  currentAction = 'currentAction',
}

export const enum ACTION {
  SEARCH = 'SEARCH',
  ADD = 'ADD',
  DETAIL = 'DETAIL',
  EDIT = 'EDIT',
  DELETE = 'DELETE',
  CHECK = 'CHECK',
  UN_CHECK = 'UN_CHECK',
  EXPORT = 'EXPORT',
  // STARTORSTOP = 'STARTORSTOP',
  START = 'START',
  STOP = 'STOP',
}

export const ACTION_LABEL: { [key in ACTION]: string } = {
  [ACTION.SEARCH]: '搜索',
  [ACTION.ADD]: '新增计划',
  [ACTION.DETAIL]: '详情',
  [ACTION.EDIT]: '编辑',
  [ACTION.DELETE]: '删除',
  [ACTION.CHECK]: '选择检查项',
  [ACTION.UN_CHECK]: '取消检查项',
  [ACTION.EXPORT]: '导出',
  [ACTION.START]: '启用',
  [ACTION.STOP]: '停用',
};

// 检查类型
export const inspectType: { label: string; value: string }[] = [
  // 1:督察检查;2:专项检查;3:自查检查
  {
    label: '督察检查',
    value: '1',
  },
  {
    label: '专项检查',
    value: '2',
  },
  {
    label: '自查检查',
    value: '3',
  },
];

// 	string
// 检查状态(1:未发布;2:进行中;3:已结束;4:已停用)
export const planStatusList: { label: string; value: string }[] = [
  {
    label: '未发布',
    value: '1',
  },
  {
    label: '进行中',
    value: '2',
  },
  {
    label: '已结束',
    value: '3',
  },
  {
    label: '已停用',
    value: '4',
  },
];
