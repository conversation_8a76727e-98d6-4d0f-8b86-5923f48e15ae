<template>
  <div class="hazard-capture com-g-row-a1">
    <com-bread :data="breadData"></com-bread>
    <div class="bg-[#EEF7FF] border border-white rounded p-[20px]">
      <div class="relative">
        <Header :title="detailInfo.planName" :blueIcon="true"></Header>
        <!-- <span class="absolute right-[20px] top-[50%] translate-y-[-50%]">作业票编号: 20240820-00001</</n-descriptions>span> -->
      </div>

      <n-descriptions class="mb-4 mt-4" separator=" " label-style="color: #909399;" :column="3" label-placement="left">
        <n-descriptions-item v-for="item of basicInfo" :key="item.label" :label="item.label">
          <span v-if="item.key === 'timeStateName'" :class="{ 'text-[red]': detailInfo.timeState == 2 }">
            {{ detailInfo.timeStateName }}
          </span>
          <span v-else-if="item.key === 'planStartDate'">
            {{ detailInfo.planStartDate }} ~ {{ detailInfo.planEndDate }}
          </span>
          <span v-else-if="item.key === 'planStartTime'">
            {{ detailInfo.planStartTime }} ~ {{ detailInfo.planEndTime }}
          </span>
          <span v-else-if="item.key === 'beginTime'">{{
            detailInfo.beginTime && detailInfo.finishTime
              ? [detailInfo.beginTime, detailInfo.finishTime].filter((item) => item).join(' ~ ')
              : ''
          }}</span>
          <span v-else-if="item.key === 'frequency'">
            <frequencyType :row="detailInfo" />
          </span>

          <span v-else>{{ detailInfo[item.key] }}</span>
        </n-descriptions-item>
      </n-descriptions>
      <n-grid :x-gap="12" :y-gap="14" :cols="5" class="mb-4">
        <n-grid-item :span="3" class="com-g-row-a1">
          <Header title="隐患检查情况" :hasIcon="false"></Header>
          <div class="flex items-center mt-4">
            <n-space class="w-full" vertical>
              <n-steps>
                <n-step title="计划下发" :description="detailInfo.createTime || '--'"
                  :status="detailInfo.createTime ? 'process' : 'wait'"></n-step>
                <n-step title="任务执行" :description="detailInfo.beginTime || '--'"
                  :status="detailInfo.beginTime ? 'process' : 'wait'"></n-step>
                <n-step title="任务完成" :description="detailInfo.finishTime || '--'"
                  :status="detailInfo.finishTime ? 'process' : 'wait'"></n-step>
              </n-steps>
            </n-space>
          </div>
        </n-grid-item>
        <!-- 12470 【任务管理】督察计划勾选不需要现场打卡，则生成的任务，详情页面需去掉打卡记录模块 -->
        <!-- 只有选择现场打卡的任务才展示打卡记录 -->
        <n-grid-item :span="2">
          <template v-if="detailInfo.isNeedClock == '1'">
            <div class="relative">
              <Header title="打卡记录" :hasIcon="false"></Header>
              <span class="absolute right-[20px] top-[50%] translate-y-[-50%]">
                <n-button text @click="showRecordList"> 详情 </n-button>
              </span>
            </div>
            <div class="mt-4">
              <div class="rounded bg-white h-[80px] w-[240px] relative flex justify-center items-end">
                <span class="left-[19px] top-[15px] absolute">打卡</span>
                <span class="text-[#4081FF] text-[36px]">
                  {{ detailInfo.clockNum }}
                  <span class="text-[24px]">次</span>
                </span>
              </div>
              <!-- <div class="rounded bg-white h-[80px] w-[240px] relative flex justify-center items-end">
                <span class="left-[19px] top-[15px] absolute">规范率</span>
                <span class="text-[#4081FF] text-[36px]">100<span class="text-[24px]">%</span></span>
              </div> -->
            </div>
          </template>
        </n-grid-item>
        <n-grid-item :span="2">
          <Header title="检查人员" :blueIcon="true"></Header>
          <n-descriptions class="mt-4" label-style="color: #909399;" :column="1" label-placement="left">
            <n-descriptions-item label="检查负责人">{{ detailInfo.fzrs }}</n-descriptions-item>
            <n-descriptions-item label="检查参与人">{{ detailInfo.cyrs }}</n-descriptions-item>
          </n-descriptions>
        </n-grid-item>
        <n-grid-item :span="detailInfo.planType != '3' ? 3 : 5" class="flex flex-col">
          <Header title="检查表" :hasIcon="false"></Header>
          <div class="flex justify-between mt-4 flex-1 items-center">
            <span class="left">{{ detailInfo.checkTable }}</span>
            <!-- <n-button
              text
              @click="
                router.push({
                  name: ['', 'inspectRegionDetail', 'inspectDeviceDetail', 'inspectPointDetail'][
                    detailInfo.checkRange as number
                  ],
                  query: { id: detailInfo.checkTableId, checkAreaFormGrade: detailInfo.checkAreaFormGrade },
                })
              "
            >
              检查内容:{{ detailInfo.checkItemNum }}项目>
            </n-button> -->
          </div>
        </n-grid-item>
      </n-grid>

      <n-grid :x-gap="12" :y-gap="8" :cols="1">
        <n-grid-item>
          <Header title="隐患检查情况" :blueIcon="true"></Header>
          <div class="flex gap-[8px] mb-4 mt-4">
            <div class="w-[182px] h-[68px] bg flex flex-col">
              <n-statistic tabular-nums>
                <n-number-animation :from="0" :to="statisticsData?.total" />
              </n-statistic>

              <span>隐患数量</span>
            </div>
            <template v-if="taskState == 3">
              <div class="w-[182px] h-[68px] bg flex flex-col">
                <n-statistic tabular-nums style="--n-value-text-color: #00b578">
                  <n-number-animation :from="0" :to="statisticsData?.disposedNum" />
                </n-statistic>
                <span>已整改</span>
              </div>
              <div class="w-[182px] h-[68px] bg flex flex-col">
                <n-statistic tabular-nums style="--n-value-text-color: #fa5151">
                  <n-number-animation :from="0" :to="statisticsData?.unDisposedNum" />
                </n-statistic>
                <span>未整改</span>
              </div>
              <div class="w-[182px] h-[68px] bg flex flex-col">
                <n-statistic tabular-nums style="--n-value-text-color: #f78c10">
                  <n-number-animation :from="0" :to="statisticsData?.timeout" />
                </n-statistic>
                <span>超期数量</span>
              </div>
            </template>
          </div>
        </n-grid-item>
      </n-grid>
      <n-grid :x-gap="12" :y-gap="8" :cols="1" v-if="taskState">
        <n-grid-item>
          <Header title="隐患清单" :blueIcon="true"></Header>
          <div class="gap-y-[8px] w-full">
            <Filter ref="filterRef" class="com-table-filter" @action="actionFn" />
            <hazardListTable ref="hazardListTableRef" @showDetails="showHazardDetails" :taskState="taskState" />
          </div>
        </n-grid-item>
      </n-grid>
    </div>
    <!-- 打卡列表抽屉 -->
    <punchCardRecord ref="punchCardRecordRef" />
    <hazardDetails ref="hazardDetailsRef" :showMap="false" />
  </div>
</template>

<script lang="ts" setup>
import ComBread from '@/components/breadcrumb/ComBread.vue';
import { IBreadData } from '@/components/breadcrumb/type.ts';
import Filter from './Filter.vue';
import hazardListTable from './hazard-list-table.vue';
import punchCardRecord from './punch-card-record.vue';
import { computed, onMounted, ref } from 'vue';
import { useRouter } from 'vue-router';
import hazardDetails from './hazard-details.vue';
import Header from '@/components/header/ComHeaderB.vue';
import { getHazardPlanDetail, unfinishedHazardStatistics, getHazardStatistics } from './fetchData.ts';
import { IHazardStatistic, ACTION, IHazardTaskDetailsData } from './type.ts';
import { frequencyType } from '@/components/table-col/frequencyType.tsx';

const router = useRouter();
const route = router.currentRoute;
const filterRef = ref();
const hazardListTableRef = ref();

const breadData: IBreadData[] = [
  {
    name: '承租方管理',
    routeRaw: { name: 'lesseeManage', params: { tenantryId: route.value.params.tenantryId } },
    clickable: true,
  },
  { name: '任务详情' },
];

const punchCardRecordRef = ref();
const hazardDetailsRef = ref();
const detailInfo = ref<IHazardTaskDetailsData>({ planName: '' });
const statisticsData = ref<IHazardStatistic>({});

/** 任务状态 */
const taskState = computed(() => detailInfo.value.taskState); // 1:待开始;2:进行中;3:已完成

const showHazardDetails = (row: any) => {
  console.log(row);
  hazardDetailsRef.value.open(row);
};

const actionFn = (data: any) => {
  if (data.action === ACTION.search) {
    hazardListTableRef.value.getTableDataWrap(data.data);
  }
};

const getHazardPlanDetailFn = async () => {
  const res = await getHazardPlanDetail({ taskId: route.value.params.id });
  detailInfo.value = res.data;
};

const getHazardStatisticsFn = async () => {
  console.log('taskState.value', taskState.value);
  const fn = taskState.value != 3 ? getHazardStatistics : unfinishedHazardStatistics;
  const res = await fn({ randomCheckId: route.value.params.id });
  statisticsData.value = res.data;
  console.log(res);
};

/** 打开记录抽屉 */
const showRecordList = () => {
  punchCardRecordRef.value.open();
};

const basicInfo: { label: string; key: keyof IHazardTaskDetailsData }[] = [
  { label: '检查类型:', key: 'planTypeName' },
  { label: '创建人:', key: 'createByName' },
  { label: '创建时间:', key: 'createTime' },
  { label: '检查频次:', key: 'frequency' },
  { label: '计划起止时间:', key: 'planStartDate' },
  { label: '任务起止时间:', key: 'beginTime' },
  // { label: '分管单位:', key: 'superviseUnitName' },
  { label: '检查对象:', key: 'unitNames' },
  { label: '任务时效:', key: 'timeStateName' },
];

onMounted(async () => {
  console.log(route.value.params.tenantryId, '000000000000000000000000000000000');
  console.log(route.value.params.id, '002222222222000000000');
  await getHazardPlanDetailFn();
  getHazardStatisticsFn();
});

defineOptions({ name: 'TaskDetail' });
</script>

<style scoped>
.title {
  font-weight: bold;
  font-size: 1.5em;
  margin-bottom: 1em;
}

.hazard-capture :deep(.n-data-table) {
  --n-merged-th-color: #bbccf3;
}

.bg {
  background-image: url('@/assets/yhqktj-bj.png');
  background-size: cover;
  padding-left: 10px;
}
</style>
