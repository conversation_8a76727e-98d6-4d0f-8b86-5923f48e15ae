<template>
  <n-form :show-feedback="false" label-placement="left">
    <n-grid :x-gap="12" :y-gap="8" :cols="4">
      <!-- <n-grid-item>
        <n-form-item label="检查对象:">
          <n-select v-model:value="filterForm.unitId" placeholder="请选择" :options="unitOptions" clearable />
        </n-form-item>
      </n-grid-item> -->
      <n-grid-item>
        <n-form-item>
          <n-input placeholder="请输入检查对象/隐患描述模糊搜索" v-model:value="filterForm.likeFieldValue" clearable>
            <template #suffix>
              <BsSearch />
            </template>
          </n-input>
        </n-form-item>
      </n-grid-item>
    </n-grid>
  </n-form>
</template>

<script setup lang="ts">
import { onMounted, ref, watch } from 'vue';
import { BsSearch } from '@kalimahapps/vue-icons';
import { trimObjNull } from '@/utils/obj.ts';
// import { SelectOption } from 'naive-ui';
// import { getAllUnit } from './fetchData.ts';
import { useStore } from '@/store/index.ts';
import { ACTION } from './type.ts';

const { userInfo } = useStore();
const emits = defineEmits(['action']);
// const unitOptions = ref<SelectOption[]>([]);

const filterForm = ref<{ likeFieldValue: string; likeFields: string }>({
  // unitId: null,
  likeFieldValue: '',
  likeFields: 'unitId,hazardDesc',
});

function doHandle(action: ACTION) {
  emits('action', {
    action: action,
    data: trimObjNull(filterForm.value),
  });
}

watch(filterForm.value, () => {
  console.log(filterForm.value, 'filterForm');
  doHandle(ACTION.search);
});

onMounted(() => {
  doHandle(ACTION.search);
  // getAllUnit({ pageSize: -1, orgCode: userInfo.orgCode }).then((res) => {
  // getAllUnit({ pageSize: -1 }).then((res) => {
  //   console.log('getAllUnit res', JSON.parse(JSON.stringify(res)));
  //   unitOptions.value = res.data.rows.map((item: any) => ({ label: item.unitName, value: item.id }));
  // });
});

const resetField = () => {
  // filterForm.value.unitId = null;
  filterForm.value.likeFieldValue = '';
};

defineExpose({ resetField });
defineOptions({ name: 'checkTempFilterComp' });
</script>
<style module lang="scss"></style>
