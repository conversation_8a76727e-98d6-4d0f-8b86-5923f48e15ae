import { disposeState } from '@/components/table-col/disposeState.tsx';
import { DataTableColumn } from 'naive-ui';
import { h } from 'vue';

export const cols: DataTableColumn[] = [
  {
    title: '检查对象',
    key: 'unitName',
    align: 'left',
    width: 150,
  },
  {
    title: '隐患位置',
    key: 'hazardPosition',
    align: 'left',
    width: 150,
  },
  {
    title: '隐患描述',
    key: 'hazardDesc',
    align: 'left',
    width: 150,
  },
  {
    title: '隐患类别',
    key: 'hazardTypeName',
    align: 'left',
    width: 120,
  },
  {
    title: '隐患级别',
    key: 'hazardLevelName',
    align: 'left',
    width: 100,
  },
  {
    title: '检查时间',
    key: 'createTime',
    align: 'left',
    width: 200,
  },
  {
    title: '整改状态',
    key: 'disposeStateName',
    align: 'left',
    width: 120,
    render: (row) => h(disposeState, { row }),
  },
  {
    title: '是否超期',
    width: 150,
    key: 'timeoutDays',
    align: 'left',
    render: (row: any) => {
      if (row.timeoutDays == 0) return '否';
      if (row.timeoutDays) return `超期${row.timeoutDays}天`;
    },
  },
];
