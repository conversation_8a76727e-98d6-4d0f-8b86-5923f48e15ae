<template>
  <div class="gap-y-[20px] grid">
    <div class="border border-gray-300 rounded-[4px] p-[20px]">
      <h1 class="title">隐患基本信息</h1>
      <n-grid :x-gap="12" :y-gap="8" :cols="1">
        <n-grid-item v-for="item of basicInfo" :key="item.label" :span="item.span" class="flex">
          <span class="whitespace-nowrap">{{ item.label }}&nbsp; </span>
          <span>{{ detailsData[item.key as keyof IHazardInfo] }}</span>
          <span class="relative" v-if="item.key === 'hazardPosition' && showMap">
            <viewLocation
              :deviceList="deviceList"
              :floorData="floorData"
              class="absolute translate-y-[-6px] translate-x-[6px]"
            />
          </span>
        </n-grid-item>
      </n-grid>
    </div>
    <div class="border border-gray-300 rounded-[4px] p-[20px]">
      <h1 class="title">隐患详细信息</h1>
      <n-grid :x-gap="12" :y-gap="8" :cols="1">
        <n-grid-item v-for="item of detailedInfo" :key="item.label" :span="item.span" class="flex">
          <span class="whitespace-nowrap">{{ item.label }}&nbsp; </span>
          <template v-if="item.type === 'image'">
            <n-image
              v-for="img of detailsData.files"
              :key="img.fileUrl"
              style="height: 60px; margin-right: 10px"
              :src="getFileURL(img.fileUrl)"
            />
          </template>
          <span v-else>{{ detailsData[item.key as keyof IHazardInfo] }}</span>
        </n-grid-item>
      </n-grid>
    </div>
    <div class="border border-gray-300 rounded-[4px] p-[20px]">
      <h1 class="title">检查信息</h1>
      <n-grid :x-gap="12" :y-gap="8" :cols="1">
        <n-grid-item v-for="item of inspectionInfo" :key="item.label" :span="item.span" class="flex">
          <span class="whitespace-nowrap">{{ item.label }}&nbsp; </span>
          <span>{{ detailsData[item.key as keyof IHazardInfo] }}</span>
        </n-grid-item>
      </n-grid>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { IHazardInfo, IDetailsItem, IMapFloorInfo } from './type.ts';
import ViewLocation from './view-location.vue';

interface Props {
  detailsData: IHazardInfo;
  showMap: boolean;
}
const props = withDefaults(defineProps<Props>(), {});

const floorData = computed<IMapFloorInfo>(() => ({
  unitId: props.detailsData.unitId,
  buildingId: props.detailsData.buildingId,
  floorId: props.detailsData.floorId,
  floorAreaImg: props.detailsData.floorAreaImg,
}));
const deviceList = computed(() => [
  {
    mapX: props.detailsData.mapX,
    mapY: props.detailsData.mapY,
    mapZ: props.detailsData.mapZ,
  },
]);

const basicInfo: IDetailsItem[] = [
  { label: '分管单位:', key: 'superviseUnitName' },
  { label: '隐患单位:', key: 'unitName' },
  { label: '隐患位置:', key: 'hazardPosition' },
  { label: '隐患来源:', key: 'hazardSource' },
  { label: '上报时间:', key: 'eventTime' },
];

const detailedInfo: IDetailsItem[] = [
  { label: '隐患描述:', key: 'hazardDesc', span: 2 },
  { label: '隐患类别:', key: 'hazardTypeName', span: 1 },
  { label: '隐患等级:', key: 'hazardLevelName', span: 1 },
  { label: '隐患图片:', key: 'files', type: 'image', span: 1 },
];

const inspectionInfo: IDetailsItem[] = [
  { label: '设备类型:', key: 'aa' },
  { label: '系统类型:', key: 'aa' },
  { label: '品牌型号:', key: 'aa' },
  { label: 'IMEI:', key: 'aa' },
  { label: '安装日期:', key: 'aa' },
];

function getFileURL(filePath: string) {
  return window.$SYS_CFG.apiImageURL + filePath;
}
</script>

<style scoped>
.label {
  width: 100px;
  flex-shrink: 0;
}
.title {
  font-weight: bold;
  font-size: 1.5em;
  margin-bottom: 1em;
}
</style>
