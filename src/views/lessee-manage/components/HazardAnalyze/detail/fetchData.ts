import { IObj } from '@/types';
import { api } from '@/api';
import { $http } from '@tanzerfe/http';

// const actionURL = api.getUrl(api.type.file, api.name.file.uploadFile);

/** 隐患整改记录 */
export function getDispsoseNodeRecord(params: any) {
  const url = api.getUrl(api.type.lease, api.ww.getDispsoseNodeRecord);
  return $http.post<any>(url, { data: { _cfg: { showTip: true }, ...params } });
}

/** 隐患详情 */
export function qeuryEventDetail(params: IObj<any>) {
  const url = api.getUrl(api.type.lease, api.ww.qeuryEventDetail, params);
  return $http.post<any>(url, { data: { _cfg: { showTip: true } } });
}

// ================================== 隐患信息为完成数据接口 ==================================
/** 隐患治理未完成列表 */
export function unfinishedGetHazardPageList(params: IObj<any>) {
  const url = api.getUrl(api.type.lease, api.ww.unfinishedGetHazardPageList);
  return $http.post<any>(url, { data: { _cfg: { showTip: true }, ...params } });
}

/** 隐患治理已完成列表 */
export function getHazardPageList(params: IObj<any>) {
  const url = api.getUrl(api.type.lease, api.ww.hazardPlanTaskEventList, params);
  return $http.post<any>(url, { data: { _cfg: { showTip: true }, ...params } });
}

// export function exportTaskListUrl() {
//   const url = api.getUrl(api.type.lease, api.taskManagement.exportTaskList);
//   return url;
// }

export function getHazardPlanDetail(query: IObj<any>) {
  const url = api.getUrl(api.type.lease, api.ww.getHazardPlanDetail, query);
  return $http.post<any>(url, { data: { _cfg: { showTip: true } } });
}

/** 获取打卡列表 */
export function getTaskClockInList(query: IObj<any>) {
  const url = api.getUrl(api.type.lease, api.ww.getTaskClockInList, query);
  return $http.post<any[]>(url, { data: { _cfg: { showTip: true } } });
}

/** 隐患治理未完成统计数据 */
export function unfinishedHazardStatistics(params: IObj<any>) {
  const url = api.getUrl(api.type.lease, api.ww.unfinishedHazardStatistics);
  return $http.post<any>(url, { data: { _cfg: { showTip: true }, ...params } });
}
/** 隐患治理已完成统计数据 */
export function getHazardStatistics(params: IObj<any>) {
  const url = api.getUrl(api.type.lease, api.ww.getHazardStatistics, params);
  return $http.post<any>(url, { data: { _cfg: { showTip: true }, ...params } });
}

// 获取单位
export function getAllUnit(params: IObj<any>) {
  const url = api.getUrl(api.type.lease, api.ww.getAllUnit);
  return $http.post<any>(url, { data: { _cfg: { showTip: true }, ...params } });
}
