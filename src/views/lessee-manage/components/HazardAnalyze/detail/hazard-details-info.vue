<template>
  <div class="gap-y-[20px] grid">
    <div class="border border-gray-300 rounded-[4px] p-[20px]">
      <h1 class="title">隐患基本信息</h1>
      <n-grid :x-gap="12" :y-gap="8" :cols="1">
        <n-grid-item v-for="item of basicInfo" :key="item.label" :span="item.span">
          <span class="whitespace-nowrap">{{ item.label }}&nbsp; </span>
          <span>{{ detailsData[item.key as keyof IHazardInfo] }}</span>
          <span class="relative" v-if="item.type === 'map' && showMap">
            <viewLocation
              :deviceList="deviceList"
              :floorData="floorData"
              class="absolute translate-y-[-6px] translate-x-[6px]"
            />
          </span>
        </n-grid-item>
      </n-grid>
    </div>
    <div class="border border-gray-300 rounded-[4px] p-[20px]">
      <h1 class="title">隐患详细信息</h1>
      <n-grid :x-gap="12" :y-gap="8" :cols="1">
        <n-grid-item v-for="item of detailedInfo" :key="item.label" :span="item.span" class="flex">
          <span class="whitespace-nowrap">{{ item.label }}&nbsp; </span>
          <template v-if="item.type === 'image'">
            <n-image
              v-for="img of detailsData.files"
              :key="img.fileUrl"
              style="height: 60px; margin-right: 10px"
              :src="getFileURL(img.fileUrl)"
            />
          </template>
          <span v-else>{{ detailsData[item.key as keyof IHazardInfo] }}</span>
        </n-grid-item>
      </n-grid>
    </div>
    <div class="border border-gray-300 rounded-[4px] p-[20px]">
      <h1 class="title">检查信息</h1>
      <n-grid :x-gap="12" :y-gap="8" :cols="1">
        <n-grid-item v-for="item of inspectionInfo" :key="item.label" :span="item.span" class="flex">
          <span class="whitespace-nowrap">{{ item.label }}&nbsp; </span>
          <template v-if="item.type === 'image'">
            <n-image
              v-for="img of detailsData.classItemVo?.files"
              :key="img.fileUrl"
              style="height: 60px; margin-right: 10px"
              :src="getFileURL(img.fileUrl)"
            />
          </template>
          <span v-else>{{
            detailsData.classItemVo && detailsData.classItemVo[item.key as keyof IHazardInfo['classItemVo']]
          }}</span>
        </n-grid-item>
      </n-grid>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';
import viewLocation from './view-location.vue';
import { IDetailsItem, IHazardInfo, IMapFloorInfo } from './type.ts';

interface Props {
  detailsData: IHazardInfo;
  showMap: boolean;
}
const props = withDefaults(defineProps<Props>(), {});

const floorData = computed<IMapFloorInfo>(() => ({
  unitId: props.detailsData.unitId,
  buildingId: props.detailsData.buildingId,
  floorId: props.detailsData.floorId,
  floorAreaImg: props.detailsData.floorAreaImg,
}));
const deviceList = computed(() => [
  {
    mapX: props.detailsData.mapX,
    mapY: props.detailsData.mapY,
    mapZ: props.detailsData.mapZ,
  },
]);
function getFileURL(filePath: string) {
  // return window.$SYS_CFG.apiServiceFileURL + filePath;
  return window.$SYS_CFG.apiImageURL + filePath;
}

const basicInfo: IDetailsItem[] = [
  { label: '分管单位:', key: 'superviseUnitName', span: 1 },
  { label: '隐患单位:', key: 'unitName', span: 1 },
  { label: '隐患整改人员:', key: 'reformUser', span: 1 },
  { label: '隐患来源:', key: 'hazardSourceName', span: 1 },
  { label: '隐患位置:', key: 'hazardPosition', type: 'map', span: 2 },
  { label: '上报时间:', key: 'eventTime', span: 1 },
  { label: '检查时间:', key: 'createTime', span: 1 },
];

const detailedInfo: IDetailsItem[] = [
  { label: '隐患描述:', key: 'hazardDesc', span: 2 },
  { label: '隐患类别:', key: 'hazardTypeName', span: 1 },
  { label: '隐患等级:', key: 'hazardLevelName', span: 1 },
  { label: '备注:', key: 'remark', span: 1 },
  { label: '隐患图片:', key: 'files', type: 'image', span: 1 },
];

const inspectionInfo: IDetailsItem[] = [
  { label: '检查内容:', key: 'inspectionItem' },
  { label: '检查详情:', key: 'inspectionDescribe' },
  { label: '法规依据:', key: 'inspectionItemBasis' },
  { label: '合规要求:', key: 'inspectionAsk' },
  { label: '法律原文:', key: 'legalText' },
  { label: '法律责任:', key: 'legalLiability' },
  { label: '图例:', key: 'files', type: 'image' },
];
</script>

<style scoped>
.label {
  width: 100px;
  flex-shrink: 0;
}
.title {
  font-weight: bold;
  font-size: 1.5em;
  margin-bottom: 1em;
}
</style>
