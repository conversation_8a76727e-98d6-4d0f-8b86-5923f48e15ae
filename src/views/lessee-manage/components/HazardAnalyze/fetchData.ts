import { IClockData, IHazardTask, IHazardTaskDetailsData } from './type.ts';
import { IObj, IPageRes } from '@/types';
import { api } from '@/api';
import { $http } from '@tanzerfe/http';

// const actionURL = api.getUrl(api.type.file, api.name.file.uploadFile);

/** 隐患任务列表 */
export function getPlanTaskPageList(query: IObj<any>) {
  const url = api.getUrl(api.type.lease, api.ww.getPlanTaskPageList);
  return $http.post<IPageRes<IHazardTask>>(url, { data: { _cfg: { showTip: true }, ...query } });
}
export function exportTaskListUrl() {
  const url = api.getUrl(api.type.lease, api.ww.exportTaskList);
  return url;
}

export function getHazardPlanDetail(query: IObj<any>) {
  const url = api.getUrl(api.type.lease, api.ww.getHazardPlanDetail, query);
  return $http.post<IHazardTaskDetailsData>(url, { data: { _cfg: { showTip: true } } });
}

/** 获取打卡列表 */
export function getTaskClockInList(query: IObj<any>) {
  const url = api.getUrl(api.type.lease, api.ww.getTaskClockInList, query);
  return $http.post<IClockData[]>(url, { data: { _cfg: { showTip: true } } });
}

/** 隐患分析-检查任务执行情况 */
export function getCheckTaskExecuteCondition(query: IObj<any>) {
  const url = api.getUrl(api.type.lease, api.ww.getCheckTaskExecuteCondition, query);
  return $http.post<IClockData[]>(url, { data: { _cfg: { showTip: true } } });
}

/** 隐患分析-隐患治理情况*/
export function getHazardEssentialFactorCondition(query: IObj<any>) {
  const url = api.getUrl(api.type.lease, api.ww.getHazardEssentialFactorCondition, query);
  return $http.post<IClockData[]>(url, { data: { _cfg: { showTip: true } } });
}
