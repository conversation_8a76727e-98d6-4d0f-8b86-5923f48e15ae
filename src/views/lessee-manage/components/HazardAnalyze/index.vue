<template>
  <div>
    <div class="flex justify-between items-center">
      <!-- 检查任务执行情况 -->
      <div class="execute-content com-container-bg">
        <div class="title">检查任务执行情况</div>
        <div class="flex justify-between items-center container">
          <div>
            <div class="flex justify-center items-center">任务总数</div>
            <div class="flex justify-center items-center text-24" style="color: #527cff">
              {{ checkTaskExecuteCondition.totalTaskCount || '--' }}
            </div>
            <img src="./img/img1.png" style="width: 142px" alt="" />
          </div>
          <div>
            <div class="flex justify-center items-center">已逾期任务</div>
            <div class="flex justify-center items-center text-24" style="color: #f72929">
              {{ checkTaskExecuteCondition.expectedUnfinishedTaskCount || '--' }}
            </div>
            <img src="./img/img2.png" style="width: 142px" alt="" />
          </div>
          <div>
            <div class="flex justify-center items-center">进行中任务</div>
            <div class="flex justify-center items-center text-24" style="color: #00b578">
              {{ checkTaskExecuteCondition.inProgressTaskCount || '--' }}
            </div>
            <img src="./img/img3.png" style="width: 142px" alt="" />
          </div>
          <div>
            <div class="flex justify-center items-center p-1.5 bg-white rounded-full"
              style="box-shadow: 0px 0px 6px 0px rgba(0, 0, 0, 0.25)">
              <n-progress type="circle" :percentage="checkTaskExecuteCondition.taskCompletionRate || 0"
                color="#1d91f4" />
            </div>

            <div class="flex justify-center items-center mt-3">任务完成率</div>
          </div>
        </div>
      </div>
      <!-- 隐患治理情况 -->
      <div class="govern-content com-container-bg">
        <div class="title">隐患治理情况</div>
        <div class="container overflow-hidden" style="padding: 21px 24px">
          <div class="flex justify-between items-center govern-line1">
            <div class="flex justify-start items-center">
              <img src="./img/img4.png" style="width: 66px" alt="" />
              <div class="flex justify-between items-start flex-col ml-2">
                <div>隐患总数</div>
                <div class="text-18" style="color: #ff9500">
                  <div v-if="hazardEssentialFactorCondition.length > 0">
                    {{ hazardEssentialFactorCondition[0].totalCount || '--' }}
                  </div>
                  <div v-else>--</div>
                </div>
              </div>
            </div>
            <div class="flex justify-start items-center">
              <img src="./img/img5.png" style="width: 66px" alt="" />
              <div class="flex justify-between items-start flex-col ml-2">
                <div>已整改</div>
                <div class="text-18" style="color: #00b578">
                  <div v-if="hazardEssentialFactorCondition.length > 0">
                    {{ hazardEssentialFactorCondition[0].rectifiedCount || '--' }}
                  </div>
                  <div v-else>--</div>
                </div>
              </div>
            </div>
            <div class="flex justify-start items-center">
              <div class="flex justify-center items-center p-1.5 bg-white rounded-full"
                style="box-shadow: 0px 0px 6px 0px rgba(0, 0, 0, 0.25); width: 57px; height: 57px">
                <n-progress type="circle" v-if="hazardEssentialFactorCondition.length > 0"
                  :percentage="hazardEssentialFactorCondition[0].rectifiedRate" color="#1d91f4"
                  :show-indicator="false" />
                <n-progress type="circle" v-else :percentage="0" color="#1d91f4" :show-indicator="false" />
              </div>
              <div class="flex justify-between items-start flex-col ml-4">
                <div>整改率</div>
                <div class="text-18">
                  <div v-if="hazardEssentialFactorCondition.length > 0">
                    {{ hazardEssentialFactorCondition[0].rectifiedRate + '%' || '--' }}
                  </div>
                  <div v-else>--</div>
                </div>
              </div>
            </div>
          </div>
          <div class="mt-3" v-if="hazardEssentialFactorCondition.length > 1">
            <n-grid :x-gap="12" :y-gap="12" :cols="2">
              <n-grid-item v-for="(item, idx) in hazardEssentialFactorCondition.slice(1)" :key="idx"
                class="flex justify-between items-center govern-line2" style="width: 100%">
                <div>
                  <div>{{ item.gradeName }}</div>
                  <div class="text-18" style="color: #ff9500">{{ item.totalCount || '--' }}</div>
                </div>
                <div>
                  <div>已整改</div>
                  <div class="text-18" style="color: #00b578">{{ item.rectifiedCount || '--' }}</div>
                </div>
                <div>
                  <div>整改率</div>
                  <div class="text-18">{{ item.rectifiedRate + '%' || '--' }}</div>
                </div>
              </n-grid-item>
            </n-grid>
          </div>
        </div>
      </div>
    </div>
    <div class="com-g-row-a1 h-full mt-5 gap-y-[20px] com-table-container">
      <Filter @action="actionFn" v-show="props.searchShow" />
      <TaskTable ref="tableCompRef" @action="actionFn" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { IActionData } from './type';
import { ref, inject, Ref } from 'vue';
import { IObj } from '@/types';
import Filter from './Filter.vue';
import TaskTable from './Table.vue';
import { getCheckTaskExecuteCondition, getHazardEssentialFactorCondition } from './fetchData.ts';
import { PROVIDE_KEY } from '@/views/safetyExamine/constant.ts';
import { useRoute } from 'vue-router';
interface Props {
  searchShow: boolean;
}
const props = withDefaults(defineProps<Props>(), {
  searchShow: true,
});

const route = useRoute();
const tableCompRef = ref();
const checkTaskExecuteCondition = ref({
  totalTaskCount: 0,
  expectedUnfinishedTaskCount: 0,
  inProgressTaskCount: 0,
  taskCompletionRate: 0,
});
const hazardEssentialFactorCondition = ref<any>([{ totalCount: 0, rectifiedCount: 0, rectifiedRate: 0 }]);
const curTenantryId = inject(PROVIDE_KEY.tenantryId); // inject
const tenantryId = ref(route.params.tenantryId || curTenantryId?.value);

function handleSearch(data?: IObj<any>) {
  if (data) {
    tableCompRef.value?.getTableDataWrap(data);
  } else {
    tableCompRef.value?.getTableData();
  }
}

function getData() {
  console.log(tenantryId.value, 'curTenantryId-----------');
  getCheckTaskExecuteCondition({ tenantryId: tenantryId.value }).then((res: any) => {
    checkTaskExecuteCondition.value = res.data || [];
  });
  getHazardEssentialFactorCondition({ tenantryId: tenantryId.value }).then((res: any) => {
    hazardEssentialFactorCondition.value = res.data || [];
  });
}
function actionFn(val: IActionData) {
  return handleSearch(val.data);
}

getData();

defineOptions({ name: 'HazardAnalyze' });
</script>

<style scoped lang="scss">
.execute-content,
.govern-content {
  width: 49%;
  border-radius: 9px;
  padding: 1px;
  max-height: 300px;

  .title {
    padding: 15px 25px;
    background-color: #dce4f4;
    font-size: 16px;
    border-radius: 9px 9px 0px 0px;
    font-weight: 500;
  }

  .container {
    height: 215px;
    padding: 22px 48px 28px;

    .govern-line1 {
      padding: 15px 58px;
      background: linear-gradient(180deg, #ffffff 0%, #e5ecff 100%);
      box-shadow: 0px 2px 0px 0px rgba(0, 20, 82, 0.18);
      border-radius: 8px 8px 8px 8px;
    }

    .govern-line2,
    .govern-line3 {
      padding: 15px 32px;
      width: 49%;
      background: linear-gradient(180deg, #ffffff 0%, #e5ecff 100%);
      box-shadow: 0px 2px 0px 0px rgba(0, 20, 82, 0.18);
      border-radius: 8px 8px 8px 8px;
    }
  }

  // 超出隐藏
  .overflow-hidden {
    overflow-y: scroll;
  }
}

.text-24 {
  font-size: 24px;
}

.text-18 {
  font-size: 18px;
}
</style>
