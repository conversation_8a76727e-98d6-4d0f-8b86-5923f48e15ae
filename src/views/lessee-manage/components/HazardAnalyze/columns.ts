import { DataTableColumn } from 'naive-ui';
import { h } from 'vue';
import { taskState } from '@/components/table-col/taskState';
export const cols: DataTableColumn[] = [
  {
    title: '计划名称',
    key: 'planName',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '检查类型',
    key: 'planTypeName',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '任务起止时间',
    key: 'planEndTime',
    align: 'left',
    width: 350,
    ellipsis: {
      tooltip: true,
    },
    render(row) {
      return `${row.planStartTime} - ${row.planEndTime}`;
    },
  },
  {
    title: '任务状态',
    key: 'taskStateName',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
    render(row: any) {
      return h(taskState, { row });
    },
  },
  {
    title: '任务时效',
    key: 'timeStateName',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
    render(row: any) {
      return h(
        'span',
        {
          text: true,
          style: `color: ${row.timeState == '1' ? '#000' : 'red'}`,
        },
        { default: () => row.timeStateName }
      );
    },
  },
  {
    title: '创建时间',
    key: 'createTime',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '计划创建人',
    key: 'createByName',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
  },
];
