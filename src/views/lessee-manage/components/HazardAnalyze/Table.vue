<template>
  <n-data-table class="com-table" style="height: 500px" remote striped :columns="columns" :data="tableData"
    :bordered="false" :flex-height="true" :loading="loading" :pagination="pagination" :render-cell="useEmptyCell" />
</template>

<script setup lang="ts">
import { h, inject, ref, VNode } from 'vue';
import { DataTableColumns, NButton } from 'naive-ui';
import { cols } from './columns';
import { useRouter, useRoute } from 'vue-router';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import { useNaivePagination } from '@/common/hooks/useNaivePagination.ts';
import { useActionDivider } from '@/common/hooks/useActionDivider.ts';
import { IObj } from '@/types';
import { useEmptyCell } from '@/common/hooks/useEmptyCell.ts';
import { getPlanTaskPageList } from './fetchData';
import { PROVIDE_KEY } from '@/views/safetyExamine/constant.ts';

const router = useRouter();
const route = useRoute();
const emits = defineEmits(['action', 'showDetails']);
const tableData = ref<any[]>();
const curTenantryId = inject(PROVIDE_KEY.tenantryId); // inject
const tenantryId = ref(route.params.tenantryId || curTenantryId?.value);

function getTableData() {
  const params = {
    pageNo: pagination.page,
    pageSize: pagination.pageSize,
    ...filterData,
    tenantryId: tenantryId.value,
  };

  search(getPlanTaskPageList(params)).then((res: any) => {
    tableData.value = res.data.rows || [];
    updateTotal(res.data.total || 0);
  });
  // tableData.value = [
  //   {
  //     taskId: '05d0741f92db4d54a3199ebc279b4fc5',
  //     unitIds: '89b8cd9cd00042acb83c9413a68f285d',
  //     unitNames: '7号站',
  //     taskName: '每月重复逐一检查1个负责人拍照',
  //     planId: 'e63f32641779436a8ae6a965a6d86e6b',
  //     planName: '每月重复逐一检查1个负责人拍照',
  //     planType: '1',
  //     planTypeName: '督察检查',
  //     taskState: '2',
  //     taskStateName: '进行中',
  //     frequency: '1',
  //     frequencyType: '3',
  //     planStartTime: '2024-11-01 00:00:00',
  //     planEndTime: '2024-11-30 23:59:59',
  //     beginTime: '2024-10-08 10:41:06',
  //     finishTime: null,
  //     checkRange: '1',
  //     createByName: '系统管理员',
  //     createTime: '2024-10-08 10:30:07',
  //     checkTableSet: '',
  //     checkTableId: '1839999875770839042',
  //     checkTableName: '对区域内生产单位安全生产检查',
  //     checkExecuteMethod: null,
  //     checkDemand: '',
  //     checkEndOperate: '1',
  //     timeState: '2',
  //     timeStateName: '逾期',
  //     startEndTime: '2024-11-01 00:00:00~2024-11-30 23:59:59',
  //   },
  //   {
  //     taskId: '025b55e643c540d6bed85c7ef98ee4de',
  //     unitIds: '063deb9bf59a41eb98fae8c58949eb09',
  //     unitNames: '延919采气大队',
  //     taskName: '人工智能巡检计划1',
  //     planId: '7fbebc2a0afc4edd95a70718f9d04e6d',
  //     planName: '人工智能巡检计划1',
  //     planType: '3',
  //     planTypeName: '自行检查',
  //     taskState: '3',
  //     taskStateName: '已完成',
  //     frequency: '3',
  //     frequencyType: '0',
  //     planStartTime: '2024-10-11 21:00:00',
  //     planEndTime: '2024-10-11 23:59:59',
  //     beginTime: '2024-10-09 17:21:07',
  //     finishTime: null,
  //     checkRange: '3',
  //     createByName: '系统管理员',
  //     createTime: '2024-10-09 10:43:24',
  //     checkTableSet: '',
  //     checkTableId: '1839195140960624642',
  //     checkTableName: '延长石油点位检查表',
  //     checkExecuteMethod: null,
  //     checkDemand: '必须按照位置点位逐一检查。',
  //     checkEndOperate: '2',
  //     timeState: '1',
  //     timeStateName: '正常',
  //     startEndTime: '2024-10-11 21:00:00~2024-10-11 23:59:59',
  //   },
  //   {
  //     taskId: '2b740d24aed544269c7fed06cf3b3a46',
  //     unitIds: '063deb9bf59a41eb98fae8c58949eb09',
  //     unitNames: '延919采气大队',
  //     taskName: '人工智能巡检计划1',
  //     planId: '7fbebc2a0afc4edd95a70718f9d04e6d',
  //     planName: '人工智能巡检计划1',
  //     planType: '3',
  //     planTypeName: '自行检查',
  //     taskState: '3',
  //     taskStateName: '已完成',
  //     frequency: '3',
  //     frequencyType: '0',
  //     planStartTime: '2024-10-11 18:00:00',
  //     planEndTime: '2024-10-11 23:59:59',
  //     beginTime: '2024-10-09 17:21:17',
  //     finishTime: null,
  //     checkRange: '3',
  //     createByName: '系统管理员',
  //     createTime: '2024-10-09 10:43:24',
  //     checkTableSet: '',
  //     checkTableId: '1839195140960624642',
  //     checkTableName: '延长石油点位检查表',
  //     checkExecuteMethod: null,
  //     checkDemand: '必须按照位置点位逐一检查。',
  //     checkEndOperate: '2',
  //     timeState: '1',
  //     timeStateName: '正常',
  //     startEndTime: '2024-10-11 18:00:00~2024-10-11 21:00:00',
  //   },
  // ];
}

const [loading, search] = useAutoLoading(true);
const { pagination, updateTotal } = useNaivePagination(getTableData);

const columns = ref<DataTableColumns>([]);

function setColumns() {
  columns.value.push(...cols);
  // 添加操作栏 action
  columns.value.push({
    title: '操作',
    key: 'actions',
    align: 'left',
    fixed: 'right',
    width: 80,
    render(row) {
      return getActionBtn(row);
    },
  });
}
setColumns();

function getActionBtn(row: any) {
  const acList: [VNode, boolean?][] = [
    [
      h(
        NButton,
        {
          size: 'small',
          type: 'primary',
          ghost: true,
          class: 'com-action-button',
          onClick: () => {
            console.log(router);
            router.push({ name: 'hazardTaskDetail', params: { id: row.taskId, tenantryId: tenantryId.value } });
          },
        },
        { default: () => '详情' }
      ),
    ],
  ];
  return useActionDivider(acList);
}

let filterData: IObj<any> = {}; // 搜索条件
function getTableDataWrap(data: IObj<any>) {
  filterData = Object.assign({}, data) || {};
  pagination.page = 1;
  getTableData();
}
defineOptions({ name: 'TaskTable' });
defineExpose({ getTableDataWrap, getTableData });
</script>
<style lang="scss"></style>
