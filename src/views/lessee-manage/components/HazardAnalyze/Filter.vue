<template>
  <n-form :show-feedback="false" label-placement="left" ref="formRef">
    <n-grid :x-gap="12" :y-gap="8" :cols="4">
      <n-grid-item>
        <n-form-item label="任务起止时间:">
          <n-date-picker v-model:formatted-value="daterange" value-format="yyyy-MM-dd" type="daterange" clearable />
        </n-form-item>
      </n-grid-item>
      <n-grid-item>
        <n-form-item label="检查类型:">
          <n-select v-model:value="filterForm.planType" :options="planTypeOptions" clearable />
        </n-form-item>
      </n-grid-item>
      <n-grid-item>
        <n-form-item label="任务状态:">
          <n-select v-model:value="filterForm.taskState" :options="taskStateOptions" clearable />
        </n-form-item>
      </n-grid-item>
      <n-grid-item>
        <n-form-item>
          <n-input placeholder="请输入计划名称/创建人模糊搜索" v-model:value="filterForm.keyWords" clearable>
            <template #suffix>
              <BsSearch />
            </template>
          </n-input>
        </n-form-item>
      </n-grid-item>
    </n-grid>
  </n-form>
</template>

<script setup lang="ts">
import { computed, onMounted, ref, watch } from 'vue';
import { trimObjNull } from '@/utils/obj.ts';
import { ACTION, IPlanType, ITaskFilter, ITaskState, taskStateOptions } from './type';
import { BsSearch } from '@kalimahapps/vue-icons';
import { useStore } from '@/store/index.ts';
import { getDefaultDateRange } from '@/utils/getDefaultDateRange';
import { throttle } from 'lodash-es';

const { userInfo } = useStore();

import { useRoute } from 'vue-router';
const route = useRoute();

const defaultDateRange = getDefaultDateRange();
const emits = defineEmits(['action']);

const formRef = ref();

const planTypeOptions = [
  { label: IPlanType[1], value: 1 },
  { label: IPlanType[2], value: 2 },
  { label: IPlanType[3], value: 3 },
];

const filterForm = ref<ITaskFilter>({
  // unitId: userInfo.orgCode,
  // planStartDate: defaultDateRange[0],
  // planEndDate: defaultDateRange[1],
});

if (route.query?.dateTime) {
  let dateTime = JSON.parse(route.query.dateTime as string);
  filterForm.value.planStartDate = dateTime[0];
  filterForm.value.planEndDate = dateTime[1];
}

const daterange = computed({
  get: () => {
    if (filterForm.value.planStartDate && filterForm.value.planEndDate)
      return [filterForm.value.planStartDate, filterForm.value.planEndDate];
    return null;
  },
  set: (val) => {
    if (!val) {
      filterForm.value.planStartDate = '';
      filterForm.value.planEndDate = '';
    } else {
      filterForm.value.planStartDate = val[0];
      filterForm.value.planEndDate = val[1];
    }
  },
});

const doHandle = throttle((action: ACTION) => {
  emits('action', {
    action,
    data: trimObjNull(filterForm.value),
  });
}, 1000);

watch(filterForm.value, () => {
  doHandle(ACTION.search);
});

onMounted(() => {
  doHandle(ACTION.search);
});

const resetField = () => {
  Object.keys(filterForm.value).forEach((key) => {
    filterForm.value[key as keyof ITaskFilter] = null;
  });

  // filterForm.value.planStartDate = defaultDateRange[0];
  // filterForm.value.planEndDate = defaultDateRange[1];
  // filterForm.value.unitId = userInfo.orgCode;
};

defineExpose({ resetField });
defineOptions({ name: 'checkTempFilterComp' });
</script>
<style module lang="scss"></style>
