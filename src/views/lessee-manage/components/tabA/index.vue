<template>
  <div class="">
    <!-- 客户基本信息 -->
    <div class="w_execute_content com-container-bg">
      <div class="title">客户基本信息</div>
      <div class="pb-[25px]">
        <div class="w_div_name">{{ infoData.tenantryName || '--' }}</div>
        <div class="">
          <div class="w_flex_div_box mt-[20px]">
            <div class="w_flex_div">
              <div>统一社会信用代码：{{ infoData.socialCode || '--' }}</div>
            </div>
            <!--            <div class="w_flex_div">-->
            <!--              <div>相关方类型：{{ infoData.name || '&#45;&#45;' }}</div>-->
            <!--            </div>-->
            <div class="w_flex_div">
              <div>客户负责人：{{ infoData.tenantryManager || '--' }}</div>
            </div>
            <div class="w_flex_div">
              <div>
                客户负责人联系方式：{{ infoData.tenantryManagerPhone || '--' }}
              </div>
            </div>
          </div>
          <div class="w_flex_div_box mt-[20px]">
            <div class="w_flex_div">
              <div>客户性质：{{ infoData.tenantryNatureName || '--' }}</div>
            </div>
            <div class="w_flex_div">
              <div>客户来源：{{ infoData.tenantrySourceName || '--' }}</div>
            </div>
            <div class="w_flex_div">
              <div>所属公司：{{ infoData.belongCompanyName || '--' }}</div>
            </div>
            <div class="w_flex_div">
              <div>
                项目经营单位：{{ infoData.projectManagementUnitName || '--' }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { getTenantDetail } from './fetchData'
import { useRoute } from 'vue-router'
import Empty from '@/components/empty/Empty.vue'

const route = useRoute()
const businessUrl = ref<string>('')
const infoData = ref({
  name: '天泽科技公司',
  url: 'https://07akioni.oss-cn-beijing.aliyuncs.com/07akioni.jpeg'
})

function getDetail() {
  getTenantDetail({ tenantryId: route.params.tenantryId }).then((res) => {
    if (res.code != 'success') return
    infoData.value = res.data
  })
}

function getFileURL(filePath: string) {
  return window.$SYS_CFG.apiImageURL + filePath
}

getDetail()

defineOptions({ name: 'LesseeManageA' })
</script>

<style scoped lang="scss">
.w_execute_content {
  width: 100%;
  border-radius: 9px;
  padding: 1px;

  .title {
    padding: 15px 24px;
    background-color: #dce4f4;
    font-size: 16px;
    border-radius: 9px 9px 0px 0px;
    font-weight: 500;
  }

  .w_div_name {
    font-size: 18px;
    padding-left: 24px;
    margin-top: 20px;
    color: rgba(82, 124, 255, 1);
  }

  .w_flex_div_box {
    display: flex;
    justify-content: space-between;
    width: 100%;

    //padding: 0 50px;
    .w_flex_div {
      flex: 1;

      > div {
        height: 32px;
        line-height: 32px;
        font-size: 14px;
        padding-left: 24px;
      }
    }
  }

  .w_flex_img_box {
    display: flex;

    .w_img_title {
      height: 32px;
      line-height: 32px;
      font-size: 14px;
    }
  }

  .w_flex_1 {
    flex: 1;
    padding-left: 24px;
  }

  .w_flex_3 {
    padding-left: 12px;
    flex: 3;
  }

  .w_empty {
    height: 100px;
    width: 100px;
    position: relative;

    .w_empty_no {
      position: absolute;
      bottom: 0px;
      left: 25px;
      color: #7a7a7a;
    }
  }
}
</style>
