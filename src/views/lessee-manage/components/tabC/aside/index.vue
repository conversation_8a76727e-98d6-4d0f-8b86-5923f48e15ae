<template>
  <ComDrawerA
    :autoFocus="false"
    :footerPaddingBottom="25"
    :maskClosable="false"
    :show-action="true"
    @handle-negative="handleClose"
    @handle-positive="handleSubmit"
    class="!w-[500px]"
    v-model:show="isShowAside"
  >
    <div class="w_dialog_box">
      <div>
        <n-form ref="formRef" :model="sendFrom" :rules="rules" label-width="160" require-mark-placement="left">
          <div>
            <n-form-item label="人员姓名" path="userName">
              <n-input v-model:value="sendFrom.userName" placeholder="请输入人员姓名" @input="filterInput" />
            </n-form-item>
            <n-form-item label="手机号：" path="phone">
              <n-input v-model:value="sendFrom.phone" placeholder="请输入手机号" @input="handleInput" />
            </n-form-item>
            <n-form-item label="投保信息：" path="">
              <imageSHow :data="insuranceInfo" @update="handleUpdate" :max="2" type="1" auto=""> </imageSHow>
            </n-form-item>
            <n-form-item label="上传证件照：" path="">
              <imageSHow :data="cardFile" @update="handleUpdate" :max="1" type="2" auto=""></imageSHow>
            </n-form-item>
            <n-form-item label="身份证（人像面）：" path="">
              <imageSHow
                :data="idPhotoFile"
                @update="handleUpdate"
                :max="1"
                type="3"
                auto="2"
                @automatic="handleAutoCard"
              ></imageSHow>
            </n-form-item>
            <n-form-item label="身份证（国徽面）：" path="">
              <imageSHow
                :data="idPhotoBackFile"
                @update="handleUpdate"
                :max="1"
                type="4"
                auto="1"
                @automatic="handleAutoDate"
              ></imageSHow>
            </n-form-item>
            <n-form-item label="身份证号码：" path="">
              <n-input v-model:value="sendFrom.idNo" placeholder="请输入身份证号码" />
            </n-form-item>
            <n-form-item label="身份证有效期：" path="">
              <n-date-picker
                v-model:value="timeRange"
                type="daterange"
                clearable
                :on-update:formatted-value="onChangeData"
              />
            </n-form-item>
          </div>
        </n-form>
      </div>
    </div>
  </ComDrawerA>
</template>
<!--    v-model:show="isShowAside"-->
<!--    ref="comA"-->
<script lang="ts" setup>
import ComDrawerA from '@/components/drawer/ComDrawerA.vue';
import { imageSHow } from '@/components/upload';
// import type { IActionData } from '../../type';
import { ACTION, PROVIDE_KEY } from '../constant';
import { computed, useAttrs, ref, watch } from 'vue';
import type { FormInst, FormRules } from 'naive-ui';
import { IUploadRes } from '@/components/upload/type';
import { addUser, updateUser, detailUser } from '../fetchData.ts';
import { $toast } from '@/common/shareContext/useToastCtx.ts';
import { useRoute } from 'vue-router';
import { getUnitList } from '@/views/tenantry/tenantManagement/server/fetchData.ts';

// const comA = ref()
const isShowAside = ref(false);

interface IItem {
  id: string;
  path: string;
  fileName: string;
}
// const props = defineProps({
//   // 弹窗内容
//   data: {
//     type: Object,
//     default: () => ({}),
//   },
// });
const timeRange = ref<any>(null);
const fileData = ref<any[]>([]);
const insuranceInfo = ref<any>([]); // 投保信息
const cardFile = ref<any>([]); // 证件信息
const idPhotoFile = ref<any>([]); // 身份证信息
const idPhotoBackFile = ref<any>([]); // 身份证反面信息
const emits = defineEmits(['action', 'update:show']);
const attrs = useAttrs();
const show = computed(() => !!attrs.show);
const route = useRoute();
const formRef = ref<FormInst | null>(null);

const validateName = (rule, value, callback) => {
  if (value && value.length > 50) {
    callback(new Error('姓名不能超过50个字符'));
  } else {
    callback(); // 校验通过
  }
};
const sendFrom = ref<any>({
  id: '',
  userName: '',
  phone: '',
  idNo: '',
  idStartPeriod: '', // 起时间
  idEndPeriod: '', // 终时间
  tenantryId: route.params.tenantryId,
});

const rules: FormRules = {
  userName: [
    {
      required: true,
      message: '请输入人员姓名',
      trigger: ['input', 'blur'],
    },
    { validator: validateName, trigger: ['input', 'blur'] },
  ],
  phone: [
    {
      required: true,
      message: '请输入手机号',
      trigger: ['input', 'blur'],
    },
    {
      validator: (rule, value, callback) => {
        const regex = /^1[3-9]\d{9}$/;
        if (!regex.test(value)) {
          callback(new Error('请输入正确的手机号'));
        } else {
          callback();
        }
      },
      trigger: ['blur', 'change'],
    },
  ],
  idNo: {
    required: true,
    message: '请输入身份证号码',
    trigger: ['input', 'blur'],
  },
  idEndPeriod: {
    required: true,
    message: '请选择身份证有效期',
    trigger: ['input', 'blur'],
  },
};

const onChangeData = (value: any) => {
  console.log(value, '+++++++++++');
  if (value) {
    const _stime = value ? value[0] : '';
    const _etime = value ? value[1] : '';
    sendFrom.value.idStartPeriod = _stime;
    sendFrom.value.idEndPeriod = _etime;
  } else {
    sendFrom.value.idStartPeriod = '';
    sendFrom.value.idEndPeriod = '';
  }
};

function filterInput(value: string) {
  // 只允许汉字和字母数字
  const validInput = value.replace(/[^a-zA-Z0-9\u4e00-\u9fa5]/g, '');
  sendFrom.value.userName = validInput;
}
function handleInput(value: string) {
  // 只允许汉字和字母数字
  const validInput = value.replace(/\D/g, '');
  sendFrom.value.phone = validInput;
}
// 获取详情
function getDetail(id: string) {
  detailUser({ id }).then((res: any) => {
    if (res && res.code === 'success') {
      for (const key in sendFrom.value) {
        if (res.data.hasOwnProperty(key)) {
          sendFrom.value[key] = res.data[key];
        }
      }
      timeRange.value = res.data.idStartPeriod
        ? [new Date(res.data.idStartPeriod), new Date(res.data.idEndPeriod)]
        : null;
      // 回显图片
      insuranceInfo.value = res.data.insuranceInformationFileAttachment || [];
      cardFile.value = res.data.cardFileAttachment || [];
      // 回显身份证正面
      if (res.data.idPhotoFileAttachment[0]) {
        idPhotoFile.value = [res.data.idPhotoFileAttachment[0]];
      }
      // 回显身份证反面
      if (res.data.idPhotoFileAttachment[1]) {
        idPhotoBackFile.value = [res.data.idPhotoFileAttachment[1]];
      }
    }
  });
}

// 获取身份证日期
function handleAutoDate(data: any) {
  console.log('chufale', data);
  if (data.data.start_date) {
    timeRange.value = [new Date(data.data.start_date), new Date(data.data.end_date)];
    // 身份证日期赋值
    sendFrom.value.idStartPeriod = data.data.start_date.split('.').join('-');
    sendFrom.value.idEndPeriod = data.data.end_date.split('.').join('-');
  }
}

// 获取身份证正面身份证号码
function handleAutoCard(data: any) {
  console.log('ch11111ufale', data);
  if (data.data.id_number) {
    sendFrom.value.idNo = data.data.id_number;
  }
}

function init() {
  timeRange.value = null;
  insuranceInfo.value = [];
  cardFile.value = [];
  idPhotoFile.value = [];
  idPhotoBackFile.value = [];
  return {
    id: '',
    userName: '',
    phone: '',
    idNo: '',
    idStartPeriod: '', // 起时间
    idEndPeriod: '', // 终时间
    tenantryId: route.params.tenantryId,
  };
}

function handleSubmit() {
  let _data = {
    ...sendFrom.value,
    insuranceInformationFileAttachment: insuranceInfo.value,
    idPhotoFileAttachment: [...idPhotoFile.value, ...idPhotoBackFile.value],
    cardFileAttachment: cardFile.value,
    // cardFileAttachment: cardFile.value,
  };
  console.log('_data', _data);
  formRef.value?.validate((errors) => {
    if (!errors) {
      if (!sendFrom.value.id) {
        addUser(_data).then((res: any) => {
          if (res && res.code === 'success') {
            // $toast.success(res.message);
            handleSubmitted();
            // init();
          }
        });
      } else {
        updateUser(_data).then((res: any) => {
          if (res && res.code === 'success') {
            // $toast.success(res.message);
            handleSubmitted();
            // init();
          }
        });
      }
    }
  });
}

function handleUpdate(res: IUploadRes[], type: string) {
  if (type === '1') {
    insuranceInfo.value = [];
    res.forEach((item) => {
      insuranceInfo.value.push({ id: item.id, path: item.path, fileName: item.fileName });
    });
  } else if (type === '2') {
    cardFile.value = [];
    res.forEach((item) => {
      cardFile.value.push({ id: item.id, path: item.path, fileName: item.fileName });
    });
    console.log('cardFile.value', cardFile.value);
  } else if (type === '3') {
    idPhotoFile.value = [];
    res.forEach((item) => {
      idPhotoFile.value.push({ id: item.id, path: item.path, fileName: item.fileName });
    });
  } else {
    idPhotoBackFile.value = [];
    res.forEach((item) => {
      idPhotoBackFile.value.push({ id: item.id, path: item.path, fileName: item.fileName });
    });
  }
}

function handleSubmitted() {
  emits('action', { action: ACTION.SEARCH });
  handleClose();
}

function handleClose() {
  console.log('guanbi+++');
  isShowAside.value = false;

  // emits('update:show' as any, false); // any-屏蔽组件内透传的emit类型
}

// watch(
//   () => props.data,
//   (val) => {
//     if (val.id) {
//
//     } else {
//       sendFrom.value = init();
//     }
//   },
//   { immediate: true }
// );

function showDialog(id: any) {
  sendFrom.value = init();
  if (id) {
    getDetail(id);
  }

  isShowAside.value = true;
}

defineExpose({
  showDialog,
});

defineOptions({ name: 'serverAddDialogIndex' });
</script>

<style scoped lang="scss">
.w_dialog_box {
  height: calc(100% - 200px) !important;
  //background-color: hotpink;
  width: 100%;
  padding: 20px;

  .w_a_title {
    height: 32px;
    line-height: 32px;
    color: #527cff;
  }
}
</style>
