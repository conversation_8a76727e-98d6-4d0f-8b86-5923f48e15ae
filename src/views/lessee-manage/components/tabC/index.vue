<template>
  <div class="w_per_box">
    <div class="flex justify-end items-center">
      <n-button type="primary" @click="addHandle"> 新增 </n-button>
    </div>
    <n-data-table
      class="mt-4"
      style="height: calc(100% - 50px)"
      remote
      striped
      :columns="columns"
      :data="tableData"
      :bordered="false"
      :flex-height="true"
      :loading="loading"
      :pagination="pagination"
      :render-cell="useEmptyCell"
    />

    <AsideComp ref="addRef" :title="title" @action="actionFn" :data="rowData" />
  </div>
</template>

<script setup lang="ts">
import { h, ref, VNode, onMounted } from 'vue';
import { DataTableColumns, NButton } from 'naive-ui';
import { cols } from './columns';
import { ACTION } from './constant.ts';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import { useNaivePagination } from '@/common/hooks/useNaivePagination.ts';
import { useActionDivider } from '@/common/hooks/useActionDivider.ts';
import { useEmptyCell } from '@/common/hooks/useEmptyCell.ts';
import { getPageUserList, delUser } from './fetchData';
import { useRoute } from 'vue-router';
import { IActionData, ICheckTempRow } from '@/views/tenantry/tenantManagement/server/type.ts';
import { $dialog } from '@/common/shareContext/useDialogCtx.ts';
import AsideComp from './aside/index.vue';

const route = useRoute();

const tableData = ref<any[]>();

const rowData = ref<any>({});
const title = ref<string>('');
const addRef = ref();
function actionFn(val: IActionData) {
  if (val.action === ACTION.SEARCH) {
    getTableDataWrap();
  } else if (val.action === ACTION.DELETE) {
    return;
  }
}

function getTableData() {
  const params = {
    tenantryId: route.params.tenantryId,
    pageNo: pagination.page,
    pageSize: pagination.pageSize,
  };

  search(getPageUserList(params)).then((res: any) => {
    tableData.value = res.data.rows || [];
    updateTotal(res.data.total || 0);
  });
  console.log(tableData.value);
}

const [loading, search] = useAutoLoading(false);
const { pagination, updateTotal } = useNaivePagination(getTableData);

const columns = ref<DataTableColumns>([]);

function setColumns() {
  columns.value.push(...cols);
  // 添加操作栏 action
  columns.value.push({
    title: '操作',
    key: 'actions',
    align: 'left',
    fixed: 'right',
    width: 180,
    render(row) {
      return getActionBtn(row);
    },
  });
}
setColumns();

function addHandle() {
  title.value = '新增';
  rowData.value = {};
  addRef.value.showDialog();
  // isShowAside.value = true;
}

function getActionBtn(row: any) {
  const acList: [VNode, boolean?][] = [
    [
      h(
        NButton,
        {
          size: 'small',
          type: 'primary',
          ghost: true,
          class: 'com-action-button',
          onClick: () => {
            title.value = '编辑';
            rowData.value = JSON.parse(JSON.stringify(row));
            addRef.value.showDialog(row.id);
            // isShowAside.value = true;
          },
        },
        { default: () => '编辑' }
      ),
    ],
    [
      h(
        NButton,
        {
          size: 'small',
          type: 'primary',
          ghost: true,
          class: 'com-action-button',
          onClick: () => {
            getDelUser(row.id);
          },
        },
        { default: () => '删除' }
      ),
    ],
  ];
  return useActionDivider(acList);
}

function getTableDataWrap() {
  pagination.page = 1;
  getTableData();
}

function getDelUser(delid: string | number) {
  $dialog.error({
    title: '删除',
    content: `确定要删除吗？`,
    positiveText: '确定',
    negativeText: '取消',
    transformOrigin: 'center',
    onPositiveClick: async () => {
      delUser({ id: delid, tenantryId: route.params.tenantryId }).then(() => {
        // deleteTenant({ id: data.id }).then(() => {
        getTableData();
      });
    },
  });
}

getTableDataWrap();

defineOptions({ name: 'TaskTable' });
// defineExpose({ getTableDataWrap, getTableData });
</script>
<style lang="scss">
.w_per_box {
  height: calc(100% - 290px);
  width: 100%;
  background-color: #edf6fe;
  padding: 20px;
  border-radius: 8px;
}
</style>
