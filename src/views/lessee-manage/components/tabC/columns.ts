import { DataTableColumn, NImageGroup, NSpace, NImage } from 'naive-ui';
import { h } from 'vue';

export const cols: DataTableColumn[] = [
  {
    title: '序号',
    key: 'index',
    width: 65,
    align: 'center',
    render: (_: any, index: number) => {
      return index + 1;
    },
  },
  {
    title: '姓名',
    key: 'userName',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '手机号',
    key: 'phone',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '账号',
    key: 'loginName',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '证件照',
    key: 'cardFileAttachment',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
    render: (row: any) => {
      // 判断 cardFileAttachment 是否为有效数组
      const attachments = row.cardFileAttachment;
      if (!attachments || (Array.isArray(attachments) && attachments.length === 0)) {
        return h('span', '--');
      }

      return [
        h(NImageGroup, null, {
          default: () =>
            h(NSpace, null, {
              default: () =>
                attachments.map((image: any) =>
                  h(NImage, { src: `${window.$SYS_CFG.apiImageURL}${image.path}`, width: '80' })
                ),
            }),
        }),
      ];
      // 如果是有效数组，渲染图片
      // return attachments.map((item: any) => {
      //   return h(NSpace, {
      //     src: `${window.$SYS_CFG.apiImageURL}${item.path}`, // 图片 URL
      //     alt: row.name, // 图片 alt 属性
      //     style: { width: '100px', height: '100px', marginRight: '5px' },
      //   });
      // });
    },
  },
  {
    title: '身份证号',
    key: 'idNo',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '身份证有效期',
    key: 'idExpireTime',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '年龄',
    key: 'age',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '创建人',
    key: 'createUserName',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '创建时间',
    key: 'createTime',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
  },
];
