import { IObj, IPageRes } from '@/types';
import { api } from '@/api';
import { $http } from '@tanzerfe/http';
// import {IFormData} from "@/views/tenantry/tenantManagement/server/type.ts";

// const actionURL = api.getUrl(api.type.file, api.name.file.uploadFile);

export function getPageUserList(query: IObj<any>) {
  const url = api.getUrl(api.type.lease, api.name.lease.pageUserList, query);
  return $http.post(url, { data: { _cfg: { showTip: true, showOkTip: false } } });
}

// 创建
export function addUser(data: IObj<any>) {
  const url = api.getUrl(api.type.lease, api.name.lease.addUser);
  return $http.post<any>(url, { data: { _cfg: { showTip: true, showOkTip: false }, ...data } });
}

export function updateUser(data: IObj<any>) {
  const url = api.getUrl(api.type.lease, api.name.lease.updateUser);
  return $http.post<any>(url, { data: { _cfg: { showTip: true, showOkTip: false }, ...data } });
}

export function delUser(data: IObj<any>) {
  const url = api.getUrl(api.type.lease, api.name.lease.delUser);
  return $http.post<any>(url, { data: { _cfg: { showTip: true, showOkTip: false }, ...data } });
}

export function saveTenantFile(data: IObj<any>) {
  const url = api.getUrl(api.type.lease, api.name.lease.saveTenantFile);
  return $http.post<any>(url, { data: { _cfg: { showTip: true, showOkTip: false }, ...data } });
}

export function detailUser(data: IObj<any>) {
  const url = api.getUrl(api.type.lease, api.name.lease.detailUser);
  return $http.post<any>(url, { data: { _cfg: { showTip: true, showOkTip: false }, ...data } });
}
