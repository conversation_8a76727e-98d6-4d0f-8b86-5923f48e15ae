import { IObj, IPageRes } from '@/types';
import { api } from '@/api';
import { $http } from '@tanzerfe/http';
// import {IFormData} from "@/views/tenantry/tenantManagement/server/type.ts";

// const actionURL = api.getUrl(api.type.file, api.name.file.uploadFile);

export function getContractList(query: IObj<any>) {
  const url = api.getUrl(api.type.lease, api.name.lease.getContractList, query);
  return $http.post(url, { data: { _cfg: { showTip: true } } });
}
export function getRTenantryInfoDetail(query: IObj<any>) {
  const url = api.getUrl(api.type.lease, api.name.lease.getRTenantryInfoDetail, query);
  return $http.post(url, { data: { _cfg: { showTip: true } } });
}
