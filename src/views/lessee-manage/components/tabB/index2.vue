<template>
  <div class="w_contract_box">
    <n-scrollbar style="height: 100%">
      <div class="w_contract">
        <div class="w_contract_item_box" v-for="(item, index) in infoList" :key="index">
          <div class="w_contract_item_div">
            <div class="w_contract_title">合同编号：{{ item.contractNo || '--' }}</div>
            <div class="w_contract_div">
              <!--          合同有效期限：{{ item.contractStartTime || '&#45;&#45;' + ' ' + item.contractEndTime }}-->
              合同有效期限：{{ item.contractStartTime || '-' }} {{ item.contractEndTime || '-' }}
            </div>
            <div class="w_contract_div">合同上传时间：{{ item.createTime || '--' }}</div>
            <div class="w_contract_div">合同附件：</div>
            <div v-if="item.contractAttachmentFiles && item.contractAttachmentFiles.length > 0">
              <n-image
                v-for="(items, index2) in item.contractAttachmentFiles"
                :key="index2"
                :width="100"
                :src="getFileURL(items.path)"
                class="mr-4 h-[100px]"
              />
            </div>
          </div>
        </div>
        <!--      <div class="w_page">-->
        <!--        <n-pagination-->
        <!--          v-model:page="page"-->
        <!--          v-model:page-size="pageSize"-->
        <!--          :page-count="100"-->
        <!--          show-size-picker-->
        <!--          :page-sizes="[10, 20, 30, 40]"-->
        <!--        />-->
        <!--      </div>-->
      </div>
    </n-scrollbar>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import Image from '@/components/upload/image.vue';
import { useRoute } from 'vue-router';
import { getContractList } from './fetchData';
import { forEach } from 'lodash-es';

const route = useRoute();

const infoList = ref([]);

const infoData = {
  tenantryId: '',
};

function getList() {
  getContractList({ tenantryId: route.params.id }).then((res) => {
    if (res.code != 'success') return;
    infoList.value = res.data;
    // for (let i = 0; i < 1; i++) {
    //   infoList.value.push({ ...res.data });
    // }
  });
}

function getFileURL(filePath: string) {
  return window.$SYS_CFG.apiImageURL + filePath;
}

getList();

defineOptions({ name: 'LesseeManage' });
</script>

<style scoped lang="scss">
.w_contract_box {
  height: calc(100% - 290px);
  width: 100%;
  background: #eef7ff;
  box-sizing: border-box;
  padding: 20px 20px 0px;
  box-shadow: 0px 2px 6px 1px rgba(0, 0, 0, 0.05);
  //border: 1px solid #FFFFFF;
  border-radius: 6px;
}
.w_contract {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  .w_contract_item_box {
    border-radius: 6px;
    height: 258px;
    width: 49.5%;
    background-color: #ffffff;
    margin-bottom: 20px;
    box-sizing: border-box;
    .w_contract_item_div {
      padding: 10px 20px 10px;
      height: 250px;
      box-sizing: border-box;
    }
    .w_contract_title {
      font-size: 18px;
      height: 32px;
      line-height: 32px;
      color: rgba(82, 124, 255, 1);
    }
    .w_contract_div {
      font-size: 16px;
      height: 32px;
      line-height: 32px;
      color: rgba(34, 34, 34, 1);
    }
    .w_contract_img {
      height: 70px;
      width: 70px;
    }
  }
  .w_page {
    width: 100%;
    height: 50px;
    background-color: red;
  }
}
</style>
