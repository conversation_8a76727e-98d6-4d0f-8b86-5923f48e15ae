<template>
  <div class="w_contract_box">
    <div class="bg-[#eef7ff] p-[20px] mb-[20px] rounded">
      <n-form :show-feedback="false" label-placement="left">
        <div class="flex">
          <n-form-item label="合同到期预警:">
            <n-select
              v-model:value="filterForm.resourceTypeCode"
              class="!w-[220px] mr-5"
              :options="resourceOpt"
              placeholder="全部"
              value-field="paramCode"
              label-field="paramValue"
              clearable
            />
          </n-form-item>
          <n-form-item label="合同编号:">
            <n-input
              v-model:value="filterForm.searchQuery"
              placeholder="请输入合同编号"
              clearable
            />
          </n-form-item>
        </div>
      </n-form>
    </div>

    <div style="height: calc(100% - 80px)">
      <n-scrollbar style="height: calc(100% - 30px)">
        <div
          v-for="item in infoList"
          :key="item"
          class="bg-[#eef7ff] p-[20px] w_contract_main"
        >
          <div>
            <ComHeaderC title="承租合同信息" :active-icon="true"></ComHeaderC>
            <div class="flex w_contract_info">
              <div>
                当前生效合同编号：{{ item.effectiveContractNo || '--' }}
              </div>
              <div>合同到期天数：{{ item.contractExpirationDays }}</div>
              <div>
                合同有效期：{{
                  item.contractStartTime + '~' + item.contractEndTime
                }}
              </div>
            </div>
            <div class="flex contract-image items-start">
              <div class="w_contract_div">合同附件：</div>
              <div class="flex">
                <n-image
                  v-for="image in item.contractAttachmentFiles"
                  :key="image"
                  :width="100"
                  class="mr-4 h-[100px] rounded-sm"
                  :src="getFileURL(image.path)"
                />
                <n-empty
                  v-if="!item.contractAttachmentFiles.length"
                  class="h-[100px]"
                  size="large"
                  description="暂无附件"
                ></n-empty>
              </div>
            </div>
          </div>

          <div class="w_resources_box">
            <ComHeaderC title="承租资源信息" :active-icon="true">
              <template #right>
                <div class="mr-[10px] flex items-center">
                  <n-icon :size="20" class="mr-20px" @click="handleClick(item)">
                    <AkChevronUpSmall v-if="item.flag" />
                    <AkChevronDownSmall v-else />
                  </n-icon>
                </div>
              </template>
            </ComHeaderC>
            <div class="w_resources_main" :class="item.flag ? '' : 'is-fold'">
              <div class="w_resources_content">
                <div
                  v-for="child in item.rtenantryResourceList"
                  :key="child"
                  class="w_resources_item_box"
                >
                  <div class="w_resources_item">
                    <div>项目名称：{{ child.projectName || '--' }}</div>
                    <div>资源名称：{{ child.resourceName || '--' }}</div>
                    <div>
                      资源类型：{{ child.resourceTypeCodeName || '--' }}
                    </div>
                    <div>建筑面积：{{ child.buildingArea || '--' }}</div>
                    <div>使用面积：{{ child.usableArea || '--' }}</div>
                    <div>资源位置：{{ child.locationName || '--' }}</div>
                    <div>
                      计租面积：{{ child.calculatedRentalArea || '--' }}
                    </div>
                    <div>
                      计租模式：{{ child.rentCalculationModeCodeName || '--' }}
                    </div>
                  </div>
                  <div class="child"></div>
                </div>
                <div
                  class="empty h-[150px] w-[100%] flex items-center justify-center"
                >
                  <n-empty
                    v-if="!item.rtenantryResourceList.length"
                    size="large"
                    description="暂无数据"
                  ></n-empty>
                </div>
              </div>
            </div>
          </div>

          <!-- 右上角到期状态 -->
          <div
            v-if="item.contractExpirationWarningCode === '0'"
            class="w_state1"
          ></div>
          <div
            v-else-if="item.contractExpirationWarningCode === '1'"
            class="w_state2"
          ></div>
          <div
            v-else-if="item.contractExpirationWarningCode === '2'"
            class="w_state3"
          ></div>
        </div>
        <div
          v-if="!infoList.length"
          class="empty h-full w-[100%] flex items-center justify-center bg-[#eef7ff]"
        >
          <n-empty size="large" description="暂无数据"></n-empty>
        </div>
      </n-scrollbar>
      <div class="pagination">
        <n-pagination
          v-model:page="page"
          v-model:page-size="pageSize"
          :item-count="total"
          show-size-picker
          :page-sizes="[5, 10, 20, 30]"
        >
          <template #prefix> 共 {{ total }} 条 </template>
        </n-pagination>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { getRTenantryInfoDetail, getContractList } from './fetchData'
import { queryDictList } from '@/views/tenantry/tenantManagement/server/fetchData.ts'
import ComHeaderC from '@/components/header/ComHeaderC.vue'
import { AkChevronUpSmall, AkChevronDownSmall } from '@kalimahapps/vue-icons'

const route = useRoute()
const infoList = ref<any>([])
const page = ref(1)
const pageSize = ref(5)
const total = ref(0)
const isShowLoading = ref(false)
const resourceOpt = ref([])

const filterForm = ref(initForm())

function initForm() {
  return {
    resourceTypeCode: null,
    projectManagementUnitId: '',
    searchQuery: '',
    blacklistCode: 0
  }
}

const handleClick = (item: any) => {
  item.flag = !item.flag
}
function getResourceOpt() {
  queryDictList({ dictType: 'htdqyj' }).then((res: any) => {
    if (res.code != 'success') return
    if (res.data && res.data.length > 0) {
      resourceOpt.value = res.data
    }
  })
}

function getList() {
  console.log('发情请求列表', route.params)
  isShowLoading.value = true
  const params = {
    pageNo: page.value,
    pageSize: pageSize.value,
    searchQuery: filterForm.value.searchQuery,
    tenantryId: route.params.tenantryId,
    alarmType: filterForm.value.resourceTypeCode, // 到期状态
    contractNo: filterForm.value.searchQuery // 合同编号
  }
  // getContractList
  // getRTenantryInfoDetail

  getRTenantryInfoDetail(params)
    .then((res: any) => {
      if (res.code != 'success') return
      if(res.data.rtenantryInfoList && res.data.rtenantryInfoList.length > 0) {
        res.data.rtenantryInfoList?.forEach((item: any, idx: number) => {
          // 添加一个flag属性，用来控制是否展开
          item.flag = idx == 0
        })
      }
      infoList.value = res.data.rtenantryInfoList || []
      total.value = res.data.total
    })
    .finally(() => {
      isShowLoading.value = false
    })
}

function getFileURL(filePath: string) {
  return window.$SYS_CFG.apiImageURL + filePath
}

watch(filterForm.value, () => {
  console.log('发情请求列表')
  getList()
})

onMounted(() => {
  getResourceOpt()
  getList()
})

defineOptions({ name: 'LesseeManage' })
</script>

<style scoped lang="scss">
:deep(.n-scrollbar-content) {
  height: 100%;
}

.pagination {
  margin-top: 10px;
  padding-right: 20px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  height: 40px;
  background-color: #eef7ff;
  border-top: #ccc 1px solid;
}

.w_contract_box {
  height: calc(100% - 290px);
  width: 100%;
  // background: #eef7ff;
  box-sizing: border-box;
  // padding: 20px 20px 0px;
  box-shadow: 0px 2px 6px 1px rgba(0, 0, 0, 0.05);
  //border: 1px solid #FFFFFF;
  border-radius: 6px;

  .rounded {
    border-radius: 6px;
    padding: 10px 20px;
    height: 60px;
    display: flex;
    align-items: center;
  }

  .w_contract_main {
    position: relative;
    margin-bottom: 20px;
    min-height: 100px;

    &:last-child {
      margin-bottom: 0px;
    }

    .w_state1,
    .w_state2,
    .w_state3 {
      position: absolute;
      right: 10px;
      top: 10px;
      width: 108px;
      height: 100px;
      background: url('./image/state-normal.png') no-repeat center/cover;
    }

    .w_state2 {
      background: url('./image/state-inover.png') no-repeat center/cover;
    }

    .w_state3 {
      background: url('./image/state-over.png') no-repeat center/cover;
    }
  }

  .w_contract_info_title {
    height: 32px;
    line-height: 32px;
  }

  .w_contract_info {
    padding: 15px 0px;
    font-size: 16px;
    color: #222222;

    & > div {
      flex: 1;
    }

    .w_contract_img {
      height: 70px;
      width: 70px;
    }
  }

  .contract-image {
    padding: 0px 0px 15px;
    display: flex;
    align-items: start;
    font-size: 16px;
    color: #222222;

    .w_contract_div {
    }
  }

  .w_resources_box {
    width: 100%;

    .w_resources_main {
      width: 100%;
      overflow: hidden;
      transition: all 0.8s;

      .w_resources_content {
        display: flex;
        gap: 20px;
        overflow-x: auto;

        // 美化一下滚动条
        &::-webkit-scrollbar {
          height: 6px;
        }

        &::-webkit-scrollbar-thumb {
          background: #b2b9bf;
          height: 6px;
          border-radius: 3px;
        }

        &::-webkit-scrollbar-track {
          background: #f5f5f5;
        }

        .w_resources_item_box {
          flex-shrink: 0;
          /* 禁止元素收缩 */
          position: relative;
          margin: 20px 0 10px;
          width: 378px;
          height: 320px;
          background: url('./image/bg.png') no-repeat center/cover;
          display: flex;
          align-items: center;

          .child {
            position: absolute;
            top: 0px;
            right: 0px;
            width: 100%;
            height: 100%;
            background: url('./image/mengban.png') no-repeat center/cover;
            z-index: 2;
          }

          .w_resources_item {
            div {
              padding-left: 20px;
              height: 36px;
              line-height: 36px;
            }
          }
        }
      }
    }

    .is-fold {
      height: 0;
    }
  }
}
</style>
