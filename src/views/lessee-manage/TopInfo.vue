<template>
  <div class="info">
    <div class="title">{{ infoData.tenantryName }}</div>
    <div class="text">
      <div class="mr-20">
        管理员账号：<span>{{ infoData.tenantryAdminLogin }}</span>
      </div>
      <div class="mr-20">
        资源类型：<span>{{ infoData.resourceTypeName }}</span>
      </div>
      <div class="mr-20">
        所属单位：<span>{{ infoData.belongCompanyName }}</span>
      </div>
      <div class="mr-20">
        项目经营单位：<span>{{ infoData.projectManagementUnitName }}</span>
      </div>
      <div class="mr-20"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRoute } from 'vue-router'
import { getTenantDetail } from './components/tabA/fetchData'

const infoData = ref<any>({})
const route = useRoute()
function getDetail() {
  getTenantDetail({ tenantryId: route.params.tenantryId }).then((res: any) => {
    if (res.code != 'success') return
    infoData.value = res.data
  })
}

getDetail()
</script>

<style lang="scss" scoped>
.info {
  .title {
    font-size: 38px;
    color: #ffffff;
    margin-bottom: 22px;
  }

  .text {
    width: 100%;
    font-size: 16px;
    color: #ffffff;
    display: grid;
    grid-template-columns: repeat(5, 1fr);

    .mr-20 {
      // 不换行
      white-space: nowrap;
    }
  }
}
</style>
