<template>
  <div>
    <com-bread :data="breadData"></com-bread>
    <div class="pt-[30px] pl-[57px] rounded-lg mb-4 h-[150px] info-bg" style="background-color: #4594fe">
      <TopInfo />
    </div>
    <div class="com-box-bg rounded-lg">
      <n-tabs type="segment" animated style="width: 40%" :default-value="activeTab" @update:value="handleChange">
        <n-tab-pane name="tabA" tab="项目信息"> </n-tab-pane>
        <n-tab-pane name="tabB" tab="承租信息"> </n-tab-pane>
        <n-tab-pane name="tabC" tab="人员管理"> </n-tab-pane>
        <n-tab-pane name="HazardAnalyze" tab="隐患分析"> </n-tab-pane>
      </n-tabs>
    </div>
    <!-- 隐患分析 -->
    <keep-alive>
      <component :is="comps[activeTab]" class="mt-5"></component>
    </keep-alive>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import ComBread from '@/components/breadcrumb/ComBread.vue'
import TopInfo from './TopInfo.vue'
import { IBreadData } from '@/components/breadcrumb/type.ts'

// tab-item
import tabA from './components/tabA/index.vue'
import tabB from './components/tabB/index.vue'
import tabC from './components/tabC/index.vue'
import HazardAnalyze from './components/HazardAnalyze/index.vue'

const breadData: IBreadData[] = [
  {
    name: '承租方管理',
    clickable: true,
    routeRaw: {
      name: 'tenantManagement',
    },
  },
  { name: '项目详情' },
]

const activeTab = ref('tabA')
const comps: {
  [key: string]: any
} = {
  tabA,
  tabB,
  tabC,
  HazardAnalyze,
}

function handleChange(name: string) {
  activeTab.value = name
}
defineOptions({ name: 'LesseeManage' })
</script>

<style scoped lang="scss">
//.w_tab_box {
//  display: flex;
//  background-color: #eef7ff;
//  height: 56px;
//  line-height: 56px;
//  font-size: 16px;
//  > div {
//    padding: 0 80px;
//    color: #212121;
//    cursor: pointer;
//  }
//  .w_active_bg {
//    color: #ffffff;
//    background: linear-gradient(180deg, #527cff 0%, #2e5ced 100%);
//  }
//}
.n-tab-pane {
  padding: 0 !important;
}

.info-bg {
  background: url('@/assets/home/<USER>') no-repeat;
  background-size: 100% 100%;
}

:deep(.n-tabs-nav) {
  height: 56px;
  line-height: 56px;
}

:deep(.n-tabs .n-tabs-rail .n-tabs-capsule) {
  background: linear-gradient(180deg, #527cff 0%, #2e5ced 100%);
  height: 56px !important;
}

:deep(.n-tabs .n-tabs-rail .n-tabs-tab-wrapper .n-tabs-tab.n-tabs-tab--active) {
  color: white;
}

// ::v-deep.n-tabs .n-tabs-rail .n-tabs-tab-wrapper .n-tabs-tab.n-tabs-tab--active {
//   background-color: #446ff9 !important;
//   color: #fff;
// }
::v-deep .n-tabs .n-tabs-rail {
  background: var(--com-container-bg);
}
</style>
