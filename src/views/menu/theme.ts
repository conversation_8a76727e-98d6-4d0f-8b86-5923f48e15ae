import { MenuProps } from 'naive-ui';

type MenuThemeOverrides = NonNullable<MenuProps['themeOverrides']>;
export const menuTheme: MenuThemeOverrides = {
  fontSize: '16px',
  itemHeight: '48px',
  itemTextColor: '#FFFFFF',
  itemTextColorHover: '#FFFFFF',
  itemTextColorActive: '#FFFFFF',
  itemColorHover: '#363d64',
  itemColorActive: '#5278EF',
  itemIconColor: '#FFFFFF',
  itemIconColorHover: '#FFFFFF',
};
