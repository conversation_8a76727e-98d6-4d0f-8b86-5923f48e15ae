<template>
  <n-layout has-sider>
    <n-layout-sider
      :class="{ [$style.wrap]: true, [$style.expand]: isExpand }"
      :collapsed-width="48"
      :collapsed="isExpand"
      :width="240"
      @collapse="setExpand(false)"
      @expand="setExpand(true)"
      bordered
      collapse-mode="width"
    >
      <div :class="$style['btn-trigger']" @click="setExpand(!isExpand)">
        <IconExpand class="mr-[8px]" :class="{ [$style['icon-expand']]: true, [$style['re']]: isExpand }" />
        <n-ellipsis class="text-[16px]" :class="{ 'w-0': isExpand }">承租方管理</n-ellipsis>
      </div>

      <n-scrollbar :style="`max-height: ${scrollMaxH}`">
        <n-menu
          v-model:value="activeKey"
          :collapsed="isExpand"
          :options="menuList"
          :inverted="isExpand"
          accordion
          :theme-overrides="menuTheme"
          @update:value="handleUpdateValue"
          :collapsed-icon-size="20"
        />
      </n-scrollbar>
    </n-layout-sider>
  </n-layout>
</template>

<script lang="ts" setup>
import { icon_expand as IconExpand } from './assets/icon/index';
import { useMenu } from '@/views/menu/useMenu';
import { computed } from 'vue';
import { useState } from '@/common/hooks/useState.ts';
import { menuTheme } from '@/views/menu/theme.ts';

const { menuList, activeKey, handleUpdateValue } = useMenu();
const [isExpand, setExpand] = useState(false);

const props = defineProps({
  headless: Boolean,
});

const scrollMaxH = computed(() => {
  return props.headless ? 'calc(100vh - 48px)' : 'calc(100vh - 64px - 48px)';
});

defineOptions({ name: 'MenuIndex' });
</script>
<style lang="scss" scoped>
.menu-item-style {
  font-size: 16px;
}

.header_btn {
  height: 42px;
  width: 100%;
  text-align: left;
  display: flex;
  align-items: center;
  padding-left: 34px;
  cursor: pointer;
  transition:
    background-color 0.3s var(--n-bezier),
    padding-left 0.3s var(--n-bezier),
    border-color 0.3s var(--n-bezier);
  // background-color: #313652 !important;
  background-color: rgb(54, 61, 100) !important;
}

.retract {
  padding-left: 14px;
}
</style>
<style module lang="scss">
.wrap {
  background: #252843 !important;
  color: white !important;

  &.expand {
    background: var(--com-primary-color);
  }
}

:global(.n-menu-item-content) {
  padding-left: 14px !important;
}

.btn-trigger {
  @apply w-full h-[48px] flex items-center justify-start pl-[18px] text-[20px];

  .icon-expand {
    @apply cursor-pointer;

    &.re {
      transform-origin: center center;
      transform: rotate(-180deg);
      color: #fff;
    }
  }
}
</style>
<style scoped lang="scss">
:deep(.n-layout-sider-scroll-container) {
  overflow: hidden !important;
}

:deep(.n-menu .n-submenu .n-submenu-children) {
  background-color: #363d64;
}

/* :deep(.n-menu .n-menu-item) {
  height: 48px;
}
:deep(.n-menu .n-submenu .n-menu-item-content) {
  height: 48px;
} */
:deep(.n-menu .n-menu-item-content.n-menu-item-content--selected) {
  &::before {
    background: #5278ef;
  }

  &::after {
    content: '';
    position: absolute;
    right: 0;
    border-top: 8px solid transparent;
    border-bottom: 8px solid transparent;
    border-right: 8px solid #c8d5ff;
  }
}

:deep(.n-menu .n-menu-item-content .n-menu-item-content__arrow) {
  transform: rotate(0deg);
  color: #fff;
}

:deep(.n-menu .n-menu-item-content.n-menu-item-content--collapsed .n-menu-item-content__arrow) {
  transform: rotate(-90deg);
  color: #fff;
}
</style>
