import type { IMenu } from '@/views/menu/type';

/**
 * 菜单 - 支队、大队
 */
export const menuDataList: IMenu[] = [
  {
    label: '首页',
    key: 'home', // 一般同路由名
    icon: 'sy', // 图标名
    routeName: 'home', // 路由名称
  },
  {
    label: '承租方管理',
    key: 'tenantManagement',
    icon: 'sy',
    routeName: 'tenantManagement',
  },
  {
    label: '安全合规检查',
    key: 'safetyExamine', // 一般同路由名
    icon: 'sy', // 图标名
    routeName: 'safetyExamine', // 路由名称
  },
  {
    label: '隐患整改督办',
    key: 'supervise', // 一般同路由名
    icon: 'sy', // 图标名
    routeName: 'supervise', // 路由名称
  },
  {
    label: '基础配置',
    key: 'baseSet', // 一般同路由名
    icon: 'sy', // 图标名
    routeName: 'baseSet', // 路由名称
  },
  // {
  //   label: '承租方管理',
  //   key: 'tenantManagement',
  //   icon: 'czfgl',
  //   routeName: 'tenantManagement',
  // },
];
