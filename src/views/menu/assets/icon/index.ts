import { iconBase } from '@/utils/svg.ts';

export const icon_expand = (props: any) =>
  iconBase(
    props,
    `<svg fill="none" width="20" height="20" viewBox="0 0 20 20"><defs><clipPath id="a"><rect width="20" height="20" rx="0"/></clipPath></defs><g clip-path="url(#a)"><path d="M1.115 10.388l4.228 3.027c.**************.285.02a.277.277 0 0 0 .147-.249v-6.05a.277.277 0 0 0-.147-.248.271.271 0 0 0-.285.02L1.115 9.935a.28.28 0 0 0 0 .452zM8.125 6.8h10.083q.792 0 .792.8t-.792.8H8.125q-.792 0-.792-.8t.792-.8zm0 4.8h10.083q.792 0 .792.8t-.792.8H8.125q-.792 0-.792-.8t.792-.8zm-6.333 4.8h16.416q.792 0 .792.8t-.792.8H1.792Q1 18 1 17.2t.792-.8zm0-14.4h16.416Q19 2 19 2.8t-.792.8H1.792Q1 3.6 1 2.8t.792-.8z" fill="#606266"/></g></svg>`
  );

export const icon_sy = (props: any) =>
  iconBase(
    props,
    `<svg  fill="none"  width="20" height="20" viewBox="0 0 20 20"><g><g><path d="M11.5,17L11.5,13.6766C11.5,13.1521,11.05229,12.72701,10.5,12.72701L9.5,12.72701C8.94771,12.72701,8.5,13.1521,8.5,13.6766L8.5,17L4,17C3.447715,17,3,16.5749,3,16.0504L3,8.2812C3.0000698853,7.98449,3.1462,7.70487,3.395,7.52536L9.395,3.193495C9.75258,2.9355018,10.24742,2.9355018,10.605,3.193495L16.605,7.52536C16.8539,7.705,17,7.98462,17,8.2812L17,16.0504C17,16.5749,16.552300000000002,17,16,17L11.5,17Z" fill="#606266" fill-opacity="1"/></g></g></svg>`
  );

export const icon_jcpz = (props: any) =>
  iconBase(
    props,
    `<svg  fill="none" version="1.1" width="20" height="20" viewBox="0 0 20 20"><defs><clipPath id="master_svg0_1_8384"><rect x="0" y="0" width="20" height="20" rx="0"/></clipPath></defs><g clip-path="url(#master_svg0_1_8384)"><g><path d="M17.26914,13.38107L11.730858,13.38107C11.324826,13.38107,11,13.07417,11,12.690536999999999C11,12.306905,11.324826,12,11.730858,12L17.26914,12C17.67517,12,18,12.306905,18,12.690536999999999C18,13.07417,17.67517,13.38107,17.26914,13.38107ZM17.26914,15.69821L11.730858,15.69821C11.324826,15.69821,11,15.391300000000001,11,15.007670000000001C11,14.62404,11.324826,14.31714,11.730858,14.31714L17.26914,14.31714C17.67517,14.31714,18,14.62404,18,15.007670000000001C18,15.391300000000001,17.67517,15.69821,17.26914,15.69821ZM17.26914,18L11.730858,18C11.324826,18,11,17.693089999999998,11,17.30946C11,16.92583,11.324826,16.61893,11.730858,16.61893L17.26914,16.61893C17.67517,16.61893,18,16.92583,18,17.30946C18,17.693089999999998,17.67517,18,17.26914,18Z" fill="#606266" fill-opacity="1"/></g><g><path d="M17.8602,7.19424L16.4621,4.82156C16.1857,4.37268,15.6005,4.19633,15.1291,4.4368099999999995L13.9911,4.88569C13.5684,4.56506,13.0969,4.30855,12.593,4.11617L12.4304,2.945865C12.3979,2.4007899999999998,11.95895,2,11.42248,2L8.593779999999999,2C8.0573,2,7.61836,2.4007899999999998,7.58585,2.929834L7.42328,4.10014C6.93557,4.30855,6.46412,4.56506,6.02519,4.86966L4.88721,4.42077C4.415760000000001,4.1803,3.8305100000000003,4.35665,3.5541400000000003,4.80553L2.1397909999999998,7.19424C1.87968,7.64313,2.0259924,8.2363,2.481185,8.50884L3.44034,9.230260000000001C3.37531,9.743269999999999,3.37531,10.27231,3.44034,10.78533L2.464929,11.50675C2.00973549,11.77929,1.863423,12.3725,2.1397909999999998,12.8213L3.5541400000000003,15.194C3.8305100000000003,15.6429,4.415760000000001,15.8193,4.90346,15.5788L6.02519,15.1299C6.44787,15.4505,6.91932,15.707,7.40702,15.8994L7.56959,17.069699999999997C7.60211,17.5988,8.041039999999999,18.0156,8.57752,17.9996L9.69925,17.9996C9.86182,17.9996,9.97561,17.871299999999998,9.991869999999999,17.727L9.991869999999999,17.117800000000003L9.991869999999999,13.2542C8.18736,13.2702,6.70798,11.81135,6.70798,10.01581C6.70798,8.22027,8.18736,6.76139,10.00813,6.76139C11.8289,6.76139,13.3083,8.22027,13.3083,10.01581C13.3083,10.25628,13.2758,10.49676,13.227,10.73723L16.3808,10.73723C16.4946,10.73723,16.5922,10.64104,16.6084,10.52882C16.640900000000002,10.09597,16.640900000000002,9.67915,16.5759,9.24629L17.5351,8.52487C17.990299999999998,8.2363,18.1366,7.64313,17.8602,7.19424Z" fill="#606266" fill-opacity="1"/></g></g></svg>`
  );

export const icon_yhzgdb = (props: any) =>
  iconBase(
    props,
    `<svg fill="none" version="1.1" width="20" height="20" viewBox="0 0 20 20"><defs><clipPath id="master_svg0_1_366"><rect x="0" y="0" width="20" height="20" rx="0"/></clipPath></defs><g clip-path="url(#master_svg0_1_366)"><g><path d="M9.234374375,15.0311C9.234374375,13.6357,9.927734375,12.3685,11.013674375,11.56133C10.951174375,11.59102,10.878904375,11.60957,10.806644375,11.60957L5.791014375,11.60957C5.542964375,11.60957,5.335934375,11.41289,5.335934375,11.17725C5.335934375,10.9416,5.542964375,10.74492,5.791014375,10.74492L10.796874375,10.74492C11.044924375,10.74492,11.251954375,10.9416,11.251954375,11.17725C11.251954375,11.296,11.201174375,11.39434,11.126954375,11.47227C11.851564375,10.96201,12.740184375,10.66699,13.703084375,10.66699C14.902384375,10.66699,16.164084375,11.02139,16.972684375,11.74873L16.972684375,2.875879C16.972684375,2.491797,16.652384375,2.1875,16.248084374999998,2.1875L3.183593375,2.1875C2.779297375,2.1875,2.458984375,2.491797,2.458984375,2.875879L2.458984375,17.6937C2.458984375,18.0777,2.779297375,18.382,3.183593375,18.382L10.878904375,18.382C9.833984375,17.604599999999998,9.234374375,16.3967,9.234374375,15.0311ZM5.791014375,5.41045L13.589884375,5.41045C13.837884375,5.41045,14.044884375,5.60713,14.044884375,5.84277C14.044884375,6.0784199999999995,13.837884375,6.2751,13.589884375,6.2751L5.791014375,6.2751C5.542964375,6.2751,5.335934375,6.0784199999999995,5.335934375,5.84277C5.335934375,5.60713,5.542964375,5.41045,5.791014375,5.41045ZM5.791014375,8.08232L13.589884375,8.08232C13.837884375,8.08232,14.044884375,8.279,14.044884375,8.51465C14.044884375,8.75029,13.837884375,8.94697,13.589884375,8.94697L5.791014375,8.94697C5.542964375,8.94697,5.335934375,8.75029,5.335934375,8.51465C5.335934375,8.279,5.542964375,8.08232,5.791014375,8.08232ZM11.126954375,11.47412L11.001954375,11.56318C11.054684375,11.54277,11.097654375,11.51309,11.126954375,11.47412ZM13.900384375,11.27744C11.718754375,11.27744,9.939454375,12.9585,9.939454375,15.0403C9.939454375,17.1129,11.708984375,18.8032,13.900384375,18.8032C16.081984374999998,18.8032,17.861284375,17.1222,17.861284375,15.0403C17.861284375,12.9585,16.081984374999998,11.27744,13.900384375,11.27744ZM15.994184375,14.6544L15.546884375,14.8641C15.406284375,14.9383,15.287084375,15.044,15.201184375,15.1739L14.238284375,16.7863C14.226584375,16.8086,14.212884375,16.8271,14.193384375,16.842C14.119184375,16.9032,14.005884375,16.8958,13.941384375,16.8234L13.220684375,16.1351L11.699214375,17.073900000000002L12.728484375,15.6675L12.101564375,15.0682C12.080074375,15.0515,12.064454375,15.0292,12.052734375,15.0051C12.017574375,14.9216,12.060544375,14.827,12.146484375,14.7936L13.900384375,13.9308C14.037084375,13.8547,14.144484375,13.7378,14.208984375,13.6005L14.398484375,13.1181C14.406284375,13.0847,14.425784375,13.055,14.455084375,13.0346C14.519484375,12.9882,14.611284375,13.0012,14.660184375,13.0624L16.042984375,14.3816C16.066384375,14.4002,16.081984374999998,14.4225,16.093784375,14.4484C16.123084374999998,14.5319,16.080084375,14.6229,15.994184375,14.6544Z" fill="#606266" fill-opacity="1" style="mix-blend-mode:passthrough"/></g></g></svg>`
  );

export const icon_czfgl = (props: any) =>
  iconBase(
    props,
    `<svg fill="none" version="1.1" width="20" height="20" viewBox="0 0 20 20"><defs><clipPath id="master_svg0_1_362"><rect x="0" y="0" width="20" height="20" rx="0"/></clipPath></defs><g clip-path="url(#master_svg0_1_362)"><g><path d="M3.1428599999999998,2L16.857100000000003,2C17.488300000000002,2,18,2.511675,18,3.1428599999999998L18,7.71429C18,8.345469999999999,17.488300000000002,8.857140000000001,16.857100000000003,8.857140000000001L3.1428599999999998,8.857140000000001C2.511675,8.857140000000001,2,8.345469999999999,2,7.71429L2,3.1428599999999998C1.9999999247572,2.511675,2.511675,2,3.1428599999999998,2ZM4.857139999999999,4.28571C4.54155,4.28571,4.28571,4.54155,4.28571,4.857139999999999C4.28571,5.17273,4.54155,5.428570000000001,4.857139999999999,5.428570000000001L8.28571,5.428570000000001C8.60115,5.42835,8.85675,5.17258,8.85675,4.857139999999999C8.85675,4.54171,8.60115,4.2859300000000005,8.28571,4.28571L4.857139999999999,4.28571ZM3.1428599999999998,11.14286L16.857100000000003,11.14286C17.488300000000002,11.14286,18,11.65453,18,12.2857L18,16.857100000000003C18,17.488300000000002,17.488300000000002,18,16.857100000000003,18L3.1428599999999998,18C2.511675,18,2,17.488300000000002,2,16.857100000000003L2,12.2857C1.9999999247572,11.65453,2.511675,11.14286,3.1428599999999998,11.14286ZM4.857139999999999,13.4286C4.54155,13.4286,4.28571,13.6844,4.28571,14C4.28571,14.3156,4.54155,14.5714,4.857139999999999,14.5714L8.28571,14.5714C8.60131,14.5714,8.857140000000001,14.3156,8.857140000000001,14C8.857140000000001,13.6844,8.60131,13.4286,8.28571,13.4286L4.857139999999999,13.4286Z" fill="#FFFFFF" fill-opacity="1" style="mix-blend-mode:passthrough"/></g></g></svg>`
  );

export const icon_yjssgl = (props: any) =>
  iconBase(
    props,
    `<svg fill="none" version="1.1" width="20" height="20" viewBox="0 0 20 20"><defs><clipPath id="master_svg0_1_355"><rect x="0" y="0" width="20" height="20" rx="0"/></clipPath></defs><g clip-path="url(#master_svg0_1_355)"><g><path d="M6.916797573242187,16.356248823242186Q6.916797573242187,16.42619882324219,6.909937573242187,16.495808823242186Q6.903077573242188,16.565418823242187,6.889437573242187,16.634018823242187Q6.875787573242187,16.702628823242186,6.855487573242188,16.76955882324219Q6.835177573242188,16.836498823242188,6.808407573242187,16.901118823242186Q6.7816475732421875,16.965748823242187,6.748667573242187,17.027438823242186Q6.7156975732421875,17.08911882324219,6.676837573242187,17.14727882324219Q6.637977573242187,17.205438823242186,6.593597573242187,17.25951882324219Q6.549227573242188,17.31358882324219,6.499767573242187,17.36304882324219Q6.4503075732421875,17.412508823242188,6.396237573242187,17.45687882324219Q6.342157573242187,17.50125882324219,6.283997573242187,17.54011882324219Q6.225837573242187,17.57897882324219,6.164157573242187,17.611948823242187Q6.102467573242187,17.64492882324219,6.037837573242188,17.67168882324219Q5.973217573242188,17.698458823242188,5.906277573242187,17.718768823242186Q5.839347573242188,17.73906882324219,5.7707375732421875,17.752718823242187Q5.7021375732421875,17.766358823242186,5.632527573242188,17.77321882324219Q5.562917573242188,17.78007882324219,5.492967573242187,17.78007882324219Q5.423017573242188,17.78007882324219,5.353407573242188,17.77321882324219Q5.283797573242188,17.766358823242186,5.215187573242187,17.752718823242187Q5.146587573242187,17.73906882324219,5.0796475732421875,17.718768823242186Q5.012713573242188,17.698458823242188,4.948090573242188,17.67168882324219Q4.883466573242187,17.64492882324219,4.821777573242187,17.611948823242187Q4.760088573242188,17.57897882324219,4.701929573242188,17.54011882324219Q4.643769573242188,17.50125882324219,4.589698573242187,17.45687882324219Q4.535627573242188,17.412508823242188,4.486167573242188,17.36304882324219Q4.436706573242187,17.31358882324219,4.3923315732421875,17.25951882324219Q4.347956573242188,17.205438823242186,4.309095573242187,17.14727882324219Q4.270234573242187,17.08911882324219,4.237261573242187,17.027438823242186Q4.204287573242188,16.965748823242187,4.177519573242187,16.901118823242186Q4.150751973242188,16.836498823242188,4.130447173242188,16.76955882324219Q4.110142273242188,16.702628823242186,4.096496073242188,16.634018823242187Q4.082849773242187,16.565418823242187,4.075993693242188,16.495808823242186Q4.0691375732421875,16.42619882324219,4.0691375732421875,16.356248823242186Q4.0691375732421875,16.286298823242188,4.075993693242188,16.216688823242187Q4.082849773242187,16.147078823242186,4.096496073242188,16.078468823242186Q4.110142273242188,16.009868823242186,4.130447173242188,15.942928823242188Q4.150751973242188,15.875994823242188,4.177519573242187,15.811371823242187Q4.204287573242188,15.746747823242188,4.237261573242187,15.685058823242187Q4.270234573242187,15.623369823242188,4.309095573242187,15.565210823242188Q4.347956573242188,15.507050823242187,4.3923315732421875,15.452979823242188Q4.436706573242187,15.398908823242188,4.486167573242188,15.349448823242188Q4.535627573242188,15.299987823242187,4.589698573242187,15.255612823242188Q4.643769573242188,15.211237823242188,4.701929573242188,15.172376823242187Q4.760088573242188,15.133515823242188,4.821777573242187,15.100542823242188Q4.883466573242187,15.067568823242187,4.948090573242188,15.040800823242188Q5.012713573242188,15.014033223242187,5.0796475732421875,14.993728423242187Q5.146587573242187,14.973423523242188,5.215187573242187,14.959777323242188Q5.283797573242188,14.946131023242188,5.353407573242188,14.939274943242188Q5.423017573242188,14.932418823242188,5.492967573242187,14.932418823242188Q5.562917573242188,14.932418823242188,5.632527573242188,14.939274943242188Q5.7021375732421875,14.946131023242188,5.7707375732421875,14.959777323242188Q5.839347573242188,14.973423523242188,5.906277573242187,14.993728423242187Q5.973217573242188,15.014033223242187,6.037837573242188,15.040800823242188Q6.102467573242187,15.067568823242187,6.164157573242187,15.100542823242188Q6.225837573242187,15.133515823242188,6.283997573242187,15.172376823242187Q6.342157573242187,15.211237823242188,6.396237573242187,15.255612823242188Q6.4503075732421875,15.299987823242187,6.499767573242187,15.349448823242188Q6.549227573242188,15.398908823242188,6.593597573242187,15.452979823242188Q6.637977573242187,15.507050823242187,6.676837573242187,15.565210823242188Q6.7156975732421875,15.623369823242188,6.748667573242187,15.685058823242187Q6.7816475732421875,15.746747823242188,6.808407573242187,15.811371823242187Q6.835177573242188,15.875994823242188,6.855487573242188,15.942928823242188Q6.875787573242187,16.009868823242186,6.889437573242187,16.078468823242186Q6.903077573242188,16.147078823242186,6.909937573242187,16.216688823242187Q6.916797573242187,16.286298823242188,6.916797573242187,16.356248823242186Z" fill="#FFFFFF" fill-opacity="1" style="mix-blend-mode:passthrough"/></g><g><path d="M16.14531319824219,16.356248823242186Q16.14531319824219,16.42619882324219,16.13845319824219,16.495808823242186Q16.13160319824219,16.565418823242187,16.117953198242187,16.634018823242187Q16.10430319824219,16.702628823242186,16.084003198242186,16.76955882324219Q16.063693198242188,16.836498823242188,16.03692319824219,16.901118823242186Q16.01016319824219,16.965748823242187,15.977183198242187,17.027438823242186Q15.944213198242188,17.08911882324219,15.905353198242187,17.14727882324219Q15.866493198242187,17.205438823242186,15.822113198242187,17.25951882324219Q15.777743198242188,17.31358882324219,15.728283198242188,17.36304882324219Q15.678823198242188,17.412508823242188,15.624753198242187,17.45687882324219Q15.570683198242188,17.50125882324219,15.512513198242187,17.54011882324219Q15.454363198242188,17.57897882324219,15.392673198242187,17.611948823242187Q15.330983198242187,17.64492882324219,15.266353198242188,17.67168882324219Q15.201733198242188,17.698458823242188,15.134793198242187,17.718768823242186Q15.067863198242188,17.73906882324219,14.999253198242187,17.752718823242187Q14.930653198242187,17.766358823242186,14.861043198242188,17.77321882324219Q14.791433198242188,17.78007882324219,14.721483198242188,17.78007882324219Q14.651533198242188,17.78007882324219,14.581923198242187,17.77321882324219Q14.512313198242188,17.766358823242186,14.443703198242188,17.752718823242187Q14.375103198242188,17.73906882324219,14.308163198242188,17.718768823242186Q14.241230198242187,17.698458823242188,14.176606198242187,17.67168882324219Q14.111982198242188,17.64492882324219,14.050293198242187,17.611948823242187Q13.988605198242187,17.57897882324219,13.930445198242188,17.54011882324219Q13.872285198242187,17.50125882324219,13.818214198242188,17.45687882324219Q13.764144198242187,17.412508823242188,13.714683198242188,17.36304882324219Q13.665222198242187,17.31358882324219,13.620847198242188,17.25951882324219Q13.576473198242187,17.205438823242186,13.537611198242187,17.14727882324219Q13.498750198242188,17.08911882324219,13.465777198242188,17.027438823242186Q13.432804198242188,16.965748823242187,13.406035198242188,16.901118823242186Q13.379267698242188,16.836498823242188,13.358962798242187,16.76955882324219Q13.338657898242188,16.702628823242186,13.325011698242188,16.634018823242187Q13.311365398242188,16.565418823242187,13.304509318242188,16.495808823242186Q13.297653198242188,16.42619882324219,13.297653198242188,16.356248823242186Q13.297653198242188,16.286298823242188,13.304509318242188,16.216688823242187Q13.311365398242188,16.147078823242186,13.325011698242188,16.078468823242186Q13.338657898242188,16.009868823242186,13.358962798242187,15.942928823242188Q13.379267698242188,15.875994823242188,13.406035198242188,15.811371823242187Q13.432804198242188,15.746747823242188,13.465777198242188,15.685058823242187Q13.498750198242188,15.623369823242188,13.537611198242187,15.565210823242188Q13.576473198242187,15.507050823242187,13.620847198242188,15.452979823242188Q13.665222198242187,15.398908823242188,13.714683198242188,15.349448823242188Q13.764144198242187,15.299987823242187,13.818214198242188,15.255612823242188Q13.872285198242187,15.211237823242188,13.930445198242188,15.172376823242187Q13.988605198242187,15.133515823242188,14.050293198242187,15.100542823242188Q14.111982198242188,15.067568823242187,14.176606198242187,15.040800823242188Q14.241230198242187,15.014033223242187,14.308163198242188,14.993728423242187Q14.375103198242188,14.973423523242188,14.443703198242188,14.959777323242188Q14.512313198242188,14.946131023242188,14.581923198242187,14.939274943242188Q14.651533198242188,14.932418823242188,14.721483198242188,14.932418823242188Q14.791433198242188,14.932418823242188,14.861043198242188,14.939274943242188Q14.930653198242187,14.946131023242188,14.999253198242187,14.959777323242188Q15.067863198242188,14.973423523242188,15.134793198242187,14.993728423242187Q15.201733198242188,15.014033223242187,15.266353198242188,15.040800823242188Q15.330983198242187,15.067568823242187,15.392673198242187,15.100542823242188Q15.454363198242188,15.133515823242188,15.512513198242187,15.172376823242187Q15.570683198242188,15.211237823242188,15.624753198242187,15.255612823242188Q15.678823198242188,15.299987823242187,15.728283198242188,15.349448823242188Q15.777743198242188,15.398908823242188,15.822113198242187,15.452979823242188Q15.866493198242187,15.507050823242187,15.905353198242187,15.565210823242188Q15.944213198242188,15.623369823242188,15.977183198242187,15.685058823242187Q16.01016319824219,15.746747823242188,16.03692319824219,15.811371823242187Q16.063693198242188,15.875994823242188,16.084003198242186,15.942928823242188Q16.10430319824219,16.009868823242186,16.117953198242187,16.078468823242186Q16.13160319824219,16.147078823242186,16.13845319824219,16.216688823242187Q16.14531319824219,16.286298823242188,16.14531319824219,16.356248823242186Z" fill="#FFFFFF" fill-opacity="1" style="mix-blend-mode:passthrough"/></g><g><path d="M17.66230373046875,4.468157094726562C17.66230373046875,3.7843660947265625,17.10683373046875,3.2288970947265625,16.42305373046875,3.2288970947265625C15.97480373046875,3.2288970947265625,15.58105373046875,3.4679600947265623,15.36308373046875,3.8247960947265627L15.34199373046875,3.8089750947265624L9.25644763046875,11.346477094726563C9.08242433046875,11.557417094726562,9.23183833046875,11.877337094726563,9.50781473046875,11.877337094726563L13.334573730468751,11.877337094726563C13.635153730468751,11.877337094726563,13.91992373046875,11.738467094726563,14.106253730468751,11.501167094726563L17.34590373046875,5.338267094726563L17.32305373046875,5.320697094726563C17.53223373046875,5.097447094726562,17.66230373046875,4.798627094726562,17.66230373046875,4.468157094726562ZM18.21250373046875,10.808587094726562C18.13691373046875,10.808587094726562,18.07363373046875,10.868347094726563,18.07012373046875,10.942177094726564L18.064843730468752,10.942177094726564L18.064843730468752,10.945697094726562C18.064843730468752,11.309557094726562,17.75547373046875,11.603117094726562,17.38633373046875,11.576747094726562C17.07519373046875,11.555657094726563,16.82383373046875,11.304287094726563,16.80098373046875,10.993157094726563C16.77812373046875,10.687297094726564,16.97324373046875,10.423627094726562,17.247463730468752,10.341007094726564C17.298443730468748,10.325187094726562,17.331843730468748,10.277727094726561,17.331843730468748,10.224987094726561L17.331843730468748,9.695887094726562C17.553323730468747,9.695887094726562,17.73438373046875,9.516597094726563,17.73438373046875,9.293347094726563L17.73438373046875,6.449207094726562C17.73438373046875,6.215417094726563,17.43730373046875,6.115227094726563,17.29668373046875,6.3015570947265624L16.750003730468748,7.440617094726562C16.63398373046875,7.593547094726563,16.57070373046875,7.7798770947265625,16.57070373046875,7.971477094726563L16.57070373046875,9.252917094726563C16.57070373046875,9.499017094726563,16.76933373046875,9.697647094726562,17.01543373046875,9.697647094726562L17.01543373046875,10.042177094726561C17.01543373046875,10.089637094726562,16.99082373046875,10.135337094726562,16.95039373046875,10.159947094726562C16.67617373046875,10.326947094726563,16.49687373046875,10.632807094726562,16.50742373046875,10.979087094726562C16.52324373046875,11.457217094726563,16.90820373046875,11.847447094726563,17.38457373046875,11.870307094726563C17.90488373046875,11.894917094726562,18.33554373046875,11.488857094726562,18.35137373046875,10.977337094726563C18.353123730468752,10.970307094726563,18.353123730468752,10.961517094726563,18.353123730468752,10.954487094726563C18.35488373046875,10.873627094726562,18.29160373046875,10.808587094726562,18.21250373046875,10.808587094726562Z" fill="#FFFFFF" fill-opacity="1" style="mix-blend-mode:passthrough"/></g><g><path d="M17.669342407226562,12.566406875L8.839832407226563,12.566406875C8.451362407226561,12.566406875,8.136712407226563,12.251756875,8.136712407226563,11.863276875L8.136712407226563,8.294921875C8.136712407226563,7.906444875,7.8220624072265625,7.591796875,7.433582407226562,7.591796875L6.150382407226562,7.591796875C5.616002407226563,7.591796875,5.100962407226563,7.795702875,4.708972407226563,8.161327875L2.5943264072265624,10.138866875C2.3096924072265623,10.404416874999999,2.1480073632265624,10.776156875,2.1478424072265625,11.165426875L2.1478424072265625,15.589846875C2.1478424072265625,15.978316875,2.4624904072265625,16.292966874999998,2.8509674072265625,16.292966874999998L3.2078024072265627,16.292966874999998C3.2078024072265627,15.048436875,4.216792407226563,14.035936875,5.463072407226562,14.035936875C6.709362407226562,14.035936875,7.716592407226562,15.044916875,7.716592407226562,16.291206875C7.716592407226562,16.292966874999998,12.436342407226563,16.294726875000002,12.436342407226563,16.292966874999998C12.436342407226563,15.048436875,13.445342407226562,14.035936875,14.689842407226562,14.035936875C15.934342407226563,14.035936875,16.943342407226563,15.050196875000001,16.943342407226563,16.294726875000002Q16.943342407226563,16.296486875,17.669342407226562,16.292966874999998C18.057842407226563,16.292966874999998,18.37244240722656,15.978316875,18.37244240722656,15.589846875L18.37244240722656,13.269526875C18.37244240722656,12.881056874999999,18.057842407226563,12.566406875,17.669342407226562,12.566406875ZM6.234752407226562,11.938866875C6.234752407226562,12.293946875,5.948232407226563,12.580466874999999,5.593152407226563,12.580466874999999L4.503312407226563,12.580466874999999C4.148232407226562,12.580466874999999,3.8617124072265625,12.293946875,3.8617124072265625,11.938866875L3.8617124072265625,11.931836875C3.8617124072265625,11.596096875,4.004092407226563,11.274416875,4.253702407226562,11.049416875L5.130852407226563,10.258396874999999C5.366392407226563,10.045706875,5.672252407226562,9.927926875,5.990422407226562,9.927926875C6.125772407226563,9.927926875,6.234752407226562,10.036916875,6.234752407226562,10.172266875L6.234752407226562,11.938866875Z" fill="#FFFFFF" fill-opacity="1" style="mix-blend-mode:passthrough"/></g></g></svg>`
  );

export const icon_whp = (props: any) =>
  iconBase(
    props,
    `<svg fill="none" version="1.1" width="20" height="20" viewBox="0 0 20 20"><defs><clipPath id="master_svg0_1_351"><rect x="0" y="0" width="20" height="20" rx="0"/></clipPath></defs><g clip-path="url(#master_svg0_1_351)"><g><path d="M8.631270659179687,10.699987504882813C8.859560659179689,10.699987504882813,9.055380659179686,10.615767504882813,9.218790659179687,10.446457504882812C9.381920659179688,10.277397504882812,9.463630659179689,10.072657504882812,9.463630659179689,9.832457504882813C9.463630659179689,9.592417504882812,9.381900659179689,9.387717504882811,9.218790659179687,9.218937504882813C9.055400659179687,9.049967504882812,8.859560659179689,8.965347504882812,8.631270659179687,8.965347504882812C8.403050659179687,8.965347504882812,8.207200659179687,9.049947504882812,8.044010659179687,9.218937504882813C7.880920659179687,9.387737504882812,7.799220659179688,9.592397504882811,7.799220659179688,9.832457504882813C7.799220659179688,10.072657504882812,7.880920659179687,10.277397504882812,8.044010659179687,10.446437504882812C8.207200659179687,10.615767504882813,8.403030659179688,10.699987504882813,8.631270659179687,10.699987504882813ZM9.928250659179687,11.499687504882813C10.134990659179687,11.499687504882813,10.238540659179687,11.422017504882813,10.238540659179687,11.266027504882812C10.238540659179687,11.163597504882812,10.204190659179687,11.056267504882813,10.136270659179688,10.943367504882813C10.068240659179688,10.829997504882812,9.996290659179687,10.773617504882813,9.920160659179688,10.773617504882813C9.843870659179688,10.773617504882813,9.766560659179687,10.824807504882813,9.687840659179688,10.926717504882813C9.608930659179688,11.029127504882812,9.569620659179687,11.131197504882813,9.569580659179689,11.233287504882812C9.569580659179689,11.297987504882812,9.588320659179686,11.351507504882813,9.626480659179688,11.394997504882813C9.691730659179688,11.464627504882813,9.792350659179688,11.499687504882813,9.928250659179687,11.499687504882813ZM11.135590659179687,10.699987504882813C11.369560659179687,10.699987504882813,11.566700659179688,10.615767504882813,11.727150659179687,10.446457504882812C11.887450659179688,10.277397504882812,11.967850659179687,10.072657504882812,11.967850659179687,9.832457504882813C11.967850659179687,9.592417504882812,11.887450659179688,9.387717504882811,11.727150659179687,9.218937504882813C11.566660659179687,9.049967504882812,11.369540659179687,8.965347504882812,11.135590659179687,8.965347504882812C10.907390659179688,8.965347504882812,10.712940659179688,9.049947504882812,10.552510659179687,9.218937504882813C10.391900659179688,9.387737504882812,10.311690659179687,9.592397504882811,10.311710659179688,9.832437504882812C10.311710659179688,10.072617504882812,10.391900659179688,10.277377504882812,10.552510659179687,10.446437504882812C10.712960659179688,10.615767504882813,10.907390659179688,10.699987504882813,11.135590659179687,10.699987504882813ZM18.10835065917969,15.085167504882813C17.788450659179688,14.448667504882813,11.294650659179688,3.5113875048828125,10.738940659179688,2.4229015048828124C10.374310659179688,1.7078065048828126,9.504160659179687,1.7197985048828124,9.139510659179688,2.4229015048828124C8.732280659179686,3.205687504882812,2.2068086591796874,14.192167504882812,1.7526366591796876,15.119067504882812C1.4207416591796875,15.798067504882813,1.7886726591796875,16.68786750488281,2.5441596591796873,16.68786750488281C3.3116806591796877,16.68786750488281,16.784050659179687,16.68786750488281,17.31355065917969,16.68786750488281C18.00135065917969,16.68786750488281,18.511250659179687,15.881067504882813,18.10835065917969,15.085167504882813ZM6.918270659179687,9.631397504882813C7.021480659179687,8.935847504882812,7.339610659179687,8.359357504882812,7.872690659179687,7.902087504882813C8.432630659179686,7.423927504882813,9.115100659179689,7.184617504882812,9.920140659179687,7.184617504882812C10.724990659179687,7.184617504882812,11.407570659179688,7.426667504882812,11.967750659179687,7.910437504882813C12.500550659179687,8.361757504882814,12.821550659179687,8.935907504882813,12.930150659179688,9.631437504882813C12.952050659179687,9.745797504882812,12.962950659179688,9.854467504882813,12.962950659179688,9.957977504882813C12.962950659179688,10.425897504882812,12.808050659179688,10.830937504882813,12.498050659179688,11.173247504882813C12.176950659179688,11.532707504882813,11.722750659179688,11.788197504882813,11.135570659179688,11.940567504882813L11.135570659179688,12.030267504882813C11.135570659179688,12.182467504882812,11.084030659179687,12.308367504882812,10.980700659179687,12.409467504882812C10.877410659179688,12.510067504882812,10.749570659179687,12.568567504882813,10.597320659179687,12.584867504882812C10.450380659179688,12.595767504882813,10.338970659179688,12.571067504882812,10.262800659179687,12.511467504882813C10.148680659179687,12.593067504882812,10.023490659179688,12.633567504882812,9.887600659179688,12.633567504882812C9.740660659179687,12.633567504882812,9.607270659179687,12.590067504882812,9.487770659179688,12.503267504882812C9.411600659179687,12.568567504882813,9.294800659179687,12.595767504882813,9.137090659179687,12.584867504882812C8.984830659179687,12.573967504882813,8.857040659179688,12.516867504882812,8.753710659179688,12.413367504882812C8.650380659179689,12.310167504882813,8.598850659179687,12.182367504882812,8.598850659179687,12.030167504882812L8.598850659179687,11.907267504882812C8.054680659179688,11.749997504882813,7.633230659179688,11.499627504882813,7.3342306591796875,11.157037504882812C7.034970659179687,10.814217504882812,6.885510659179688,10.414767504882812,6.8854706591796875,9.957957504882813C6.885500659179687,9.854467504882813,6.896350659179688,9.745777504882813,6.918270659179687,9.631397504882813ZM12.453750659179688,14.717467504882812C12.322050659179688,14.969967504882813,12.470350659179687,15.450767504882812,12.019650659179687,15.354767504882812C11.801050659179687,15.307767504882813,11.953650659179688,14.921767504882812,11.388400659179688,14.667067504882812C11.366520659179688,14.657067504882813,11.348090659179688,14.643767504882813,11.332220659179688,14.628167504882812C10.907430659179688,14.430967504882812,10.445340659179687,14.236067504882813,9.974400659179688,14.054067504882813C9.390860659179687,14.268667504882812,8.820840659179687,14.503067504882813,8.318070659179687,14.738267504882813C8.080630659179688,14.871767504882813,7.939720659179687,14.953467504882813,7.875890659179688,15.109867504882812C7.8021606591796875,15.286667504882812,7.8404706591796876,15.592667504882812,7.530380659179688,15.423767504882813C7.197630659179688,15.243467504882812,7.405900659179688,14.799467504882813,7.3071106591796875,14.534767504882813C7.2119306591796875,14.278567504882812,6.7593206591796875,14.033767504882812,7.145220659179688,13.822767504882812C7.309970659179688,13.731667504882813,7.467040659179688,14.003867504882813,7.904570659179687,13.951067504882813C8.220830659179686,13.869467504882813,8.554700659179687,13.773067504882812,8.895370659179687,13.665767504882812C8.589360659179688,13.563967504882813,8.288880659179688,13.471167504882812,8.001980659179686,13.390767504882813C7.730360659179688,13.335567504882812,7.567190659179688,13.304567504882813,7.403170659179687,13.376567504882813C7.216650659179687,13.456767504882812,7.013170659179687,13.701667504882813,6.917380659179687,13.382367504882813C6.813870659179687,13.040267504882813,7.2990406591796875,12.853367504882813,7.427710659179687,12.599767504882813C7.552580659179688,12.354467504882813,7.411420659179687,11.889577504882812,7.848600659179687,11.983667504882812C8.036040659179687,12.023167504882812,7.943720659179688,12.319267504882813,8.298640659179688,12.560167504882813C8.805770659179688,12.810667504882813,9.386300659179687,13.062867504882812,9.982340659179687,13.294667504882813C10.427690659179687,13.131067504882813,10.865200659179688,12.955967504882812,11.271200659179687,12.777867504882812C11.282010659179688,12.765067504882813,11.294910659179687,12.754167504882812,11.310040659179688,12.745467504882813C11.662750659179688,12.539967504882812,11.860350659179687,12.462067504882812,11.942250659179688,12.262867504882813C12.019750659179687,12.078167504882812,11.977750659179687,11.763607504882813,12.297650659179688,11.938867504882813C12.637050659179687,12.126567504882813,12.423250659179688,12.582367504882813,12.525650659179687,12.855467504882812C12.621350659179688,13.121367504882812,13.091150659179688,13.373067504882812,12.694150659179687,13.591267504882813C12.501150659179688,13.696767504882812,12.319650659179688,13.321467504882813,11.720350659179687,13.498467504882813C11.696950659179688,13.505267504882813,11.673550659179687,13.507367504882813,11.650350659179688,13.506267504882812C11.456280659179688,13.561267504882812,11.257590659179687,13.620867504882812,11.056530659179687,13.684267504882813C11.243160659179688,13.746967504882813,11.427880659179687,13.806367504882813,11.608910659179687,13.861767504882813C11.626380659179688,13.859467504882813,11.643950659179687,13.859867504882812,11.661350659179687,13.863167504882812C12.070550659179688,13.940067504882812,12.271650659179688,14.010067504882812,12.481050659179688,13.918767504882812C12.676150659179687,13.835167504882813,12.883450659179687,13.581867504882812,12.981650659179687,13.911967504882812C13.084350659179687,14.263567504882813,12.586250659179688,14.455467504882812,12.453750659179688,14.717467504882812Z" fill="#FFFFFF" fill-opacity="1" style="mix-blend-mode:passthrough"/></g></g></svg>`
  );

export const icon_aqhgjc = (props: any) =>
  iconBase(
    props,
    '<svg fill="none" version="1.1" width="20" height="20" viewBox="0 0 20 20"><defs><clipPath id="master_svg0_1_347"><rect x="0" y="0" width="20" height="20" rx="0"/></clipPath></defs><g clip-path="url(#master_svg0_1_347)"><g><path d="M14.9656,2.749996C14.9656,2.335935,14.5954,2,14.1381,2C13.6807,2,13.3105,2.335919,13.3105,2.749996L13.3105,4.25C13.3105,4.66406,13.6807,5.00001,14.1381,5.00001C14.5954,5.00001,14.9656,4.66406,14.9656,4.25L14.9656,2.749996ZM10.82761,2.749996C10.82761,2.335935,10.45678,2,10.00003,2C9.54324,2,9.172419999999999,2.335935,9.172419999999999,2.749996L9.172419999999999,4.25C9.172419999999999,4.66406,9.54326,5.00001,10.00003,5.00001C10.45681,5.00001,10.82761,4.66406,10.82761,4.25L10.82761,2.749996ZM6.68958,2.749996C6.68958,2.335935,6.31927,2,5.86196,2C5.40468,2,5.03437,2.335935,5.03437,2.749996L5.03437,4.25C5.03437,4.66406,5.40468,5.00001,5.86196,5.00001C6.31927,5.00001,6.68958,4.66406,6.68958,4.25L6.68958,2.749996ZM17.1725,3.49998L15.7933,3.49998L15.7933,4.24999C15.7933,5.0786,15.0521,5.74999,14.1381,5.74999C13.224,5.74999,12.4829,5.07859,12.4829,4.24999L12.4829,3.49998L11.65526,3.49998L11.65526,4.24999C11.65526,5.0786,10.91413,5.74999,10.00007,5.74999C9.086,5.74999,8.34486,5.07859,8.34486,4.24999L8.34486,3.49998L7.51723,3.49998L7.51723,4.24999C7.51723,5.0786,6.77661,5.74999,5.86203,5.74999C4.948,5.74999,4.2068200000000004,5.07859,4.2068200000000004,4.24999L4.2068200000000004,3.49998L2.827614,3.49998C2.370308,3.49998,2,3.8359300000000003,2,4.24999L2,17.25C2,17.664099999999998,2.370308,18,2.827614,18L17.1725,18C17.6297,18,18,17.664099999999998,18,17.25L18,4.25C18,3.83594,17.6297,3.49998,17.1725,3.49998ZM6.5547,15.6029C6.5547,15.9543,6.24541,16.238799999999998,5.86481,16.238799999999998C5.483169999999999,16.238799999999998,5.17439,15.9543,5.17439,15.6029L5.17439,13.9078C5.17439,13.5569,5.483169999999999,13.2723,5.86481,13.2723C6.24541,13.2723,6.5547,13.5569,6.5547,13.9078L6.5547,15.6029ZM9.31532,15.6029C9.31532,15.9543,9.00652,16.238799999999998,8.625409999999999,16.238799999999998C8.24377,16.238799999999998,7.935,15.9543,7.935,15.6029L7.935,12.2127C7.935,11.8618,8.24377,11.57724,8.625409999999999,11.57724C9.00652,11.57724,9.31532,11.8618,9.31532,12.2127L9.31532,15.6029ZM12.0759,15.6029C12.0759,15.9543,11.76768,16.238799999999998,11.38604,16.238799999999998C11.00493,16.238799999999998,10.69615,15.9543,10.69615,15.6029L10.69615,10.5176C10.69615,10.16619,11.00493,9.88164,11.38604,9.88164C11.76768,9.88164,12.0759,10.16619,12.0759,10.5176L12.0759,15.6029ZM14.8371,15.6029C14.8371,15.9543,14.5283,16.238799999999998,14.1467,16.238799999999998C13.766,16.238799999999998,13.4568,15.9543,13.4568,15.6029L13.4568,8.82205C13.4568,8.47115,13.766,8.18658,14.1467,8.18658C14.5283,8.18658,14.8371,8.47115,14.8371,8.82205L14.8371,15.6029Z" fill="#606266" fill-opacity="1" style="mix-blend-mode:passthrough"/></g></g></svg>'
  );
