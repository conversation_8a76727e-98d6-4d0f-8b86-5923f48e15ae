<template>
  <div class="h-full w-full">
    <n-data-table class="h-full" remote striped :columns="columns" :data="tableData" :bordered="false"
      :flex-height="true" :pagination="pagination" :loading="loading" :render-cell="useEmptyCell" :scroll-x="1450" />
  </div>
</template>

<script lang="ts" setup>
import type { IPageData } from '../type'
import { ACTION, ACTION_LABEL } from '../constant'
import { cols } from './columns'
import { DataTableColumns, NButton } from 'naive-ui'
import { h, ref, toRaw, VNode, watch } from 'vue'
import { pageData } from '../fetchData'
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts'
import { useNaivePagination } from '@/common/hooks/useNaivePagination.ts'
import { IObj } from '@/types'
import { useActionDivider } from '@/common/hooks/useActionDivider.ts'
import { useEmptyCell } from '@/common/hooks/useEmptyCell.ts'

const props = defineProps({
  id: {
    type: String,
    default: '',
  },
})
const emits = defineEmits(['action'])

const [loading, search] = useAutoLoading(false)
const columns = ref<DataTableColumns>([])
const tableData = ref<IPageData[]>([])
const { pagination, updateTotal } = useNaivePagination(getTableData)

let filterData: IObj<any> = {} // 搜索条件

function getTableData() {
  const params = {
    startTime: filterData.datetimeValue[0],
    endTime: filterData.datetimeValue[1],
    pageNo: pagination.page,
    pageSize: pagination.pageSize,
    ...filterData,
    // unitId: props.id,
  };

  search(pageData(params)).then((res) => {
    tableData.value = res.data.rows || []
    updateTotal(res.data.total || 0)
  })
  // tableData.value = [{}];
  // updateTotal(1);
}

function getTableDataWrap(data: IObj<any>) {
  if (data.datetimeValue && data.datetimeValue.length > 0) {
    data.startTime = data.datetimeValue[0]
    data.endTime = data.datetimeValue[1]
  }
  filterData = Object.assign({}, data) || {}
  pagination.page = 1
  getTableData()
}

function setColumns() {
  columns.value.push(...cols)

  // 添加操作栏 action
  columns.value.push({
    title: '操作',
    key: 'actions',
    align: 'center',
    fixed: 'right',
    width: 150,
    render(row) {
      return getActionBtn(row)
    },
  })
}

function getActionBtn(row: any) {
  const acList: [VNode, boolean?][] = [
    [
      h(
        NButton,
        {
          size: 'small',
          type: 'primary',
          ghost: true,
          class: 'com-action-button',
          onClick: () =>
            emits('action', {
              action: ACTION.DETAIL,
              data: row,
            }),
        },
        { default: () => '详情' }
      ),
    ],
    [
      h(
        NButton,
        {
          size: 'small',
          type: 'primary',
          ghost: true,
          class: 'com-action-button',
          onClick: () => {
            emits('action', {
              action: ACTION.URGE,
              data: row,
            })
          },
        },
        { default: () => '催促' }
      ),
    ],
  ]

  return useActionDivider(acList)
}

// watch(
//   () => props.id,
//   (newVal: string) => {
//     if (newVal) {
//       getTableData();
//     }
//   }
// );

// on created
setColumns()

defineExpose({
  getTableDataWrap,
  getTableData,
})

defineOptions({ name: 'DemoJurisdictionTable' })
</script>

<style module lang="scss"></style>
