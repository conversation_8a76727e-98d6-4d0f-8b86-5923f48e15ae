import { DataTableColumn } from 'naive-ui';
import { disposeState } from '@/components/table-col/disposeState';
import { h } from 'vue';

export const cols: DataTableColumn[] = [
  {
    title: '序号',
    key: 'index',
    align: 'left',
    width: 65,
    render: (_: any, index: number) => {
      return index + 1;
    },
  },
  {
    title: '项目经营单位',
    key: 'projectManagementUnitName',
    align: 'left',
    width: 170,
    ellipsis: {
      tooltip: true,
    },
    render: (row: any) => {
      return row.projectManagementUnitName ? row.projectManagementUnitName : '--';
    },
  },
  {
    title: '隐患单位',
    key: 'tenantryName',
    align: 'left',
    width: 170,
    ellipsis: {
      tooltip: true,
    },
    render: (row: any) => {
      return row.tenantryName ? row.tenantryName : '--';
    },
  },
  {
    title: '隐患来源',
    key: 'hazardSourceName',
    align: 'left',
    width: 130,
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '隐患位置',
    key: 'hazardPosition',
    align: 'left',
    width: 130,
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '隐患描述',
    key: 'hazardDesc',
    align: 'left',
    width: 140,
    ellipsis: {
      tooltip: true,
    },
  },

  {
    title: '隐患类别',
    key: 'hazardTypeName',
    align: 'left',
    width: 140,
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '隐患级别',
    key: 'hazardLevelName',
    align: 'left',
    width: 100,
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '上报时间',
    key: 'createTime',
    align: 'left',
    width: 170,
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '整改状态',
    key: 'disposeStateName',
    align: 'left',
    width: 120,
    render: (row) => h(disposeState, { row }),
  },
  {
    title: '是否超期',
    key: 'timeoutDays',
    align: 'left',
    width: 100,
    ellipsis: {
      tooltip: true,
    },
    render: (row: any) => {
      if (row.timeoutDays == 0) return '否';
      if (row.timeoutDays) return `超期${row.timeoutDays}天`;
    },
  },
];
