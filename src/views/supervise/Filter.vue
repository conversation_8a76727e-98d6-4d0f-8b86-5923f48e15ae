<template>
  <div>
    <n-form :show-feedback="false" label-placement="left" label-width="100px">
      <n-grid x-gap="12" :y-gap="8" :cols="4">
        <n-gi>
          <n-form-item label="隐患单位:">
            <n-select v-model:value="filterForm.unitId" :options="tenantInfoOptions" placeholder="请选择" clearable
                      filterable />
          </n-form-item>
        </n-gi>
        <n-gi>
          <n-form-item label="隐患级别:">
            <n-select v-model:value="filterForm.hazardLevel" :options="gradeOptions" placeholder="请选择" clearable
                      filterable />
          </n-form-item>
        </n-gi>
        <n-gi>
          <n-form-item>
            <n-input v-model:value="filterForm.likeFieldValue" show-count :maxlength="50" placeholder="请输入隐患单位/隐患类别/级别" />
          </n-form-item>
        </n-gi>
        <n-gi>
          <n-form-item label="整改状态:">
            <n-select v-model:value="filterForm.disposeState" :options="disposeStateOptions" placeholder="请选择" />
          </n-form-item>
        </n-gi>
        <n-gi>
          <n-form-item label="是否超期:">
            <n-select v-model:value="filterForm.timeoutDays" :options="timeout" placeholder="请选择" clearable filterable />
          </n-form-item>
        </n-gi>
        <n-gi :span="2">
          <n-form-item label="上报时间">
            <n-date-picker v-model:formatted-value="filterForm.datetimeValue" value-format="yyyy-MM-dd" type="daterange"
                           clearable />
          </n-form-item>
        </n-gi>
      </n-grid>
    </n-form>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, ref, watch } from 'vue'
import { trimObjNull } from '@/utils/obj.ts'
import { ACTION } from './constant.ts'
import { getDefaultDateRange } from '@/utils/getDefaultDateRange.ts'
import {
  hazardDisposeSuperviseLevelGradeList,
  hazardDisposeSuperviseLevelQueryUserTenantInfoList,
  hazardDisposeSuperviseLevelTimeoutPage,
} from '@/views/supervise/fetchData.ts'
import { hazardDisposeSuperviseGetOrgTree } from '@/views/safetyExamine/fetchData.ts'
import { useAuthStore } from '@/store/modules'

const props = defineProps({
  id: {
    type: String,
    default: '',
  },
})
const emits = defineEmits(['action'])
const defaultDateRange = getDefaultDateRange()
const store = useAuthStore()
const OrgTree = ref<any>([])
const filterForm = ref({
  unitId: null,
  likeFieldValue: '',
  hazardLevel: null,
  disposeState: null,
  timeoutDays: null,
  datetimeValue: defaultDateRange,
  likeFields: 'unitName,hazardTypeName,hazardLevelName',
})

const disposeStateOptions = [
  { label: '待整改', value: 0 },
  { label: '已整改', value: 1 },
  { label: '整改中', value: 2 },
]
const gradeOptions = ref([])

const tenantInfoOptions = ref([])
const timeout = ref<any>([])

function getFilterForm() {
  return trimObjNull({ ...filterForm.value, unitId: props.id ? props.id : store.userInfo.unitId })
}

const getListGrade = () => {
  hazardDisposeSuperviseLevelGradeList({
    unitId: filterForm.value.unitId || store.userInfo.unitId,
  }).then((res: any) => {
    gradeOptions.value = res.data.map((item: any) => {
      return {
        label: item.gradeName,
        value: item.id,
      }
    })
  })
}
const tenantInfoList = (value: any) => {
  hazardDisposeSuperviseLevelQueryUserTenantInfoList({ unitId: value }).then((res: any) => {
    tenantInfoOptions.value = res.data.map((item: any) => {
      return {
        label: item.tenantryName,
        value: item.tenantryId,
      }
    })
  })
}
const timeoutPage = () => {
  hazardDisposeSuperviseLevelTimeoutPage({
    pageNo: 1,
    pageSize: 1000,
  }).then((res) => {
    timeout.value = res.data.rows.map((item: any) => {
      return {
        label: item.overdueDay + '天',
        value: item.overdueDay,
      }
    })
    timeout.value.unshift({ label: '否', value: 0 })
  })
}
const getOrgTree = () => {
  hazardDisposeSuperviseGetOrgTree({
    type: '2',
  }).then((res) => {
    OrgTree.value = res.data
  })
}
const handleUpdateValue = (value: any) => {
  tenantInfoList(value)
}

function doHandle(action: string) {
  emits('action', {
    action: action,
    data: getFilterForm(),
  })
}

getOrgTree()
getListGrade()
timeoutPage()
handleUpdateValue(filterForm.value.unitId || store.userInfo.unitId)

onMounted(() => {
  doHandle(ACTION.SEARCH)
})

watch(filterForm.value, () => {
  doHandle(ACTION.SEARCH);
});

watch(
  () => props.id,
  (newVal: string) => {
    if (newVal) {
      handleUpdateValue(newVal)
      doHandle(ACTION.SEARCH);
    }
  }
);
// watch(
//   filterForm.value
//   () => {
//     loadFloorData();
//   },
//   {
//     deep: true,
//   }
// );

defineOptions({ name: 'SafetyExamineFilter' })
</script>

<style module lang="scss"></style>
