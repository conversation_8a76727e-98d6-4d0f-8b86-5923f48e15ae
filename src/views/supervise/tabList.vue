<template>
  <n-scrollbar :x-scrollable="true" style="height: 110px">
    <div class="head">
      <div>
        <div class="w_box-item-danger bgc">
          <div class="flex">
            <img :src="imgUrl" alt="" />
            <div class="box-item-right">
              <div class="box-context">隐患数量</div>
              <div class="box-title" style="color: #ff9500">{{ list.total || 0 }}</div>
            </div>
          </div>
        </div>
      </div>
      <div>
        <div class="box-item bg1">
          <div class="box-context">已整改</div>
          <div class="box-title" style="color: #00b578">{{ list.disposedNum || 0 }}</div>
        </div>
      </div>
      <div>
        <div class="box-item bg1">
          <div class="box-context">未整改</div>
          <div class="box-title" style="color: #fa5151">{{ list.unDisposedNum + list.disposingNum || 0 }}</div>
        </div>
      </div>
      <div>
        <div class="box-item bg2">
          <div class="box-context">超期数量</div>
          <div class="box-title" style="color: #ff9500">{{ list.timeout || 0 }}</div>
        </div>
      </div>
      <div>
        <div class="box-item-more bg3">
          <div class="box-item" style="padding: 0px" v-for="(item, index) in levelList" :key="index">
            <div class="box-context">{{ item.hazardLevelName }}</div>
            <div class="box-title" :style="{ color: colorList[index % 4] }">{{ item.total || 0 }}</div>
          </div>

          <!--        <div class="box-title">-->
          <!--          <span v-for="(item,index) in levelList" :key="index">{{item.total || 0}}-->
          <!--            <span v-if="index!=levelList.length-1">/</span>-->
          <!--          </span>-->
          <!--        </div>-->
          <!--        <div class="box-context">-->
          <!--          <span v-for="(item,index) in levelList" :key="index">-->
          <!--            {{item.hazardLevelName}}<span v-if="index!=levelList.length-1">/</span>-->
          <!--          </span>-->
          <!--        </div>-->
        </div>
      </div>
    </div>
  </n-scrollbar>
</template>
<script setup lang="ts">
import imgUrl from '@/assets/icon1.png'

import { hazardDisposeSuperviseLevelStatistics, hazardDisposeSuperviseStatistics } from '@/views/supervise/fetchData.ts'
import { ref } from 'vue'
import { useAuthStore } from '@/store/modules'
const list = ref({})
const levelList = ref([])
const getList = (e = {}) => {
  if (e.datetimeValue && e.datetimeValue.length > 0) {
    e.startTime = e.datetimeValue[0]
    e.endTime = e.datetimeValue[1]
  }
  hazardDisposeSuperviseStatistics(e).then((res) => {
    list.value = res.data;
  });
  hazardDisposeSuperviseLevelStatistics(e).then((res) => {
    levelList.value = res.data;
  });
};
const getLevelList = ({ startTime, endTime, unitId }) => {
  // hazardDisposeSuperviseLevelStatistics({ unitId, startTime, endTime }).then((res) => {
  //   levelList.value = res.data
  // })
};
const colorList = ['#e23b50', '#f59a23', '#bfbf00', '#76b90e'];
defineExpose({ getLevelList, getList });
</script>

<style scoped lang="scss">
.head {
  display: flex;
}

.box-item {
  height: 80px;
  display: flex;
  margin-right: 20px;
  box-sizing: border-box;
  padding: 0px 80px 0px 20px;
  justify-content: center;
  align-items: flex-start;
  flex-direction: column;
  font-size: 15px;

  .box-context {
    margin-bottom: 10px;
    white-space: nowrap;
  }

  .box-title {
    font-size: 20px;
  }
}

.bgc {
  background: linear-gradient(180deg, #ffffff 0%, #e1e7fa 99%);
  box-shadow: 0px 2px 0px 0px rgba(0, 20, 82, 0.18);
  border-radius: 8px;
}

.w_box-item-danger {
  height: 80px;
  box-sizing: border-box;
  font-size: 15px;
  padding: 10px 20px 0;
  border-radius: 10px;
  //display: flex;
  margin-right: 20px;

  img {
    height: 64px;
    width: 64px;
  }

  .box-item-right {
    padding-left: 10px;
    display: flex;
    justify-content: center;
    align-items: flex-start;
    flex-direction: column;
    padding-right: 10px;
    //background: red;
    width: 140px;

    .box-context {
      margin-bottom: 10px;
      white-space: nowrap;
    }

    .box-title {
      font-size: 20px;
    }
  }
}

.box-item-more {
  display: flex;
  padding: 0px 80px 0px 20px;
}

.bg1 {
  background-size: 100% 100%;
  background-image: url('@/assets/bg1.png');
}

.bg2 {
  background-size: 100% 100%;
  background-image: url('@/assets/bg2.png');
}

.bg3 {
  background-size: 100% 100%;
  background-image: url('@/assets/bg3.png');
}
</style>
