<template>
  <div :class="{ [$style.videoWrap]: true }">
    <ComBread :data="breadData" />
    <div class="w-full h-full flex overflow-hidden">
      <div ref="siderRef" class="h-full bg-[#EEF7FF] rounded-[12px] mr-[16px]"
        :style="`width:${collapsed ? toVw(0) : toVw(346)};`">
        <n-layout has-sider style="height: 100%">
          <n-layout-sider bordered collapse-mode="width" :collapsed-width="0" width="95%" :collapsed="collapsed"
            @collapse="collapsed = true" @expand="collapsed = false">
            <TreeModle @treeChange="fn" />
          </n-layout-sider>
        </n-layout>
      </div>

      <div class="bg-[#EEF7FF] rounded-[12px] com-g-row-aaa1 relative"
        :style="`width: calc(100% - ${toVw(collapsed ? 15 : 346)} );`">
        <!--        <div class="com-g-row-aa1 com-table-container gap-y-[20px]">-->
        <div class="w_table_box">
          <TableList ref="tableListRef" :id="unitIds" />
          <filter-comp @action="actionFn" class="mt-5 mb-5" :id="unitIds"></filter-comp>
          <Table ref="tableCompRef" @action="actionFn" :id="unitIds" class="w_table"></Table>
          <div class="img-switch" @click="collapsed = !collapsed"></div>
        </div>
      </div>
    </div>
    <hazardDetails ref="hazardDetailsRef" />
  </div>
</template>
<script setup lang="ts">
import ComBread from '@/components/breadcrumb/ComBread.vue'
import TableList from './tabList.vue'
import { IBreadData } from '@/components/breadcrumb/type.ts'
import filterComp from './Filter.vue'
import Table from './table/Table.vue'
import { ref, watch, onMounted } from 'vue'
import useMndCtx from '@/common/shareContext/useMndCtx.ts'
// import { useMessage } from 'naive-ui'
import hazardDetails from './hazard-details.vue'
import { IActionData } from '@/views/configure-mgr/jurisdiction/type.ts'
import { ACTION } from './constant.ts'
import { hazardDisposeSuperviseSaveUrgeRecord } from '@/views/supervise/fetchData.ts'
import { toVw, toVh } from '@/utils/fit'
import TreeModle from '@/views/tenantry/tenantManagement/TreeModle.vue'
import { useAuthStore } from '@/store/modules'

const store = useAuthStore()
const ins = useMndCtx().message
const breadData: IBreadData[] = [{ name: '隐患整改督办' }]
const collapsed: any = ref(store.collapsedFlag)
// console.log('message',message)
const tableCompRef = ref()
const tableListRef = ref()
const hazardDetailsRef = ref()
const unitIds = ref<string | undefined>(store.userInfo.unitId)
function actionFn(val: IActionData) {
  if (val.action === ACTION.SEARCH) {
    handleSearch(val.data)
  } else if (val.action === ACTION.URGE) {
    console.log('催促')
    hazardUrgeFn(val.data)
  } else if (val.action === ACTION.DETAIL) {
    console.log('详情')
    hazardDetailsRef.value.open(val.data)
  }
}
function fn(val: string) {
  unitIds.value = val
}
function handleSearch(data?: Record<string, any>) {
  if (data) {
    tableCompRef.value?.getTableDataWrap(data)
    // tableListRef.value?.getLevelList(data)
    tableListRef.value?.getList(data)
  } else {
    tableCompRef.value?.getTableData()
  }
}

const hazardUrgeFn = async (row: any) => {
  let { id, userTelphone, userName } = store.userInfo
  const params = {
    disposeId: row.disposeId,
    eventType: 4,
    subCenterCode: row.zhId,
    operatorId: id,
    operatorTel: userTelphone,
    operatorName: userName,
  }
  await hazardDisposeSuperviseSaveUrgeRecord(params)
  // message.success('催促成功');
  ins.success('催促成功')
  // const message = useMessage()
  // message.success(
  //     '催促成功'
  // )
}
watch(
  () => collapsed.value,
  (nv) => {
    store.collapsedFlag = nv
  }
)
const siderRef: any = ref<HTMLElement | null>(null)
onMounted(async () => {
  // const trigger = siderRef.value.querySelector('.n-layout-toggle-button');
  // const svgPath = new URL(`@/assets/svg/ss.svg`, import.meta.url).href;
  // trigger.innerHTML = `<img  class='triggerImg' src="${svgPath}" alt="">`; // 替换为新的图标元素或类名
})
defineOptions({ name: 'superviseName' })
</script>

<style module lang="scss">
.videoWrap {
  display: flex;
  flex-direction: column;

  .container {
    flex: 1;
    background: #eef7ff;
  }

  .padding {
    padding: 16px 24px;
  }
}
</style>
<style scoped lang="scss">
.w_bg {
  background-color: #dce4f4 !important;
}

.w_table_box {
  height: 100%;
  padding: 20px;
  box-sizing: border-box;

  .w_table {
    height: calc(100% - 222px);
  }
}

.img-switch {
  cursor: pointer;
  width: 34px;
  height: 36px;
  position: absolute;
  left: 0;
  top: 47%;
  transform: translateX(-50%);
  background: url('/src/assets/expand.png') no-repeat;
  background-size: cover;
}

:deep(.triggerImg) {
  width: 20px;
}

:deep(.n-layout-sider) {
  background-color: #eef7ff;
  max-width: 100% !important;
  width: 100% !important;
}

:deep(.n-layout .n-layout-scroll-container) {
  background-color: #eef7ff;
  border-radius: 12px;
}

:deep(.n-layout),
:deep(.n-layout-sider) {
  border-radius: 12px;
  overflow: hidden;
}

:deep(.n-layout-sider-border) {
  border-radius: 12px;
}
</style>
