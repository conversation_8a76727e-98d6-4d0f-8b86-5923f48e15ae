<template>
  <n-space vertical v-if="dispsoseNodeRecordData?.length">
    <n-steps vertical>
      <n-step v-for="node of dispsoseNodeRecordData" :title="node.nodeName" :key="node.id">
        <n-descriptions label-placement="left" class="mb-6" :column="1">
          <n-descriptions-item v-for="(item, index) of node.nodeInfo" :key="index" :label="item.description">
            <span v-if="['string', 'String'].includes(item.webType)">{{
              typeof item.dataValue == 'number' ? dayjs(item.dataValue).format('YYYY-MM-DD hh:mm:ss') : item.dataValue
              }}</span>
            <template v-else-if="item.webType === 'image'">
              <n-image v-for="src of item.dataValue" :key="src" width="100" :src="getFileURL(src)" />
            </template>
          </n-descriptions-item>
        </n-descriptions>
      </n-step>
    </n-steps>
  </n-space>
  <div v-else class="flex justify-center items-center">
    <n-spin v-if="loading" />
    <empty v-else />
  </div>
</template>

<script setup lang="ts">
import { IHazardRecord } from './type';
import dayjs from 'dayjs';
import getFileURL from '@/utils/getFileURL';

interface Props {
  dispsoseNodeRecordData: IHazardRecord[] | null;
  loading: boolean;
}
const props = withDefaults(defineProps<Props>(), {
  dispsoseNodeRecordData: null,
});
</script>

<style scoped></style>
