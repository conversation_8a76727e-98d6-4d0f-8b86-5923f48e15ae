<template>
  <span>
    <n-button tertiary @click="showModal = true"> 查看位置 </n-button>
    <n-modal v-model:show="showModal" title="隐患详情" preset="card" style="width: 800px; height: 600px">
      <indoorMap :floor-info="floorData" :device-list="deviceList" />
    </n-modal>
  </span>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import indoorMap from '@/components/indoorMap/index.vue';
import { pointerList } from './data';
import { IMapFloorInfo } from '../type';

interface Props {
  floorData: IMapFloorInfo;
  deviceList: any[];
}
const props = withDefaults(defineProps<Props>(), {});
const showModal = ref(false);
</script>
