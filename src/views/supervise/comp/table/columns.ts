import { DataTableColumn } from 'naive-ui';
import { IHazardSource } from '../../type';
import { tagBgs } from '@/views/random-check/check-task-manage/comp/detail/columns';
import { h } from 'vue';
import { disposeState } from '@/components/table-col/disposeState';
export const cols: DataTableColumn[] = [
  {
    title: '隐患单位',
    key: 'unitName',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '隐患来源',
    key: 'hazardSourceName',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '隐患位置',
    key: 'hazardPosition',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '隐患描述',
    key: 'hazardDesc',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '隐患类别',
    key: 'hazardTypeName',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '隐患级别',
    key: 'hazardLevelName',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '上报时间',
    key: 'eventTime',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '整改状态',
    key: 'disposeStateName',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
    render: (row) => h(disposeState, { row }),
  },
  {
    title: '是否超期',
    key: 'timeoutDays',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
    render: (row: any) => {
      if (row.timeoutDays == 0) return '否';
      if (row.timeoutDays) return `超期${row.timeoutDays}天`;
    },
  },
];
