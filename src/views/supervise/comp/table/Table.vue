<template>
  <n-data-table
    class="h-full com-table"
    remote
    striped
    :columns="columns"
    :data="tableData"
    :bordered="false"
    :flex-height="true"
    :loading="loading"
    :pagination="pagination"
    :render-cell="useEmptyCell"
  />
</template>

<script setup lang="ts">
import { h, ref, VNode, toRaw, onMounted } from 'vue';
import { DataTableColumns, NButton } from 'naive-ui';
import { cols } from './columns';
import { ACTION } from '@/views/configure-mgr/checklist-conf/check-template/constant';
import { useRouter } from 'vue-router';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import { useNaivePagination } from '@/common/hooks/useNaivePagination.ts';
import { useActionDivider } from '@/common/hooks/useActionDivider.ts';
import { IObj } from '@/types';
import { useEmptyCell } from '@/common/hooks/useEmptyCell.ts';
import { getHazardPageList } from '../../fetchData';
import { IActionType, IDisposeState, IHazardRowInfo } from '../../type';
import { useStore } from '@/store';

const userInfo = useStore()?.userInfo;
const router = useRouter();
const emits = defineEmits(['action', 'showDetails']);

const [loading, search] = useAutoLoading(true);
const { pagination, updateTotal } = useNaivePagination(getTableData);

const columns = ref<DataTableColumns>([]);
const tableData = ref<any[]>([]);
function setColumns() {
  columns.value.push(...cols);
  columns.value.push({
    title: '操作',
    key: 'actions',
    align: 'left',
    fixed: 'right',
    width: 170,
    render: (row) => getActionBtn(row),
  });
}
setColumns();

function getActionBtn(row: IHazardRowInfo | any) {
  const acList: [VNode, boolean?][] = [
    [
      h(
        NButton,
        {
          size: 'small',
          type: 'primary',
          ghost: true,
          class: 'com-action-button',
          onClick: () =>
            emits('action', {
              action: IActionType.showDetails,
              data: row,
            }),
        },
        { default: () => '详情' }
      ),
    ],
    [
      h(
        NButton,
        {
          size: 'small',
          type: 'primary',
          ghost: true,
          class: 'com-action-button',
          disabled: row.disposeState !== IDisposeState.待整改,
          onClick: () =>
            emits('action', {
              action: IActionType.hazardUrge,
              data: row,
            }),
        },
        { default: () => '催促' }
      ),
    ],
  ];
  return useActionDivider(acList);
}

let filterData: IObj<any> = {}; // 搜索条件
function getTableData() {
  const params: any = {
    pageNo: pagination.page,
    pageSize: pagination.pageSize,
    ...filterData,
  };
  // 如果没选检查对象，默认登录用户自己的
  if (!params.unitId) params.unitId = userInfo?.unitId;

  search(getHazardPageList(params)).then((res) => {
    console.log(res);
    tableData.value = res.data.rows || [];
    updateTotal(res.data.total || 0);
  });
}

loading.value = false;
function getTableDataWrap(data: IObj<any>) {
  filterData = Object.assign({}, data) || {};
  pagination.page = 1;
  getTableData();
}
defineOptions({ name: 'checkTampTableComp' });
defineExpose({ getTableDataWrap, getTableData });
</script>
<style module lang="scss"></style>
