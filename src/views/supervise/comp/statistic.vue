<template>
  <div class="flex justify-start items-center h-[100px]">
    <Header title="隐患检查情况" :blueIcon="true"></Header>
    <div class="flex justify-start items-center py-5 w-full">
      <div class="trouble-number flex justify-start items-center">
        <img src="./icon1.png" alt="" />
        <div class="ml-3">
          <div>隐患数量</div>
          <n-statistic tabular-nums style="--n-value-text-color: #ff9500">
            <n-number-animation :from="0" :to="statisticsData.total" />
          </n-statistic>
        </div>
      </div>
      <div class="flex justify-start items-center other-card ml-5 bg1">
        <div style="z-index: 2">
          <div>已整改</div>
          <n-statistic tabular-nums style="--n-value-text-color: #00b578">
            <n-number-animation :from="0" :to="statisticsData.disposedNum" />
          </n-statistic>
        </div>
      </div>
      <div class="flex justify-start items-center other-card ml-5 bg1">
        <div style="z-index: 2">
          <div>未整改</div>

          <n-statistic tabular-nums style="--n-value-text-color: #fa5151">
            <n-number-animation :from="0" :to="statisticsData.unDisposedNum" />
          </n-statistic>
        </div>
      </div>
      <div class="flex justify-start items-center other-card ml-5 bg2">
        <div style="z-index: 2">
          <div>超期数量</div>
          <n-statistic tabular-nums style="--n-value-text-color: #ff9500">
            <n-number-animation :from="0" :to="statisticsData.timeout" />
          </n-statistic>
        </div>
      </div>
      <div class="other-card ml-5 bg3" style="width: 494px" v-if="levelStatisticsData && levelStatisticsData.length">
        <!-- <img src="./bg3.png" alt="" style="position: absolute; top: 0; left: 0" /> -->
        <div style="z-index: 2; position: relative; top: -6px" class="flex justify-start items-center">
          <div v-for="(item, idx) in levelStatisticsData" :key="idx" style="width: 25%">
            <div>{{ item.hazardLevelName }}</div>
            <n-statistic tabular-nums :style="{ '--n-value-text-color': colorList[idx] }">
              <n-number-animation :from="0" :to="item.total" />
            </n-statistic>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { getHazardStatistics, getLevelStatistics } from '../fetchData';
import { IHazardStatistic, ILevelStatistic, IPageEventParams } from '../type';
import { useStore } from '@/store';

const userInfo = useStore()?.userInfo;
const statisticsData = ref<IHazardStatistic>({});
const levelStatisticsData = ref<ILevelStatistic[]>([]);
const colorList = ['#e23b50', '#f59a23', '#bfbf00', '#76b90e'];

const getData = async (data: IPageEventParams = {}) => {
  statisticsData.value = {};
  levelStatisticsData.value = [];
  getHazardStatistics({ ...data, unitId: userInfo?.unitId }).then((res) => {
    statisticsData.value = res.data;
  });

  getLevelStatistics({ ...data, unitId: userInfo?.unitId }).then((res) => {
    levelStatisticsData.value = res.data;
  });
};

defineExpose({ getData });

defineOptions({ name: 'hazard-management-statistic' });
</script>
<style lang="scss" scoped>
.bg1 {
  background-size: 100% 100%;
  background-image: url('@/views/hazard-management/comp/bg1.png');
}

.bg2 {
  background-size: 100% 100%;
  background-image: url('@/views/hazard-management/comp/bg2.png');
}

.bg3 {
  background-size: 100% 100%;
  background-image: url('@/views/hazard-management/comp/bg3.png');
}

.trouble-number {
  img {
    width: 66px;
    height: 66px;
  }

  width: 218px;
  height: 80px;
  padding: 15px 32px 17px 32px;
  background: linear-gradient(180deg, #ffffff 0%, #e1e7fa 99%);
  box-shadow: 0px 2px 0px 0px rgba(0, 20, 82, 0.18);
  border-radius: 8px;
}

.other-card {
  position: relative;
  width: 172px;
  height: 80px;
  padding: 15px 32px 17px 32px;
}
</style>
