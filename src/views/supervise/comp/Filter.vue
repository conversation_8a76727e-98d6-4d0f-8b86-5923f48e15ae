<template>
  <n-form :show-feedback="false" label-placement="left">
    <n-grid :x-gap="12" :y-gap="8" :cols="4">
      <n-grid-item>
        <n-form-item label="隐患单位:">
          <n-select placeholder="请选择" v-model:value="filterForm.unitId" clearable :options="unitOptions" />
        </n-form-item>
      </n-grid-item>
      <n-grid-item>
        <n-form-item label="是否超期:">
          <n-select
            placeholder="请选择"
            v-model:value="filterForm.timeoutDays"
            clearable
            :options="hazardOverdueOptions"
          />
        </n-form-item>
      </n-grid-item>
      <n-grid-item>
        <n-form-item>
          <n-input
            placeholder="请输入隐患单位/隐患类别/级别模糊搜索"
            v-model:value="filterForm.likeFieldValue"
            clearable
          >
            <template #suffix>
              <BsSearch />
            </template>
          </n-input>
        </n-form-item>
      </n-grid-item>
      <n-grid-item>
        <n-form-item label="隐患级别:">
          <n-select v-model:value="filterForm.hazardLevel" placeholder="请选择" clearable :options="hazardLevelList" />
        </n-form-item>
      </n-grid-item>
      <n-grid-item>
        <n-form-item label="整改状态:">
          <n-select v-model:value="filterForm.disposeState" clearable :options="disposeStateOptions" />
        </n-form-item>
      </n-grid-item>

      <n-grid-item>
        <n-form-item label="上报时间:">
          <n-date-picker v-model:formatted-value="timeRange" value-format="yyyy-MM-dd" type="daterange" clearable />
        </n-form-item>
      </n-grid-item>
      <n-grid-item span="2">
        <n-form-item>
          <div class="flex justify-end w-full">
            <n-button type="primary" @click="exportEventListFn"> 导出 </n-button>
          </div>
        </n-form-item>
      </n-grid-item>
    </n-grid>
  </n-form>
</template>

<script setup lang="ts">
import { onMounted, ref, watch } from 'vue';
import { BsSearch } from '@kalimahapps/vue-icons';
import { trimObjNull } from '@/utils/obj.ts';
import { SelectOption } from 'naive-ui';
import { exportEventList, getAllUnit, getHazardGradeList, getHazardOverdueList } from '../fetchData.ts';
import { IActionType } from '../type.ts';
import { computed } from 'vue';
import { fileDownloader } from '@/utils/fileDownloader';
import { throttle } from 'lodash-es';
import { useStore } from '@/store/index.ts';
import { getDefaultDateRange } from '@/utils/getDefaultDateRange.ts';
import dayjs from 'dayjs';

const { userInfo } = useStore();

import { useRoute } from 'vue-router';
const route = useRoute();

const emits = defineEmits(['action']);

const defaultDateRange = getDefaultDateRange();

const hazardLevelList = ref<SelectOption[]>([]);
const hazardOverdueOptions = ref<SelectOption[]>([]);
const unitOptions = ref<SelectOption[]>([]);

const disposeStateOptions = [
  { label: '待整改', value: 0 },
  { label: '已整改', value: 1 },
  { label: '整改中', value: 2 },
];

const filterForm = ref({
  unitId: null,
  hazardType: null,
  likeFields: 'unitName,hazardTypeName,hazardLevelName',
  likeFieldValue: null,
  hazardLevel: null,
  disposeState: null,
  timeoutDays: null,
  startTime: defaultDateRange[0],
  endTime: defaultDateRange[1],
});

const timeRange = computed({
  get: () => {
    if (filterForm.value.startTime && filterForm.value.endTime) {
      return [filterForm.value.startTime, filterForm.value.endTime];
    }
    return undefined;
  },
  set: (value) => {
    if (!value) {
      filterForm.value.startTime = '';
      filterForm.value.endTime = '';
      return;
    }
    filterForm.value.startTime = value[0];
    filterForm.value.endTime = value[1];
  },
});

const exportEventListFn = async () => {
  fileDownloader(exportEventList(), {
    method: 'POST',
    contentType: 'application/json',
    body: JSON.stringify(filterForm.value),
  });
};

const doHandle = throttle(() => {
  emits('action', {
    action: IActionType.search,
    data: trimObjNull(filterForm.value),
  });
}, 1000);

console.log(route.query);
if (route.query?.unitId) {
  let query = route.query;
  filterForm.value.unitId = query.unitId as null;
  filterForm.value.startTime = dayjs(query.eventTime as string).format('YYYY-MM-DD');
  filterForm.value.endTime = dayjs(query.eventTime as string).format('YYYY-MM-DD');
  filterForm.value.disposeState = 0;
  filterForm.value.hazardLevel = query.hazardLevel as null;
}

/** 获取下拉框数据 */
const getFilterFormData = async () => {
  getHazardGradeList({ delFlag: 0, unitId: userInfo.orgCode }).then((res) => {
    hazardLevelList.value = res.data.map((item) => ({ label: item.gradeName, value: item.id }));
  });
  getHazardOverdueList({ pageSize: 1000, pageNo: 1, unitId: userInfo.unitId }).then((res) => {
    hazardOverdueOptions.value = res.data.rows.map((item) => ({
      label: item.overdueDay == 0 ? '否' : item.overdueDay + '天',
      value: item.overdueDay,
    }));
  });
  await getAllUnit({ pageSize: -1, orgCode: userInfo.orgCode }).then((res) => {
    unitOptions.value = res.data.rows.map((item) => ({ label: item.unitName, value: item.id }));
  });
};

watch(filterForm.value, doHandle);

onMounted(async () => {
  getFilterFormData();
  doHandle();
});

defineOptions({ name: 'checkTempFilterComp' });
</script>
<style module lang="scss"></style>
