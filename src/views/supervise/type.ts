import type { IObj, IPageRes } from '@/types';

// 分页列表数据
export interface IHazardGrade {
  gradeName: string;
  gradeSort: number;
  id?: string;
}
export type IRes = {
  data: IHazardGrade[];
};

export interface IDetail {
  areaCode: string;
  areaName: string;
  createdBy: string;
  createdTime: string;
  /**
   * 主键ID
   */
  id: string;
  isIot: string;
  /**
   * 单位辖区范围
   */
  jurisdiction: string;
  unitName: string;
  unitType: string;
  updatedBy: string;
  updatedTime: string;
}

export enum IActionType {
  search,
  showDetails,
  hazardUrge,
  export,
}

/** 隐患记录整改节点内的信息类型和label，value */
export interface IHazardRecordNode {
  dataValue: string | string[];
  description: string;
  webType: 'string' | 'image';
}

/** 隐患记录整改节点 */
export interface IHazardRecord {
  nodeName: string;
  id: string;
  nodeInfo: IHazardRecordNode[];
}

export interface IHazardRowInfo {
  createBy: string; //    上报人
  eventTime: string; //      上报时间
  disposeId: string; //   处置id
  disposeState: 0; //  整改状态code
  disposeStateName: string; //  整改状态
  hazardDesc: string; //    隐患描述
  hazardLevel: string; //  隐患级别code
  hazardLevelName: string; //   隐患级别
  hazardPosition: string; //   隐患位置
  hazardSource: IHazardSource; // 隐患来源code
  hazardSourceName: string; //隐患来源
  hazardType: string; //     隐患类别code
  hazardTypeName: string; // 隐患类别
  id: string; //    隐患ID
  superviseUnitId: string; //  分管单位ID
  superviseUnitName: string; //  分管单位
  timeoutDays: string; //   是否超期
  unitId: string; //  隐患单位ID
  unitName: string; //   隐患单位
  eventSourceId: string;
  eventType: string;
  subCenterCode: string;
  zhId?: string;
}

export interface IHazardInfo {
  aa: string; // 占位
  superviseUnitName: string; //  分管单位
  unitName: string; //  隐患单位
  hazardSource: IHazardSource; //  隐患来源code

  hazardPosition: string; //  隐患位置

  hazardSourceName: string; // 隐患来源
  eventTime: string; //  上报时间
  createTime: string; //  检查时间
  reportTime: string; //  上报时间
  checkTime: string; //  检查时间
  reformUser: string; //  隐患整改人员
  hazardDesc: string; //  隐患描述
  hazardType: string; //  隐患类别
  hazardLevel: string; //  隐患等级
  hazardLevelName: string; //  隐患等级
  remark: string; //  备注
  files: { fileUrl: string }[]; //  图片
  classItemVo: {
    inspectionItem: string; //  检查内容
    inspectionDescribe: string; //  检查详情
    inspectionItemBasis: string; //  法规依据
    inspectionAsk: string; //  合规要求
    legalText: string; //  法律原文
    legalLiability: string; //  法律责任
    listFile: { fileUrl: string }[]; //  检查信息图片
    [property: string]: any;
  };

  // 临时
  unitId: string;
  buildingId: string;
  floorId: string;
  floorAreaImg: string;
  mapX: number;
  mapY: number;
  mapZ: number;
  longitude: number;
  latitude: number;
  [property: string]: any;
}

export enum IHazardSource {
  物联网监测 = 1,
  人工上报,
  智能视频终端,
  监督检查隐患,
  巡查检查,
}

export interface IMapFloorInfo {
  unitId: string;
  buildingId: string;
  floorId: string;
  floorAreaImg?: string | undefined;
}

/** 隐患治理首页列表接口参数 */
export interface IPageEventParams {
  superviseUnitId?: string; // 分管单位
  unitId?: string; // 隐患单位
  hazardType?: string; // 隐患类别
  hazardLevel?: string; // 隐患级别
  disposeState?: string; // 整改状态
  timeoutDays?: number; // 是否超期
  startTime?: string; // 开始时间
  endTime?: string; // 结束时间
  search?: string; // 搜索框
  webSearch?: 'superviseUnitName|unitName|hazardTypeName|hazardLevelName'; // 搜索框
  pageNo?: number; // 页码
  pageSize?: number; // 每页条数
}

export interface IHazardStatistic {
  total?: number; //  隐患数量
  unDisposedNum?: number; //  未整改
  disposedNum?: number; //  已整改
  disposingNum?: number; //  整改中
  timeout?: number; //  超期未整改
}

export interface ILevelStatistic {
  total: number;
  hazardLevelName: string;
}

export interface IDetailsItem {
  label: string;
  key: keyof IHazardInfo | keyof IHazardInfo['classItemVo'];
  span?: number;
  type?: 'image' | 'map';
}

export enum IDisposeState {
  '待整改' = 0,
  '已整改' = 1,
  '整改中' = 2,
}

export interface IUnitInfo {
  unitName: string;
  id: string;
}
