import type { IDetail, IPageDataRes } from './type';
import { $http } from '@tanzerfe/http';
import { api } from '@/api';
import { IObj, IPageRes } from '@/types';
import { ICategory } from '@/views/configure-mgr/checklist-conf/check-library/type.ts';
import { IHazardTask } from '@/views/lessee-manage/components/HazardAnalyze/type.ts';
import {
  IHazardGrade,
  IHazardInfo,
  IHazardRecord,
  IHazardRowInfo,
  IHazardStatistic,
  ILevelStatistic,
  IUnitInfo,
} from './type';

// 获取分页
export function pageData(query: IObj<any>) {
  const url = api.getUrl(api.type.lease, api.name.demo.hazardDisposeSupervisePageEvent);
  return $http.post<IPageRes<IHazardTask>>(url, { data: { _cfg: { showTip: true }, ...query } });
}

// 隐患数统计
export function hazardDisposeSuperviseStatistics(query: IObj<any>) {
  const url = api.getUrl(api.type.lease, api.name.demo.hazardDisposeSuperviseStatistics);
  return $http.post<IPageRes<IHazardTask>>(url, { data: { _cfg: { showTip: true }, ...query } });
}

//
export function hazardDisposeSuperviseLevelStatistics(query: IObj<any>) {
  const url = api.getUrl(api.type.lease, api.name.demo.hazardDisposeSuperviseLevelStatistics);
  return $http.post<IPageRes<IHazardTask>>(url, { data: { _cfg: { showTip: true }, ...query } });
}

//
export function hazardDisposeSuperviseLevelGradeList(query: IObj<any>) {
  const url = api.getUrl(api.type.lease, api.name.demo.hazardDisposeSuperviseLevelGradeList);
  return $http.post<IPageRes<IHazardTask>>(url, { data: { _cfg: { showTip: true }, ...query } });
}

//
export function hazardDisposeSuperviseLevelQueryUserTenantInfoList(query: IObj<any>) {
  const url = api.getUrl(api.type.lease, api.name.demo.hazardDisposeSuperviseLevelQueryUserTenantInfoList, query);
  return $http.post<IPageRes<IHazardTask>>(url, { data: { _cfg: { showTip: true }, ...query } });
}
//
export function hazardDisposeSuperviseLevelTimeoutPage(query: IObj<any>) {
  const url = api.getUrl(api.type.lease, api.name.demo.hazardDisposeSuperviseLevelTimeoutPage);
  return $http.post<IPageRes<IHazardTask>>(url, { data: { _cfg: { showTip: true }, ...query } });
}
export function hazardDisposeSuperviseSaveUrgeRecord(query: IObj<any>) {
  const url = api.getUrl(api.type.lease, api.name.demo.hazardDisposeSuperviseSaveUrgeRecord);
  return $http.post<IPageRes<IHazardTask>>(url, { data: { _cfg: { showTip: true }, ...query } });
}
/** 隐患整改记录 */
export function hazardDisposeSuperviseRecord(query: IObj<any>) {
  const url = api.getUrl(api.type.lease, api.name.demo.hazardDisposeSuperviseRecord);
  return $http.post<IPageRes<IHazardTask>>(url, { data: { _cfg: { showTip: true }, ...query } });
}
export function hazardDisposeSuperviseQueryEventDetail(query: IObj<any>) {
  const url = api.getUrl(api.type.lease, api.name.demo.hazardDisposeSuperviseQueryEventDetail, query);
  return $http.post<IPageRes<IHazardTask>>(url, { data: { _cfg: { showTip: true } } });
}

// 获取隐患等级
export function getHazardGradeList(params: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.hazardManagement.hazardGradeList, params);
  return $http.post<IHazardGrade[]>(url, { data: { _cfg: { showTip: true }, ...params } });
}

/** 隐患分类列表 */
export function getHazardEssentialFactorClassList() {
  const url = api.getUrl(api.type.hazard, api.hazardManagement.hazardEssentialFactorClassList);
  return $http.post<IHazardGrade[]>(url, { data: { _cfg: { showTip: true } } });
}

/** 隐患催促 */
export function hazardUrge(params: any) {
  const url = api.getUrl(api.type.hazard, api.hazardManagement.hazardUrge);
  return $http.post<IHazardGrade[]>(url, { data: { _cfg: { showTip: true }, ...params } });
}

/** 隐患处置详情 */
export function getHazardDetails(params: any) {
  const url = api.getUrl(api.type.hazard, api.hazardManagement.hazardDetails);
  return $http.post<IHazardGrade[]>(url, { data: { _cfg: { showTip: true }, ...params } });
}

// ================================== 隐患信息已完成数据接口 ==================================

/** 隐患治理已完成列表 */
export function getHazardPageList(params: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.hazardManagement.completedGetHazardPageList, params);
  return $http.post<IPageRes<IHazardRowInfo>>(url, { data: { _cfg: { showTip: true }, ...params } });
}

export function hazardPlanTaskEvent(params: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.hazardManagement.hazardPlanTaskEvent, params);
  return $http.post<IPageRes<IHazardRowInfo>>(url, { data: { _cfg: { showTip: true }, ...params } });
}

/** 隐患治理已完成统计数据 */
export function getHazardStatistics(params: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.hazardManagement.completedHazardStatistics, params);
  return $http.post<IHazardStatistic>(url, { data: { _cfg: { showTip: true }, ...params } });
}
/** 隐患列表已完成隐患等级信息 */
export function getLevelStatistics(params: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.hazardManagement.completedLevelStatistics, params);
  return $http.post<ILevelStatistic[]>(url, { data: { _cfg: { showTip: true }, ...params } });
}

// ================================== 隐患信息为完成数据接口 ==================================
/** 隐患治理未完成列表 */
export function unfinishedGetHazardPageList(params: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.hazardManagement.getHazardPageList);
  return $http.post<IPageRes<IHazardRowInfo>>(url, { data: { _cfg: { showTip: true }, ...params } });
}

/** 隐患治理未完成统计数据 */
export function unfinishedHazardStatistics(params: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.hazardManagement.hazardStatistics);
  return $http.post<IHazardStatistic>(url, { data: { _cfg: { showTip: true }, ...params } });
}
/** 隐患列表未完成隐患等级信息 */
export function unfinishedLevelStatistics(params: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.hazardManagement.levelStatistics);
  return $http.post<ILevelStatistic[]>(url, { data: { _cfg: { showTip: true }, ...params } });
}

/** 隐患详情 */
export function qeuryEventDetail(params: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.hazardManagement.qeuryEventDetail, params);
  return $http.post<IHazardInfo>(url, { data: { _cfg: { showTip: true } } });
}

export function getHazardOverdueList(params: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.hazardManagement.hazardOverdueList);
  return $http.post<IPageRes<{ overdueDay: number }>>(url, { data: { _cfg: { showTip: true }, ...params } });
}

export function exportEventList() {
  const url = api.getUrl(api.type.hazard, api.hazardManagement.exportEventList);
  return url;
}

/** 获取隐患单位列表 */
export function getAllUnit(query: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.hazardManagement.getAllUnit);
  return $http.post<IPageRes<IUnitInfo>>(url, { data: { _cfg: { showTip: true }, ...query } });
}
