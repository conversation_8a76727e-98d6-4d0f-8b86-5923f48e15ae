<template>
  <div class="relative danger">
    <div class="check-task" v-if="taskList.total">
      <div class="task-item" :style="{ backgroundImage: `url(${one})` }">
        <div class="title">隐患总数</div>
        <div class="content flex items-center">
          <div>{{ taskList.total }}</div>
        </div>
      </div>
      <div class="task-item" :style="{ backgroundImage: `url(${two})` }">
        <div class="title">已整改</div>
        <div class="content flex items-center">
          <div>{{ taskList.disposedNum }}</div>
        </div>
      </div>
      <div class="task-item" :style="{ backgroundImage: `url(${three})` }">
        <div class="title">未整改</div>
        <div class="content flex items-center">
          <div>{{ taskList.unDisposedNum + taskList.disposingNum }}</div>
        </div>
      </div>
      <div class="task-item" :style="{ backgroundImage: `url(${four})` }">
        <div class="title">超期数量</div>
        <div class="content flex items-center">
          <div>{{ taskList.timeout || 0 }}</div>
        </div>
      </div>
    </div>
    <div class="w-full h-[105px] mt-[25px]" v-if="taskList.total">
      <n-scrollbar>
        <div class="bottom">
          <div
            v-for="(item, index) in riskList"
            :key="index"
            class="task-item"
            :style="{ backgroundImage: `url(${getImg(index)}) ` }"
          >
            <div class="title">{{ item.hazardLevelName }}</div>
            <div class="content flex items-center">
              <div>{{ item.total }}</div>
            </div>
          </div>
        </div>
      </n-scrollbar>
    </div>
    <div v-else class="empty">
      <EmptyComp />
      <div class="text-center text-[#ccc]">暂无数据</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import one from '@/assets/home/<USER>';
import two from '@/assets/home/<USER>';
import three from '@/assets/home/<USER>';
import four from '@/assets/home/<USER>';
import five from '@/assets/home/<USER>';
import six from '@/assets/home/<USER>';
import seven from '@/assets/home/<USER>';
import eight from '@/assets/home/<USER>';
import { getGivernInfo } from '../fetchData';
import { HazardLevelStatisticsVo } from '../type';
import EmptyComp from '@/components/empty/Empty.vue';

const riskBgList = [five, six, seven, eight];
const taskList = ref<any>({});
const riskList = ref<HazardLevelStatisticsVo[]>([]);

function getImg(index: number) {
  const bgIndex = index % riskBgList.length;
  return riskBgList[bgIndex];
}

function getTypeTaskList() {
  getGivernInfo().then((res: any) => {
    taskList.value = res.data.hazardStatisticsVo;
    riskList.value = res.data.hazardLevelStatisticsVos;
  });
}

onMounted(() => {
  getTypeTaskList();
});
</script>

<style lang="scss" scoped>
.check-task {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 27px;
  margin-top: 32px;
}

.task-item {
  // width: 374px;
  height: 103px;
  padding: 14px 0 0 23px;
  // background: #80cafa;
  border-radius: 4px;
  background-size: 100% 100%;

  .title {
    font-weight: 500;
    font-size: 16px;
    color: #ffffff;
    margin-bottom: 7px;
  }

  .content {
    font-weight: 500;
    font-size: 34px;
    color: #ffffff;

    .sub-content {
      font-weight: 400;
      font-size: 16px;
      color: #ffffff;
      margin-left: 8px;
    }
  }
}

.bottom {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 27px;

  padding-top: 2px;
}

.danger {
  height: 14.3125rem;
}

.empty {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}

:deep(.n-scrollbar) {
  height: 105px !important;
}
</style>
