<template>
  <div class="les-middle">
    <div class="les-middle-title">承租方项目分布</div>
    <div class="les-middle-content">
      <div class="w-2/4 left pr-[12px] relative">
        <ComHeaderC title="承租方来源分布" :activeIcon="true" />
        <PieEchart :data="pieChartData" />
      </div>
      <div class="flex-1 right ml-[12px] relative">
        <ComHeaderC title="承租方类型分布" :activeIcon="true" />
        <LineEchart :data="lineChartData" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import ComHeaderC from '@/components/header/ComHeaderC.vue';
import PieEchart from './pieEchart.vue';
import LineEchart from './lineEchart.vue';
import { getItemDistribution, getTypeDistribution } from '../fetchData';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import { echartData } from '../type';

const lineChartData = ref<echartData[]>([]);
const pieChartData = ref<echartData[]>([]);
const [loading, search] = useAutoLoading(true);

function getItem() {
  search(getItemDistribution()).then((res: any) => {
    const arr: { value: number; name: string }[] = [];
    res.data.forEach((item: { textName: string; num: number }) => {
      arr.push({
        name: item.textName,
        value: item.num,
      });
    });
    pieChartData.value = arr;
  });
}

function getType() {
  search(getTypeDistribution()).then((res: any) => {
    let arr: { value: number; name: string }[] = [];
    res.data.forEach((item: { typeName: string; count: number }) => {
      arr.push({
        name: item.typeName,
        value: item.count,
      });
    });
    lineChartData.value = arr.sort((a, b) => b.value - a.value);
  });
}

onMounted(() => {
  getItem();
  getType();
});
defineOptions({ name: 'MiddleComp' });
</script>

<style lang="scss" scoped>
.les-middle {
  height: 497px;
  background: #eef7ff;
  border: 1px solid #ffffff;
  border-radius: 9px 9px 0px 0px;

  .les-middle-title {
    height: 56px;
    background: #dce4f4;
    border-radius: 9px 9px 0px 0px;
    font-weight: 500;
    font-size: 16px;
    color: #222222;
    line-height: 56px;
    padding-left: 25px;
  }

  .les-middle-content {
    height: 441px;
    padding: 20px 23px;
    display: flex;
    justify-content: space-between;
  }
}
</style>
