<template>
  <div class="les-top">
    <div class="les-top-title">预警信息</div>
    <div class="les-top-content flex-1">
      <n-scrollbar>
        <div class="infinite-scroll" v-if="riskInfo.length">
          <div v-for="(item, index) in riskInfo" class="mb-[20px] les-item" :key="index">
            <div class="text-base mb-[5px]">
              <div v-if="item.type === '1'">
                <div v-if="item.status === '0'">
                  <span class="text-[#527CFF]">【{{ item.title }}】</span>
                  合同编号为 <span class="text-[#527CFF]">{{ item.content }}</span> 距离到期时间还有{{
                    item.num
                  }}天，请及时沟通合同变更。
                </div>
                <div v-else>
                  <span class="text-[#527CFF]">【{{ item.title }}】</span>
                  合同编号为 <span class="text-[#527CFF]">{{ item.content }}</span>
                  已过期，请及时沟通合同变更。
                </div>
              </div>
              <div v-else>
                <span class="text-[#527CFF]">【{{ item.title }}】</span>
                承租方为 <span class="text-[#527CFF]">{{ item.content }}</span>
                当前 有 1条隐患未按期整改，请及时督促整改解决。
              </div>
            </div>
            <div class="flex items-center ml-2 text-[#061032] text-sm">
              <PrClock class="mr-2" />
              接收时间: {{ item.pushTime }}
            </div>
          </div>
        </div>
        <div v-else class="mt-[50px]">
          <EmptyComp />
          <div class="text-center text-[#ccc]">暂无数据</div>
        </div>
      </n-scrollbar>
      <n-pagination
        v-model:page="page"
        v-model:page-size="pageSize"
        size="small"
        show-quick-jumper
        show-size-picker
        :page-sizes="[10, 20, 30, 40]"
        :item-count="itemCount"
        :prefix="prefixLabel"
        :on-update:page="handlePageChange"
        :on-update:page-size="handlePageSizeChange"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue';
import { PrClock } from '@kalimahapps/vue-icons';
import { getWarnInfo } from '../fetchData';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import { IDetail } from '../type';
import EmptyComp from '@/components/empty/Empty.vue';

const [loading, search] = useAutoLoading(true);
const riskInfo = ref<IDetail[]>([]);
const page = ref<number>(1); // 当前页
const pageSize = ref<number>(10); // 每页条数
const itemCount = ref<number>(0); // 总条数

function handlePageChange(value: number) {
  page.value = value;
  getWarnList();
}
function handlePageSizeChange(value: number) {
  pageSize.value = value;
  console.log('pageSize.value', pageSize.value);
  getWarnList();
}
async function getWarnList() {
  const data = {
    pageNo: page.value,
    pageSize: pageSize.value,
  };
  search(getWarnInfo(data)).then((res: any) => {
    riskInfo.value = res.data.rows || [];
    itemCount.value = res.data.total;
  });
}

function prefixLabel() {
  return `共 ${itemCount.value} 条`;
}

onMounted(() => {
  getWarnList();
});

defineOptions({ name: 'TopInfoComp' });
</script>

<style lang="scss" scoped>
.les-top {
  height: 239px;
  display: flex;
  align-items: center;
  border-radius: 5px;
  border: 1px solid #e6e6e6;
  background: url('@/assets/home/<USER>') no-repeat;
  background-size: 100% 100%;

  .les-top-title {
    margin-left: 200px;
    margin-right: 49px;
    font-family: YouSheBiaoTiHei;
    font-size: 38px;
    color: #527cff;
  }

  .les-top-content {
    height: 100%;
    display: flex;
    flex-direction: column;
    padding: 20px 20px 5px 20px;
    justify-content: space-around;
    align-items: flex-end;
  }
}
</style>
