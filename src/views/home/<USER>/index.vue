<template>
  <div class="les-bottom">
    <div class="les-middle-title">检查情况分析</div>
    <div class="les-middle-content">
      <div class="w-2/4 left pr-[12px]">
        <ComHeaderC title="检查任务执行情况" :activeIcon="true" />
        <CheckTask />
      </div>
      <div class="flex-1 right ml-[12px] relative">
        <ComHeaderC title="隐患治理情况" :activeIcon="true" />
        <DangerControl />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import ComHeaderC from '@/components/header/ComHeaderC.vue';
import CheckTask from './checkTask.vue';
import DangerControl from './dangerControl.vue';

defineOptions({ name: 'MiddleComp' });
</script>

<style lang="scss" scoped>
.les-bottom {
  // height: 419px;
  background: #eef7ff;
  border: 1px solid #ffffff;
  border-radius: 9px 9px 0px 0px;

  .les-middle-title {
    height: 56px;
    background: #dce4f4;
    border-radius: 9px 9px 0px 0px;
    font-weight: 500;
    font-size: 16px;
    color: #222222;
    line-height: 56px;
    padding-left: 25px;
  }

  .les-middle-content {
    // height: 441px;
    padding: 20px 23px;
    display: flex;
    justify-content: space-between;
  }
}
</style>
