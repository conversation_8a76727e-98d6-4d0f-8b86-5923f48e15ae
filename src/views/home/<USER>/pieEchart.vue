<template>
  <div class="line-part w-full h-full" ref="pieChart" v-if="!isEmpty"></div>
  <div v-else class="empty">
    <EmptyComp />
    <div class="text-center text-[#ccc]">暂无数据</div>
  </div>
</template>

<script lang="ts" setup>
import { defineComponent, markRaw, onMounted, ref, watch, onBeforeUnmount } from 'vue';
import * as echarts from 'echarts';
import EmptyComp from '@/components/empty/Empty.vue';

defineComponent({ name: 'pieChart' });
const props = defineProps({
  legend: {
    type: Object,
    default: () => {
      return {
        type: 'plain',
        orient: 'horizontal',
        left: '5',
        top: 'bottom',
        itemGap: 10,
        textStyle: {
          color: '#fff',
          fontSize: 14,
        },
        height: 100,
      };
    },
  },
  series: {
    type: Object,
    default: () => {
      return {
        radius: [35, '60%'],
        center: ['70%', '40%'],
        warpRadius: ['60%', '61%'],
      };
    },
  },
  data: {
    type: Array,
    default: () => [],
  },
  title: {
    type: Object,
    default: () => {
      return {
        text: 445,
        subtext: '已完成数',
      };
    },
  },
});
const pieChart = ref();
const myChart = ref<any>(null);
const isEmpty = ref(false);
const observer = ref<ResizeObserver>();

function sleep(t: number) {
  return new Promise((resolve) => setTimeout(resolve, t));
}
function initEcharts() {
  // 标记一个对象，使其永远不会再成为响应式对象
  myChart.value = markRaw(echarts.init(pieChart.value));
  const option = {
    title: {
      left: 'center',
    },
    tooltip: {
      // trigger: 'item',
    },
    legend: {
      orient: 'vertical',
      top: props.data.length <= 7 ? 'center' : '',
      right: '20%',
      bottom: props.data.length <= 7 ? '12' : '12%',
      itemGap: 15,
      itemWidth: 44,
      itemHeight: 32,
    },
    series: [
      {
        name: '',
        type: 'pie',
        radius: '60%',
        center: ['30%', '50%'],
        label: {
          show: false,
        },
        data: props.data,
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)',
          },
        },
      },
    ],
  };

  myChart.value.setOption(option, true);
  window.addEventListener('resize', () => {
    if (myChart.value) myChart.value.resize();
  });
}

function destroyEcharts() {
  if (myChart.value) {
    myChart.value.dispose();
    myChart.value = null;
  }
}

// watch(
//   () => props.data,
//   (val) => {
//     console.log('val', val);
//     if (props.data.length) initEcharts();
//   },
//   { immediate: true }
// );
onMounted(() => {
  // initEcharts();
  watch(
    () => props.data,
    async (val: any) => {
      destroyEcharts();
      isEmpty.value = !val.length;
      await sleep(500);
      if (!isEmpty.value && pieChart.value) initEcharts();
    },
    { immediate: true, deep: true }
  );
});

onBeforeUnmount(() => {
  destroyEcharts();
  // 在组件卸载时停止监听，避免内存泄漏
  observer.value?.disconnect();
});
</script>

<style scoped lang="scss">
.empty {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}
</style>
