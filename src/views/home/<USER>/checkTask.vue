<template>
  <div class="danger relative">
    <div class="check-task" v-if="taskList.count">
      <div class="task-item" :style="{ backgroundImage: `url(${one})` }">
        <div class="title">检查任务总数</div>
        <div class="content flex items-center">
          <div>{{ taskList.count }}</div>
        </div>
      </div>
      <div class="task-item" :style="{ backgroundImage: `url(${two})` }">
        <div class="title">任务完成率</div>
        <div class="content flex items-center">
          <div>{{ taskList.finishRate }}</div>
        </div>
      </div>
      <div class="task-item" :style="{ backgroundImage: `url(${three})` }">
        <div class="title">进行中检查任务数</div>
        <div class="content flex items-center">
          <div>{{ taskList.underwayNum }}</div>
          <div class="sub-content">(逾期未完成数{{ taskList.underwayLateNum }})</div>
        </div>
      </div>
      <div class="task-item" :style="{ backgroundImage: `url(${four})` }">
        <div class="title">已结束检查任务数</div>
        <div class="content flex items-center">
          <div>{{ taskList.finishNum }}</div>
          <div class="sub-content">(逾期完成{{ taskList.finishLateNum }})</div>
        </div>
      </div>
    </div>
    <div v-else class="empty">
      <EmptyComp />
      <div class="text-center text-[#ccc]">暂无数据</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue';
import one from '@/assets/home/<USER>';
import two from '@/assets/home/<USER>';
import three from '@/assets/home/<USER>';
import four from '@/assets/home/<USER>';
import { getCheckTask } from '../fetchData';
import { InspectExecuteDto } from '../type';
import EmptyComp from '@/components/empty/Empty.vue';

const bgList = [one, two, three, four];
const taskList = ref<InspectExecuteDto>({});

function getImg(index: number) {
  return bgList[index];
}

function getCheckTaskList() {
  getCheckTask().then((res: InspectExecuteDto) => {
    taskList.value = res.data;
  });
}

onMounted(() => {
  getCheckTaskList();
});
</script>

<style lang="scss" scoped>
.check-task {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 27px;
  margin-top: 32px;

  .task-item {
    // width: 374px;
    height: 103px;
    padding: 14px 0 0 23px;
    // background: #80cafa;
    border-radius: 4px;
    background-size: 100% 100%;

    .title {
      font-weight: 500;
      font-size: 16px;
      color: #ffffff;
      margin-bottom: 7px;
    }

    .content {
      font-weight: 500;
      font-size: 34px;
      color: #ffffff;

      .sub-content {
        font-weight: 400;
        font-size: 16px;
        color: #ffffff;
        margin-left: 8px;
      }
    }
  }
}

.danger {
  height: 14.3125rem;
}

.empty {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}
</style>
