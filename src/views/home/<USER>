import { IObj } from '@/types';
import { api } from '@/api';
import { $http } from '@tanzerfe/http';

// 更新检查模板
// export function updateTemplate(data: IObj<any>) {
//   const url = api.getUrl(api.type.demo, api.name.demo.updateTemplate, data);
//   return $http.post<IFormData>(url, { data: { _cfg: { showTip: true, showOkTip: true }, ...data } });
// }
// 查询预警信息
export function getWarnInfo(data: IObj<any>) {
  const url = api.getUrl(api.type.lease, api.sct.warnList);
  return $http.post(url, { data: { _cfg: { showTip: true }, ...data } });
}

// 承租方项目分布
export function getItemDistribution() {
  const url = api.getUrl(api.type.lease, api.sct.itemDistribution);
  return $http.get(url, { data: { _cfg: { showTip: true } } });
}

// 检查任务执行
export function getCheckTask() {
  const url = api.getUrl(api.type.lease, api.sct.checkTaskList);
  return $http.get(url, { data: { _cfg: { showTip: true } } });
}

// 隐患治理情况
export function getGivernInfo() {
  const url = api.getUrl(api.type.lease, api.sct.hazardGivernInfo);
  return $http.get(url, { data: { _cfg: { showTip: true } } });
}

// 承租方类型分布
export function getTypeDistribution() {
  const url = api.getUrl(api.type.lease, api.sct.typeDistribution);
  return $http.get(url, { data: { _cfg: { showTip: true } } });
}
