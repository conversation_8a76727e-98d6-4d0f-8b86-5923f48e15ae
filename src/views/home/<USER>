<template>
  <n-scrollbar>
    <div class="com-g-row-aa1">
      <ComBread :data="breadData" />
      <div class="com-g-home">
        <TopInfo />
        <Middle />
        <Bottom />
      </div>
    </div>
  </n-scrollbar>
</template>

<script setup lang="ts">
import { computed, ref, Ref } from 'vue';
import TopInfo from './topInfo/index.vue';
import Middle from './middle/index.vue';
import Bottom from './bottom/index.vue';
import ComBread from '@/components/breadcrumb/ComBread.vue';
import { IBreadData } from '@/components/breadcrumb/type.ts';

const breadData: Ref<IBreadData[]> = computed(() => [{ name: '首页' }]);

defineOptions({ name: 'HomeIndex' });
</script>

<style scoped lang="scss">
.com-g-home {
  // height: calc(100% - 45px);
  display: grid;
  grid-template-rows: 239px 1fr;
  gap: 20px;

  .com-g-risk {
    border: 1px solid #ccc;
  }
}
</style>
