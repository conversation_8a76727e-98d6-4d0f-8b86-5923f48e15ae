export interface IDetail {
  id: string;
  content: string;
  num: number;
  type: string;
  pushTime: string;
  title: string;
  status: string;
}

export interface echartData {
  value: number;
  name: string;
}

export interface InspectExecuteDto {
  /**
   * 检查任务总数量
   */
  count?: number | null;
  /**
   * 逾期完成数量
   */
  finishLateNum?: number | null;
  /**
   * 已结束检查任务数量
   */
  finishNum?: number | null;
  /**
   * 任务完成率
   */
  finishRate?: null | string;
  /**
   * 逾期未完成数量
   */
  underwayLateNum?: number | null;
  /**
   * 进行中检查任务数量
   */
  underwayNum?: number | null;
  [property: string]: any;
}

/**
 * 承租方类型分布
 *
 * InterTypeDistributionDto
 */
export interface InterTypeDistr {
  /**
   * 数量
   */
  count?: number | null;
  /**
   * 类型
   */
  type?: null | string;
  /**
   * 类型名称
   */
  typeName?: null | string;
  [property: string]: any;
}

/**
 * HazardGovernDto
 */
/**
 * 隐患事件等级统计
 *
 * HazardLevelStatisticsVo
 */
export interface HazardLevelStatisticsVo {
  /**
   * 隐患等级
   */
  hazardLevel?: number;
  /**
   * 隐患等级
   */
  hazardLevelName?: string;
  /**
   * 隐患数
   */
  total?: number;
  [property: string]: any;
}
