<template>
  <div class="line-part w-full h-full" ref="pieChart" v-if="!isEmpty"></div>
  <div v-else class="empty">
    <EmptyComp />
    <div class="text-center text-[#ccc]">暂无数据</div>
  </div>
</template>

<script lang="ts" setup>
import { defineComponent, markRaw, onMounted, ref, watch, onBeforeUnmount } from 'vue';
import * as echarts from 'echarts';
import { toVw, toVh } from '@/utils/fit';
import EmptyComp from '@/components/empty/Empty.vue';

defineComponent({ name: 'pieChart' });
const props = defineProps({
  legend: {
    type: Object,
    default: () => {
      return {
        type: 'plain',
        orient: 'horizontal',
        left: '5',
        top: 'bottom',
        itemGap: 30,
        textStyle: {
          color: '#fff',
          fontSize: 14,
        },
        height: 100,
      };
    },
  },
  series: {
    type: Object,
    default: () => {
      return {
        radius: [35, '60%'],
        center: ['70%', '40%'],
        warpRadius: ['60%', '61%'],
      };
    },
  },
  data: {
    type: Array,
    default: () => [],
  },
  title: {
    type: Object,
    default: () => {
      return {
        text: 445,
        subtext: '已完成数',
      };
    },
  },
});
const pieChart = ref();
const myChart = ref<any>(null);
const isEmpty = ref(false);
const observer = ref<ResizeObserver>();

function sleep(t: number) {
  return new Promise((resolve) => setTimeout(resolve, t));
}
function initEcharts() {
  // 标记一个对象，使其永远不会再成为响应式对象
  myChart.value = markRaw(echarts.init(pieChart.value));
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
    },
    grid: {
      left: '5%',
      right: '4%',
      top: '8%',
      // containLabel: true,
    },
    xAxis: {
      type: 'category',
      minInterval: 2, // 最小间隔
      maxInterval: 4, // 最大间隔
      axisTick: {
        show: false,
      },
      axisLine: {
        lineStyle: {
          color: '#E0E6F1',
        },
      },
      axisLabel: {
        color: '#6E7079',
        interval: 0,
        fontSize: toVw(12),
        rotate: 25,
        formatter: function (value: string) {
          return value.length >= 4 ? value.substring(0, 4) + '...' : value; // 自定义格式
        },
      },
      data: props.data.map((item: any) => item.name),
    },
    yAxis: {
      type: 'value',
    },
    series: [
      {
        name: '承租数量',
        type: 'bar',
        barWidth: '60%',
        data: props.data.map((item: any) => item.value),
        itemStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: '#60A2FF' },
              { offset: 1, color: '#387EFF' },
            ],
            global: false,
          },
        },
      },
    ],
  };
  myChart.value.setOption(option, true);
  window.addEventListener('resize', () => {
    if (myChart.value) myChart.value.resize();
  });
}
function destroyEcharts() {
  if (myChart.value) {
    myChart.value.dispose();
    myChart.value = null;
  }
}

// watch(
//   () => props.data,
//   (val) => {
//     if (props.data.length) initEcharts();
//   },
//   { immediate: true }
// );

onMounted(() => {
  // initEcharts();
  watch(
    () => props.data,
    async (val: any) => {
      destroyEcharts();
      isEmpty.value = !val[0]?.value;
      await sleep(500);
      if (!isEmpty.value && pieChart.value) initEcharts();
    },
    { immediate: true, deep: true }
  );
});

onBeforeUnmount(() => {
  destroyEcharts();
  // 在组件卸载时停止监听，避免内存泄漏
  observer.value?.disconnect();
});
</script>

<style scoped lang="scss">
.empty {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}
</style>
