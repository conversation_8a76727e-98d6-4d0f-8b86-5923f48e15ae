<!--
 * @Author: fangweiwei <EMAIL>
 * @Date: 2024-09-12 21:52:51
 * @LastEditors: fangweiwei <EMAIL>
 * @LastEditTime: 2024-09-27 21:29:45
 * @FilePath: \ehs-risk-mgr\src\views\risk-mgr\risk-divide\divideTemplate\comps\TreeModle.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="h-full" :class="$style['divide-tree']">
    <p :class="$style['head-title']">所属单位</p>
    <com-tree :data="treeData" @action="treeChange"></com-tree>
  </div>
</template>
<script lang="ts" setup>
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import ComTree from '@/components/tree/index.vue';
import { useAuthStore } from '@/store/modules';
import type { TreeOption } from 'naive-ui';
import { ref, watch } from 'vue';
import { getOrgTrees } from './fetchData';

const ui = useAuthStore();
const [loading, search] = useAutoLoading(true);
//树结构--操作
const treeData = ref([]);
const treeAct = ref();
const emits = defineEmits(['treeChange']);

function treeChange(v: TreeOption) {
  console.log('v', v);
  treeAct.value = v;
  emits('treeChange', v.id, v.attributes.erecordUnitId);
}

function QueryOrgTrees() {
  const params = {
    // needCheckJgdw: '1',
    // type: '1',
    // unitId: ui.userInfo?.unitId,
    orgCode: 10000,
    type: 2,
    needCheckJgdw: 1,
  };
  search(getOrgTrees(params)).then((res) => {
    const _RES: any = res.data;
    childrenFn(_RES); // 去除空children
    treeData.value = _RES;
  });
}

function childrenFn(_arr: any) {
  if (!_arr.length) return _arr;
  for (let index = 0; index < _arr.length; index++) {
    const element = _arr[index];
    if (element.children?.length) {
      childrenFn(element.children);
    } else {
      delete element.children;
    }
  }
  return _arr;
}

watch(
  () => ui.userInfo?.unitId,
  (nv) => {
    if (nv) {
      QueryOrgTrees();
    }
  },
  { deep: true, immediate: true }
);

defineOptions({ name: 'TreeModle' });
</script>

<style lang="scss" module>
.divide-tree {
  border-right: 1px solid #c8ced9;
  padding: 16px;

  .head-title {
    position: relative;
    padding-left: 25px;
    font-size: 14px;
    font-weight: bold;
    margin-bottom: 10px;

    &::before {
      content: '';
      width: 16px;
      height: 16px;
      background: url('@/components/header/assets/icon-title-arrow3.png') no-repeat center/contain;
      display: inline-block;
      position: absolute;
      top: 50%;
      left: 0;
      transform: translateY(-50%);
    }
  }
}
</style>
