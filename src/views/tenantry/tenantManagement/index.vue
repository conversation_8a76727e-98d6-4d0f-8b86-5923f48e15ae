<template>
  <div :class="{ [$style.videoWrap]: true }">
    <ComBread :data="breadData" />
    <div class="w-full h-full flex overflow-hidden">
      <div
        ref="siderRef"
        class="h-full bg-[#EEF7FF] mr-[16px] rounded-[12px]"
        :style="`width:${collapsed ? toVw(0) : toVw(346)};`"
      >
        <n-layout has-sider style="height: 100%">
          <n-layout-sider
            bordered
            collapse-mode="width"
            :collapsed-width="0"
            width="95%"
            :collapsed="collapsed"
            @collapse="collapsed = true"
            @expand="collapsed = false"
          >
            <TreeModle @treeChange="fn" />
          </n-layout-sider>
        </n-layout>
      </div>

      <div
        class="bg-[#EEF7FF] rounded-[12px] p-[24px] com-g-row-aaa1 relative"
        :style="`width: calc(100% - ${toVw(collapsed ? 15 : 346)} );`"
      >
        <RadioTab
          :tabList="tabList"
          :tab="curTab"
          @change="handleChange"
          class="w_bg"
        />
        <component :is="currentComp" :id="unitIds" />
        <div class="img-switch" @click="collapsed = !collapsed"></div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, Ref, watch, onMounted, provide } from 'vue'
import RadioTab from '@/components/tab/ComRadioTabA.vue'
import ComBread from '@/components/breadcrumb/ComBread.vue'
import { ITabItem } from '@/components/tab/type'
import { useRoute, useRouter } from 'vue-router'
import serverCom from './server/index.vue'
import blackCom from './black/index.vue'
import { IBreadData } from '@/components/breadcrumb/type.ts'
import { toVw, toVh } from '@/utils/fit'
import { useAuthStore } from '@/store/modules'
import TreeModle from './TreeModle.vue'

const router = useRouter()
const route = useRoute()
const ui = useAuthStore()
const collapsed: any = ref(ui.collapsedFlag)
const unitIds = ref<string | undefined>(ui.userInfo.unitId)

// tabs
const tabList: Array<ITabItem & { comp: any }> = [
  { name: 'serverCom', label: '服务项目', comp: serverCom },
  { name: 'blackCom', label: '黑名单列表', comp: blackCom }
]

const curTab = ref<string>((route.query?.tab as string) || 'serverCom')
function handleChange(name: string) {
  curTab.value = name
  router.push({
    query: { tab: name }
  })
}
function fn(val: string, id: string) {
  console.log('val', val, id)
  unitIds.value = val
}

const currentComp = computed(() => {
  return tabList.find((item) => item.name === curTab.value)?.comp || serverCom
})

const breadData: Ref<IBreadData[]> = computed(() => [{ name: '承租方管理' }])

watch(
  () => collapsed.value,
  (nv) => {
    ui.collapsedFlag = nv
  }
)
const siderRef: any = ref<HTMLElement | null>(null)
onMounted(async () => {})
defineOptions({ name: 'checklistConfComp' })
</script>

<style module lang="scss">
.videoWrap {
  display: flex;
  flex-direction: column;

  .container {
    flex: 1;
    background: #eef7ff;
  }

  .padding {
    padding: 16px 24px;
  }
}
</style>
<style scoped lang="scss">
.w_bg {
  background-color: #dce4f4 !important;
}

.com-g-row-aaa1 {
  //display: grid;
  //grid-template-rows: auto 1fr;
  .img-switch {
    cursor: pointer;
    width: 34px;
    height: 36px;
    position: absolute;
    left: 0;
    top: 47%;
    transform: translateX(-50%);
    background: url('/src/assets/expand.png') no-repeat;
    background-size: cover;
  }
}

:deep(.triggerImg) {
  width: 20px;
}

:deep(.n-layout-sider) {
  background-color: #eef7ff;
  max-width: 100% !important;
  width: 100% !important;
}

:deep(.n-layout .n-layout-scroll-container) {
  background-color: #eef7ff;
  border-radius: 12px;
}

:deep(.n-layout),
:deep(.n-layout-sider) {
  border-radius: 12px;
  overflow: hidden;
}

:deep(.n-layout-sider-border) {
  border-radius: 12px;
}
</style>
