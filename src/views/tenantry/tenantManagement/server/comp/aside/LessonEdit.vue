<template>
  <ComDrawerA
    :autoFocus="false"
    :footerPaddingBottom="25"
    :maskClosable="false"
    :show-action="true"
    :closeOnNegative="false"
    @handle-negative="handleNoSaveClose"
    @handle-positive="handleSubmit"
    class="!w-[500px]"
    v-model:show="isShowEdit"
  >
    <div class="w_dialog_box">
      <div
        v-for="(form, formIndex) in formList"
        :key="formIndex"
        class="mb-[20px]"
      >
        <div
          class="collapse-header"
          :class="{ 'is-collapsed': form.isCollapsed }"
        >
          <div class="flex items-center">
            <div
              @click="toggleCollapse(formIndex)"
              class="flex items-center w-full justify-between"
            >
              <div class="flex items-center">
                <div v-if="!form.isCollapsed">承租信息 {{ formIndex + 1 }}</div>
                <div v-else class="flex items-center">
                  <span class="text-blue-500">承租信息：</span>
                  <n-ellipsis
                    class="text-blue-500 text-xs ml-1"
                    :tooltip="true"
                    style="max-width: 300px"
                  >
                    合同编号为{{
                      form.data.effectiveContractNo || '--'
                    }}
                    ,合同有效期为{{ form.data.contractStartTime || '--' }}
                    至
                    {{ form.data.contractEndTime || '--' }}
                  </n-ellipsis>
                </div>
              </div>
              <n-icon
                :class="{ 'rotate-180': form.isCollapsed }"
                class="transition-transform duration-300"
              >
                <ChevronDown />
              </n-icon>
            </div>
          </div>
          <n-button
            v-if="formList.length > 1"
            quaternary
            circle
            type="error"
            size="small"
            @click="deleteForm(formIndex)"
          >
            <template #icon>
              <n-icon>
                <Close />
              </n-icon>
            </template>
          </n-button>
        </div>

        <div v-show="!form.isCollapsed" class="form-item relative">
          <n-form
            :ref="(el) => handleFormRef(el, formIndex)"
            :model="form.data"
            label-placement="left"
            require-mark-placement="right-hanging"
          >
            <div>
              <div class="w_a_title">承租合同信息：</div>
              <n-form-item
                label="当前生效合同编号："
                path="effectiveContractNo"
                :rule="rules.effectiveContractNo"
                label-width="auto"
              >
                <n-input
                  v-model:value="form.data.effectiveContractNo"
                  placeholder="请输入当前生效合同编号"
                  maxlength="100"
                  show-count
                  clearable
                />
              </n-form-item>
              <n-form-item
                label="合同有效期："
                path="contractStartTime"
                :rule="rules.contractStartTime"
                label-width="auto"
              >
                <n-date-picker
                  type="daterange"
                  clearable
                  :on-update:formatted-value="
                    (value) => onChangeData(value, formIndex)
                  "
                  v-model:value="form.fDate"
                />
              </n-form-item>
              <n-form-item label="合同到期天数：" label-width="auto">
                <n-input
                  v-model:value="form.data.contractExpirationDays"
                  disabled
                />
              </n-form-item>
              <n-form-item
                label="上传合同附件："
                :path="form.data._fileDataPath"
                :rule="rules.fileData"
                label-width="auto"
              >
                <imageUpCom
                  :data="form.fileList"
                  @update="(res, data) => handleUpdate(res, data, formIndex)"
                >
                </imageUpCom>
              </n-form-item>
            </div>

            <div
              v-for="(resource, resourceIndex) in form.data
                .rtenantryResourceList"
              :key="resourceIndex"
              class="resource-section"
            >
              <div class="collapse-header">
                <div
                  @click="toggleResourceCollapse(formIndex, resourceIndex)"
                  class="flex items-center w-full justify-between"
                >
                  <div class="flex items-center">
                    <div v-if="!resource.isCollapsed">
                      承租资源信息 {{ resourceIndex + 1 }}
                    </div>
                    <div v-else class="flex items-center">
                      <span class="text-blue-500 w-[100px]"
                        >承租资源信息：</span
                      >
                      <n-ellipsis
                        class="text-blue-500 text-xs ml-1"
                        :tooltip="true"
                        style="max-width: 218px"
                      >
                        资源名称为{{ resource.resourceName || '--' }},
                        建筑面积为{{ resource.buildingArea || '--' }}
                      </n-ellipsis>
                    </div>
                  </div>
                  <n-icon
                    :class="{ 'rotate-180': resource.isCollapsed }"
                    class="transition-transform duration-300"
                  >
                    <ChevronDown />
                  </n-icon>
                </div>
                <n-button
                  v-if="form.data.rtenantryResourceList.length > 1"
                  quaternary
                  circle
                  type="error"
                  size="small"
                  @click="deleteResource(formIndex, resourceIndex)"
                >
                  <template #icon>
                    <n-icon>
                      <Close />
                    </n-icon>
                  </template>
                </n-button>
              </div>

              <div v-show="!resource.isCollapsed">
                <div class="w_a_title">承租资源信息：</div>
                <n-form-item
                  :path="`rtenantryResourceList[${resourceIndex}].projectName`"
                  label="项目名称："
                  :rule="{
                    required: true,
                    message: '请输入项目名称',
                    trigger: ['input', 'blur']
                  }"
                >
                  <n-input
                    v-model:value="resource.projectName"
                    placeholder="请输入项目名称"
                    maxlength="50"
                    show-count
                    clearable
                  />
                </n-form-item>
                <n-form-item
                  label="资源位置："
                  :path="`rtenantryResourceList[${resourceIndex}].areaId`"
                  :rule="{
                    required: true,
                    message: '请选择资源位置',
                    trigger: ['input', 'blur']
                  }"
                >
                  <n-input
                    v-model:value="resource.locationName"
                    readonly
                    placeholder="请选择资源位置"
                    @click="showBuildBtn(formIndex, resourceIndex, resource)"
                  />
                </n-form-item>
                <n-form-item
                  :path="`rtenantryResourceList[${resourceIndex}].resourceName`"
                  label="资源名称："
                  :rule="{
                    required: true,
                    message: '请输入资源名称',
                    trigger: ['input', 'blur']
                  }"
                >
                  <n-input
                    v-model:value="resource.resourceName"
                    placeholder="请输入资源名称"
                    maxlength="20"
                    show-count
                    clearable
                  />
                </n-form-item>
                <n-form-item
                  :path="`rtenantryResourceList[${resourceIndex}].resourceTypeCode`"
                  label="资源类型："
                  :rule="{
                    required: true,
                    message: '请选择资源类型',
                    trigger: ['change', 'blur']
                  }"
                >
                  <n-select
                    v-model:value="resource.resourceTypeCode"
                    placeholder="请选择资源类型"
                    :options="resourceTypeOptions"
                    label-field="paramValue"
                    value-field="paramCode"
                    clearable
                  />
                </n-form-item>
                <n-form-item
                  :path="`rtenantryResourceList[${resourceIndex}].buildingArea`"
                  label="建筑面积："
                  :rule="[
                    {
                      required: true,
                      message: '请输入建筑面积',
                      trigger: ['input', 'blur']
                    },
                    {
                      validator: (rule, value) => decimal(value),
                      message: '建筑面积最多只能小数点后4位',
                      trigger: ['input', 'blur']
                    }
                  ]"
                >
                  <n-input
                    v-model:value="resource.buildingArea"
                    placeholder="请输入建筑面积"
                    clearable
                  />
                </n-form-item>
                <n-form-item
                  :path="`rtenantryResourceList[${resourceIndex}].usableArea`"
                  label="使用面积："
                  :rule="[
                    {
                      required: true,
                      message: '请输入使用面积',
                      trigger: ['input', 'blur']
                    },
                    {
                      validator: (rule: any, value: string) => decimal(value),
                      message: '使用面积最多只能小数点后4位',
                      trigger: ['input', 'blur']
                    }
                  ]"
                >
                  <n-input
                    v-model:value="resource.usableArea"
                    placeholder="请输入使用面积"
                    clearable
                  />
                </n-form-item>
                <n-form-item
                  :path="`rtenantryResourceList[${resourceIndex}].calculatedRentalArea`"
                  label="计租面积："
                  :rule="[
                    {
                      required: true,
                      message: '请输入计租面积',
                      trigger: ['input', 'blur']
                    },
                    {
                      validator: (rule: any, value: string) => decimal(value),
                      message: '计租面积最多只能小数点后4位',
                      trigger: ['input', 'blur']
                    }
                  ]"
                >
                  <n-input
                    v-model:value="resource.calculatedRentalArea"
                    placeholder="请输入计租面积"
                    clearable
                  />
                </n-form-item>
                <n-form-item
                  :path="`rtenantryResourceList[${resourceIndex}].rentCalculationModeCode`"
                  label="计租模式："
                  :rule="{
                    required: true,
                    message: '请选择计租模式',
                    trigger: ['change', 'blur']
                  }"
                >
                  <n-select
                    v-model:value="resource.rentCalculationModeCode"
                    placeholder="请选择计租模式"
                    :options="rentCalculationModeOptions"
                    label-field="paramValue"
                    value-field="paramCode"
                    clearable
                  />
                </n-form-item>
              </div>
            </div>

            <div class="add-resource-btn">
              <n-button
                quaternary
                size="small"
                style="color: #527cff"
                @click="addNewResource(formIndex)"
              >
                + 新增承租资源信息
              </n-button>
            </div>
          </n-form>
        </div>
      </div>

      <div class="add-form-btn" @click="addNewForm">
        <n-button type="primary"> 新增承租信息 </n-button>
      </div>
    </div>
    <ZoningComp ref="ZoningRef" @update="handleGisUpdate" />
  </ComDrawerA>
</template>

<script lang="ts" setup>
import ComDrawerA from '@/components/drawer/ComDrawerA.vue'
import { ACTION } from '../../constant'
import {
  onBeforeUnmount,
  onMounted,
  ref,
  watch,
  nextTick,
  provide,
  Ref
} from 'vue'
import type { FormInst, FormRules, FormItemRule } from 'naive-ui'
import { imageUpCom } from '@/components/upload'
import { IUploadRes } from '@/components/upload/type'
import {
  getTenantDetail,
  getUnitList,
  queryDictList,
  queryTenantManageArea,
  queryUpdateTenant,
  queryTenantManageAreaInRes,
  getErecordUnitId
} from '../../fetchData.ts'
import PlaneComp from '@/views/tenantry/tenantManagement/server/comp/aside/plane.vue'
import ZoningComp from './zoningComp.vue'
import cloneDeep from 'lodash-es/cloneDeep'
import { uniqBy } from 'lodash-es'
import dayjs from 'dayjs'
import { $toast } from '@/common/shareContext/useToastCtx.ts'
import { useAuthStore } from '@/store/modules'
import { decimal, isMobilePhone, stringCheck } from '@/utils/inputValidate.ts'
import { Close, ChevronDown } from '@vicons/ionicons5'

const props = defineProps({
  id: {
    type: String,
    default: ''
  }
})
const showBuild = ref(false)
const areaInputRef = ref<any>()
const mapDisabled = ref(false)
const showFloor = ref(false)
const confirmSelectArea = ref<Record<string, any>[]>([]) // 确认楼层房间选中数据
const selectAreaInfo = ref<Record<string, any>[]>([]) // 编辑回显时接口返回的数据 用作删除判断
const store = useAuthStore()
const isShowEdit = ref(false)

const emits = defineEmits(['action'])
const formRefs = ref<FormInst[]>([])
const planeRef = ref<any>()
const buildData = ref<Record<string, any>[]>([])
const floorData = ref<Record<string, any>[]>([])
const selectArea = ref<Record<string, any>[]>([])
const finishSelectAreaInfo = ref<any[]>([])
const fileUpList = ref<any>([])
const isNotLocalBool = ref(false)
const sendFrom = ref<any>(init())
const resourceTypeOptions = ref([])
const tenantryNatureOptions = ref([])
const tenantrySourceOptions = ref([])
const projectManagementOptions = ref([])
// 计租模式
const rentCalculationModeOptions = ref([])
const baseInfo = ref<any>({})
const ZoningRef = ref()
const erecordUnitId = ref<string | undefined>('')
//provide
provide<Ref<string | undefined>>('erecordUnitId', erecordUnitId)

const rules: FormRules = {
  effectiveContractNo: {
    required: true,
    message: '请输入当前生效合同编号',
    trigger: ['input', 'blur']
  },
  contractStartTime: {
    required: true,
    message: '请选择合同开始时间',
    trigger: 'blur'
  },
  fileData: [
    {
      required: true,
      trigger: ['change', 'blur'],
      validator: (rule: FormItemRule, value: any) => {
        return new Promise<void>((resolve, reject) => {
          // 获取当前验证的表单索引
          const formIndex = Number(rule.field?.split('.')[0]) || 0

          // 检查当前表单的附件
          const form = formList.value[formIndex]
          if (!form?.data.contractAttachments) {
            reject(new Error(`承租信息 ${formIndex + 1} 请上传合同附件`))
          } else {
            resolve()
          }
        })
      }
    }
  ],
  tenantryAreaVoList: {
    type: 'array',
    required: true,
    message: '请选择资源位置',
    trigger: ['change', 'blur']
  }
}
const areaName = ref('')
const buildAndFloor = ref<string[]>([])

// 表单列表
const formList = ref<
  { isCollapsed: boolean; data: any; fDate: any; fileList: IUploadRes[] }[]
>([
  {
    isCollapsed: false,
    data: init(),
    fDate: null, // 为每个表单添加独立的日期值
    fileList: [] // 为每个表单添加独立的文件列表
  }
])

// 折叠/展开
function toggleCollapse(index: number) {
  formList.value[index].isCollapsed = !formList.value[index].isCollapsed
}

// 新增表单
function addNewForm() {
  const newFormIndex = formList.value.length
  formList.value.push({
    isCollapsed: false,
    data: {
      ...init(),
      // 为每个表单添加独立的 fileData 验证路径
      _fileDataPath: `${newFormIndex}.fileData`
    },
    fDate: null, // 新表单也添加独立的日期值
    fileList: []
  })
  formRefs.value.push(null as any)
}

// 删除表单
function deleteForm(index: number) {
  if (formList.value.length <= 1) {
    $toast.warning('至少保留一条承租信息')
    return
  }

  formList.value.splice(index, 1)
  formRefs.value.splice(index, 1)
  // 更新剩余表单的验证路径
  formList.value.forEach(
    (form: { data: { _fileDataPath: string } }, i: number) => {
      form.data._fileDataPath = `${i}.fileData`
    }
  )
}
const onChangeData = (value: any, formIndex: number) => {
  const form = formList.value[formIndex]
  if (value && value[0] && value[1]) {
    try {
      const _stime = dayjs(value[0]).format('YYYY-MM-DD HH:mm:ss')
      const _etime = dayjs(value[1]).format('YYYY-MM-DD HH:mm:ss')

      // 只更新当前表单的日期信息
      form.data.contractStartTime = _stime as any
      form.data.contractEndTime = _etime as any

      // 计算到期天数
      // const today = dayjs()
      const endDate = dayjs(_etime)
      form.data.contractExpirationDays = (endDate.diff(_stime, 'day') +
        '') as any
    } catch (error) {
      console.error('日期处理错误:', error)
      form.data.contractStartTime = null
      form.data.contractEndTime = null
      form.data.contractExpirationDays = null
    }
  } else {
    form.data.contractStartTime = null
    form.data.contractEndTime = null
    form.data.contractExpirationDays = null
  }
}
function init() {
  return {
    effectiveContractNo: '', // 当前生效合同编号
    contractStartTime: null, // 合同开始时间
    contractEndTime: null, // 合同结束时间
    contractExpirationDays: null, // 合同到期天数
    contractAttachments: '', // 上传合同附件id
    contractAttachmentFiles: [], // 添加合同附件文件数组
    rtenantryResourceList: [
      {
        isCollapsed: false, // 添加折叠状态
        projectName: '', // 项目名称
        tenantryAreaVoList: [], // 资源位置名称
        resourceName: '', // 资源名称
        resourceTypeCode: null, // 资源类型
        buildingArea: '', // 建筑面积
        usableArea: '', // 使用面积
        calculatedRentalArea: '', // 计租面积
        rentCalculationModeCode: null, // 计租模式
        platformUnitId: '',
        locationName: '',
        areaId: '',
        id: ''
      }
    ]
  }
}

async function handleSubmit() {
  try {
    // 1. 遍历所有表单进行校验
    for (let formIndex = 0; formIndex < formList.value.length; formIndex++) {
      const formRef = formRefs.value[formIndex]
      if (!formRef) continue

      // 2. 校验每个表单
      await new Promise((resolve, reject) => {
        formRef.validate(async (errors) => {
          if (errors) {
            console.error('表单校验错误:', errors)
            $toast.error(errors[0]?.[0]?.message)
            return
          }
          resolve(true)
        })
      })
    }

    // 4. 所有校验通过后处理提交数据
    const formData = formList.value.map((form) => ({
      ...form.data,
      rtenantryResourceList: form.data.rtenantryResourceList.map(
        (resource: any, index: number) => ({
          sort: index + 1,
          ...resource
        })
      )
    }))
    console.log('formData', formData)
    delete baseInfo.value.rtenantryInfoList

    // 5. 提交数据
    await queryUpdateTenant({ rtenantryInfoList: formData, ...baseInfo.value })
    handleClose()
    emits('action', { action: ACTION.BLACK })
    reset()
  } catch (error: any) {
    console.error('表单校验失败:', error)
    return false
  }
}

async function getDetail(id: string) {
  try {
    const res = await getTenantDetail({ tenantryId: id })
    if (res.code != 'success') return
    // 清空现有表单列表
    formList.value = []
    baseInfo.value = res.data
    // 获取租赁信息列表
    const tenantryList = res.data.rtenantryInfoList || []

    // 遍历租赁信息创建表单
    tenantryList.forEach((tenantryData: any) => {
      const formItem: any = {
        isCollapsed: false,
        data: {
          contractStartTime: tenantryData.contractStartTime,
          contractEndTime: tenantryData.contractEndTime,
          contractExpirationDays: String(tenantryData.contractExpirationDays),
          contractAttachments: tenantryData.contractAttachments,
          contractAttachmentFiles: tenantryData.contractAttachmentFiles,
          effectiveContractNo: tenantryData.effectiveContractNo,
          // 处理资源列表
          rtenantryResourceList:
            tenantryData.rtenantryResourceList?.map((resource: any) => ({
              id: resource.id,
              isCollapsed: false,
              projectName: resource.projectName,
              resourceName: resource.resourceName,
              resourceTypeCode: resource.resourceTypeCode,
              buildingArea: resource.buildingArea,
              usableArea: resource.usableArea,
              areaId: resource.areaId,
              locationName: resource.locationName,
              calculatedRentalArea: resource.calculatedRentalArea,
              rentCalculationModeCode: resource.rentCalculationModeCode
            })) || []
        },
        fDate:
          tenantryData.contractStartTime && tenantryData.contractEndTime
            ? [
                new Date(tenantryData.contractStartTime),
                new Date(tenantryData.contractEndTime)
              ]
            : null,
        fileList: tenantryData.contractAttachmentFiles || []
      }

      formList.value.push(formItem)
    })

    // 处理初始化显示楼层楼栋资源
    formList.value[0]?.data.rtenantryResourceList.forEach(
      async (formData: any) => {
        let getAreaData = await getTenantManageAreaInRes(
          tenantryId.value,
          formData.id
        )
        console.log('xixixixi', getAreaData)
        if (getAreaData.id) {
          formData.areaId = getAreaData.id || '' //区域位置
          formData.buildingId = getAreaData.buildId || ''
          formData.floorId = getAreaData.floorId || ''
          formData.resId = formData.id //资源id
          formData.regionalType = getAreaData.foreignKeyRegionalType || '' //资源类型
        }
      }
    )
    console.log('formList.value', formList.value)

    // 处理日期回显
    if (
      formList.value[0]?.data.contractStartTime &&
      formList.value[0]?.data.contractEndTime
    ) {
      formList.value[0].fDate = [
        new Date(formList.value[0].data.contractStartTime),
        new Date(formList.value[0].data.contractEndTime)
      ]
    }

    // 处理附件回显 - 使用 nextTick 确保组件更新
    await nextTick()
    if (tenantryList[0]?.contractAttachmentFiles?.length > 0) {
      fileUpList.value = [...tenantryList[0].contractAttachmentFiles]
    } else {
      fileUpList.value = []
    }
  } catch (error) {
    console.error('获取详情失败:', error)
  }
}

// 文件更新处理函数
function handleUpdate(res: IUploadRes[], data: any[], formIndex: number) {
  console.log('res', res, data)
  const form = formList.value[formIndex]
  form.fileList = res
  form.data.contractAttachmentFiles = res

  if (res && res.length > 0) {
    const fileIdArr = res.map((item) => item.id)
    form.data.contractAttachments = fileIdArr.join(',')
  } else {
    form.data.contractAttachments = ''
  }
  console.log('form', form)
}

// 客户性质
async function getTenantryNatureOpt() {
  queryDictList({ dictType: 'khxz' }).then((res: any) => {
    if (res.code != 'success') return
    if (res.data.length > 0) {
      tenantryNatureOptions.value = res.data
      tenantryNatureOptions.value.forEach((item: any) => {
        item.paramCode = item.paramCode + ''
      })
    }
  })
}

// 客户来源
async function getTenantrySourceOpt() {
  queryDictList({ dictType: 'czfly' }).then((res: any) => {
    if (res.code != 'success') return
    if (res.data.length > 0) {
      tenantrySourceOptions.value = res.data
      tenantrySourceOptions.value.forEach((item: any) => {
        item.paramCode = item.paramCode + ''
      })
    }
  })
}

// 项目经营单位
async function getProjectManagementOpt() {
  getUnitList({ unitId: store.userInfo.unitId }).then((res: any) => {
    if (res.code != 'success') return
    if (res.data.length > 0) {
      projectManagementOptions.value = res.data
    }
  })
}

// 租资源类型
async function getResourceTypeOptionsOpt() {
  queryDictList({ dictType: 'zylx' }).then((res: any) => {
    if (res.code != 'success') return
    resourceTypeOptions.value = res.data
    resourceTypeOptions.value.forEach((item: any) => {
      item.paramCode = item.paramCode + ''
    })
  })
}

// 计租模式
async function getrentCalculationModeOpt() {
  queryDictList({ dictType: 'jzmo' }).then((res: any) => {
    if (res.code != 'success') return
    if (res.data.length > 0) {
      rentCalculationModeOptions.value = res.data
      rentCalculationModeOptions.value.forEach((item: any) => {
        item.paramCode = item.paramCode + ''
      })
    }
  })
}

// 获取当前列表单位erecordUnitId
async function getErecordId(orgCode: string) {
  getErecordUnitId({ orgCode }).then((res: any) => {
    if (res.code != 'success') return
    erecordUnitId.value = res.data
  })
}

function handleClose() {
  isShowEdit.value = false
  reset()
}

function handleNoSaveClose() {
  console.log('临时去小调用删除方法')
  // ZoningRef.value.delGridNoListData([])
  let delData: any[] = []
  formList.value.forEach((items) => {
    items.data.rtenantryResourceList.forEach((item: any) => {
      if (item.areaId && item.regionalType == 0) {
        delData.push({
          gridNoX: item.areaId,
          gridNoY: item.buildingId,
          gridNoZ: item.floorId
        })
      }
    })
  })
  console.log('delData', delData)
  if (delData.length > 0) {
    ZoningRef.value.delGridNoListData(delData)
    console.log('删除的临时数据', delData)
  }
  handleClose()
}

function reset() {
  formList.value = [
    {
      isCollapsed: false,
      data: init(),
      fDate: null, // 为每个表单添加独立的日期值
      fileList: [] // 为每个表单添加独立的文件列表
    }
  ]
  formRefs.value = [null as any]
  fileUpList.value = []
  selectArea.value = []
  confirmSelectArea.value = []
  finishSelectAreaInfo.value = []
  areaName.value = ''
  buildAndFloor.value = []
  isNotLocalBool.value = false
}
async function showDialog(id: string, erecordUnitIdStr: string) {
  erecordUnitId.value = erecordUnitIdStr
  isNotLocalBool.value = false
  selectArea.value = []
  fileUpList.value = []
  floorData.value = []
  buildData.value = []
  finishSelectAreaInfo.value = []
  sendFrom.value = init()
  tenantryId.value = id
  await getTenantryNatureOpt() // 客户性质
  await getTenantrySourceOpt() // 客户来源
  await getProjectManagementOpt() // 项目经营单位
  await getResourceTypeOptionsOpt() // 资源类型
  await getrentCalculationModeOpt()
  await getErecordId(erecordUnitIdStr) // 使用传入的参数而不是ref值
  await getDetail(id)
  await queryForeignKeyRegional(id) // 查询已选区域
  isShowEdit.value = true
}

// 获取承租方所有区域
async function queryForeignKeyRegional(tenantryId: string) {
  queryTenantManageArea({ tenantryId }).then((res: { data: never[] }) => {
    const data = res.data || []
    finishSelectAreaInfo.value = data
    selectAreaInfo.value = data
  })
}

const tenantryId = ref('')

async function getTenantManageAreaInRes(tenantryId: string, resId: string) {
  let res = await queryTenantManageAreaInRes({ tenantryId, resId })
  if (res.code == 'success') {
    if (res.data.length > 0) {
      return res.data[0]
    } else {
      return {}
    }
  } else {
    return {}
  }
}

function showBuildBtn(formIndex, resourceIndex, resources) {
  console.log(resources, 'resources-----------')
  let areaId = resources.areaId ? resources.areaId : ''
  let buildingId = resources.buildingId ? resources.buildingId : ''
  let floorId = resources.floorId ? resources.floorId : ''
  let foreignKeyRegionalName = resources.locationName
    ? resources.locationName
    : ''
  let resId = resources.resId ? resources.resId : ''
  let regionalType = resources.regionalType ? resources.regionalType : ''
  ZoningRef.value.showDialog({
    formIndex,
    resourceIndex,
    areaId,
    tenantryId: tenantryId.value,
    foreignKeyRegionalName,
    buildingId,
    floorId,
    resId,
    regionalType,
    unitId: erecordUnitId.value // 直接传递unitId
  })
}

function handleGisUpdate(data: any) {
  console.log(data, '选择的gis数据')
  console.log(
    formList.value[data.formIndex].data.rtenantryResourceList[
      data.resourceIndex
    ],
    '列表??????????的数据'
  )
  formList.value[data.formIndex].data.rtenantryResourceList[
    data.resourceIndex
  ].locationName = data.foreignKeyRegionalName
  formList.value[data.formIndex].data.rtenantryResourceList[
    data.resourceIndex
  ].areaId = data.gridNo
  formList.value[data.formIndex].data.rtenantryResourceList[
    data.resourceIndex
  ].buildingId = data.buildingId
  formList.value[data.formIndex].data.rtenantryResourceList[
    data.resourceIndex
  ].floorId = data.floorId
  formList.value[data.formIndex].data.rtenantryResourceList[
    data.resourceIndex
  ].regionalType = data.regionalType
}

function handleTip() {
  if (mapDisabled.value) $toast.warning('该楼层或楼栋已被接管')
}

// 点击其他地方 关闭下拉选择框
// function onClick(e: any) {
//   if (areaInputRef.value?.contains(e.target)) return
//   showBuild.value = false
//   showFloor.value = false
// }

// onBeforeUnmount(() => {
//   document.removeEventListener('click', onClick)
// })

// onMounted(() => {
//   document.addEventListener('click', onClick)
// })

watch(
  () => fileUpList.value,
  (newValue: any[]) => {
    // 更新所有表单的附件信息和验证状态
    formList.value.forEach((form: any, index: number) => {
      if (newValue && newValue.length > 0 && Array.isArray(newValue)) {
        const fileIdArr: string[] = []
        newValue.forEach((item: any) => {
          fileIdArr.push(item.id)
        })
        form.data.contractAttachments = fileIdArr.join(',')
        // 更新表单验证状态
        form.data._fileDataPath = `${index}.fileData`
      } else {
        form.data.contractAttachments = ''
        form.data._fileDataPath = `${index}.fileData`
      }
    })
  },
  {
    deep: true
  }
)

watch(
  () => finishSelectAreaInfo,
  () => {
    if (finishSelectAreaInfo.value && finishSelectAreaInfo.value.length > 0) {
      isNotLocalBool.value = false
    } else {
      console.log('出发了')
      isNotLocalBool.value = true
    }
  },
  {
    deep: true, // 监听深层属性的变化
    immediate: false
  }
)

defineExpose({
  showDialog
})

// getFloorUnitId();
defineOptions({ name: 'serverEditDialogIndex' })

// 删除资源信息
function deleteResource(formIndex: number, resourceIndex: number) {
  const form = formList.value[formIndex]
  if (form.data.rtenantryResourceList.length <= 1) {
    $toast.warning('至少保留一条承租资源信息')
    return
  }
  form.data.rtenantryResourceList.splice(resourceIndex, 1)
}

// 新增资源信息
function addNewResource(formIndex: number) {
  const form = formList.value[formIndex]
  form.data.rtenantryResourceList.push({
    isCollapsed: false, // 添加折叠状态
    projectName: '', // 项目名称
    tenantryAreaVoList: [], // 资源位置名称
    resourceName: '', // 资源名称
    resourceTypeCode: null, // 资源类型
    buildingArea: '', // 建筑面积
    usableArea: '', // 使用面积
    calculatedRentalArea: '', // 计租面积
    rentCalculationModeCode: null, // 计租模式
    buildingId: '',
    buildingName: '',
    floorId: '',
    floorName: '',
    platformUnitId: ''
  })
}

// 添加资源折叠/展开函数
function toggleResourceCollapse(formIndex: number, resourceIndex: number) {
  const resource =
    formList.value[formIndex].data.rtenantryResourceList[resourceIndex]
  resource.isCollapsed = !resource.isCollapsed
}

// 处理表单引用
const handleFormRef = (el: FormInst | null, index: number) => {
  if (el) {
    formRefs.value[index] = el
  } else {
    formRefs.value[index] = null as any
  }
}
</script>

<style scoped lang="scss">
.w_dialog_box {
  height: calc(100% - 200px) !important;
  //background-color: hotpink;
  width: 100%;
  padding: 20px;

  .w_a_title {
    height: 32px;
    line-height: 32px;
    color: #527cff;
  }
}

.select-box {
  display: flex;
  align-items: center;
  margin-top: 10px;

  .select-item {
    height: 36px;
    line-height: 36px;
    margin: 4px 4px 0 0;
    padding: 0 8px;
    background-color: #f1f3f9;
    border-radius: 4px;
  }
}

.add-btn {
  display: flex;
  justify-content: end;
  align-items: center;
  margin: 10px 0 20px;

  .add-icon {
    margin-right: 6px;
    width: 24px;
    height: 24px;
    background: url('@/assets/add.png') no-repeat center;
    cursor: pointer;
  }
}

.area-item {
  margin-bottom: 10px;
  padding: 10px 14px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #333;
  background: #f5f5f5;
  border-radius: 3px;

  div {
    width: calc(100% - 30px);
  }

  .delete-btn {
    display: none;
    cursor: pointer;
    width: 14px;
    height: 14px;
    background: url('@/assets/delete.png') no-repeat center;
  }

  &:hover {
    color: #527cff;
    background: #f1f3f9;

    .delete-btn {
      display: block;
    }
  }
}

.area-name {
  width: 180px;
  height: 34px;
  line-height: 34px;
  padding: 0 10px;
  background-color: #fff;
  border: 1px solid rgb(224, 224, 230);
  border-radius: 3px;
}

.build-ul {
  position: absolute;
  top: 36px;
  left: 0;
  width: 360px;
  height: 200px;
  padding: 6px 0;
  line-height: 34px;
  //background-color: #fff;
  border-radius: 4px;
  z-index: 9;

  .build-box {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 180px;
    background-color: #fff;
  }

  .floor-box {
    position: absolute;
    top: 0;
    left: 180px;
    height: 100%;
    width: 180px;
    background-color: #fff;
  }

  .item {
    display: flex;
    align-items: center;
    padding-left: 10px;
    cursor: pointer;

    &:hover {
      background-color: #f5f4f4;
    }

    .text {
      position: relative;
      flex: 1;
      width: 0;
      margin-left: 6px;

      &:after {
        content: '';
        position: absolute;
        top: 50%;
        right: 6px;
        transform: translateY(-50%);
        width: 12px;
        height: 12px;
        background: url('../assets/expand.png');
        background-size: cover;
      }
    }
  }
}

.form-item {
  margin-bottom: 20px;
  border: 1px solid #e0e0e6;
  border-top: none;
  padding: 0 10px;
}

.collapse-header {
  height: 40px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  background: #f5f5f5;
  border: 1px solid #e0e0e6;
  border-radius: 5px;
  transition: all 0.3s;
  border-color: #2080f0;

  // 左侧标题和箭头
  > div:first-child {
    cursor: pointer;
    flex: 1;
    display: flex;
    align-items: center;
    gap: 8px;

    .n-icon {
      font-size: 16px;
    }

    // 添加旋转样式
    .rotate-180 {
      transform: rotate(180deg);
    }
  }

  // 删除按钮样式保持不变
  .n-button {
    opacity: 0.7;
    transition: opacity 0.2s;

    &:hover {
      opacity: 1;
    }
  }
}

// 展开的容区域
div[v-show='!form.isCollapsed'] {
  border: 1px solid #e0e0e6;
  border-top: none;
  border-radius: 0 0 5px 5px;
  padding: 16px;
}

.add-form-btn {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.resource-section {
  margin-bottom: 20px;
  border: 1px solid #e0e0e6;
  padding: 16px;
  border-radius: 4px;
}

.resource-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;

  h3 {
    font-size: 16px;
    color: #527cff;
  }
}

.add-resource-btn {
  width: 140px;
  text-align: center;
  border: 1px solid #527cff;
  border-radius: 5px;
  margin-left: auto;
  display: block;
  margin-top: -10px;
  margin-bottom: 20px;
}
</style>
