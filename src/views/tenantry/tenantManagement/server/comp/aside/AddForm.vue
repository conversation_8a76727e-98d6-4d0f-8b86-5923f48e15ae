<template>
  <n-scrollbar>
    <n-form
      class="region-form"
      ref="formRef"
      label-placement="left"
      label-width="120"
      label-align="right"
      require-mark-placement="left"
      :model="regionalForm"
      :rules="rules"
    >
      <n-form-item label="区域名称：" path="regionalName">
        <n-input v-model:value="regionalForm.regionalName" maxlength="20" />
      </n-form-item>

      <n-form-item label="管控部门：" path="controlDeptId">
        <org-cascader :unit-id="orgCode" v-model:value="regionalForm.controlDeptId" @update:value="handleOrgChange" />
      </n-form-item>

      <n-form-item label="管控责任人：" path="controlDeptDirectorId">
        <n-select
          @click="isShowUser = true"
          v-model:value="regionalForm.controlDeptDirectorIds"
          :options="checkUserOptions"
          placeholder="请选择"
          label-field="checkUserName"
          value-field="checkUserIdArray"
          multiple
          clearable
        />
      </n-form-item>

      <div class="border-[1px] border-[#ccc] rounded-[8px] px-[20px] py-[10px]">
        <p class="mb-[20px] font-bold text-[16px]">区域划分</p>

        <n-form-item label="楼栋：" path="regionalName" label-width="auto">
          <n-select
            v-model:value="regionalForm.buildingId"
            :options="buildData"
            @update:value="changeBuild"
            placeholder="请选择"
            label-field="buildingName"
            value-field="buildingId"
            clearable
          />
        </n-form-item>
        <n-form-item label="楼层：" path="regionalName" label-width="auto">
          <n-select
            v-model:value="regionalForm.floorId"
            :options="floorData"
            :placeholder="regionalForm.buildingId ? '请选择' : '请先选择楼栋'"
            @update:value="changeFloor"
            label-field="floorName"
            value-field="floorId"
            clearable
          />
        </n-form-item>

        <div class="w-full h-[300px]">
          <PlaneComp
            ref="planeRef"
            :areaName="areaName"
            :buildingId="regionalForm.buildingId"
            :floorId="regionalForm.floorId"
            @update="updateArea"
            @finish="finishAdd"
          />
        </div>
      </div>

      <ul class="select-box" v-if="selectArea.length">
        <li v-for="(item, index) of selectArea" :key="index" class="select-item">
          {{ item.text }}
        </li>
      </ul>
      <div class="add-btn">
        <span class="add-icon" @click="addArea"></span>
        <span>添加</span>
      </div>

      <div class="border-[1px] border-[#ccc] rounded-[8px] px-[20px] py-[10px]">
        <p class="mb-[20px] font-bold text-[16px]">已选区域</p>

        <div v-for="(item, index) of finishSelectAreaInfo" :key="index" class="area-item">
          <div>{{ item.regionalName }}</div>
          <span class="delete-btn" @click="deleteArea(item.foreignKeyId)"></span>
        </div>
      </div>

      <SelectUser
        v-model:showModal="isShowUser"
        title="选择用户"
        :unitId="regionalForm.controlDeptId as string"
        @close="isShowUser = false"
        @success="selectedUser"
      ></SelectUser>
    </n-form>
  </n-scrollbar>
</template>

<script setup lang="ts">
import { computed, inject, onMounted, Ref, ref, watch } from 'vue';
import { IForeignKeyRegional, IRegionalForm } from '@/views/regionalMgr/type.ts';
import { nanoid } from 'nanoid';
import OrgCascader from '@/views/components/org-cascader/index.vue';
import SelectUser from '@/views/components/select-user/index.vue';
import { useToastCtx } from '@/common/shareContext';
import {
  addRegionalManagement,
  getBuildingListByUnitId,
  getFloorListByUnitIdAndBuilding,
} from '@/views/regionalMgr/fetchData.ts';
import PlaneComp from './plane.vue';
import cloneDeep from 'lodash-es/cloneDeep';
import { uniqBy } from 'lodash-es';

const $toast = useToastCtx();
const unitId = inject('unitId') as Ref<string>;
const orgCode = inject('orgCode') as Ref<string>;
const isShowUser = ref(false);

const formRef = ref();
const rules = {};

const regionalForm = ref<IRegionalForm>(initForm());
function initForm() {
  let form = {
    regionalName: '', // 区域名称
    controlDept: '', // 管控部门
    controlDeptId: null, // 管控部门ID
    controlDeptDirector: '', // 管控责任人
    controlDeptDirectorId: '', // 管控责任人ID
    controlDeptDirectorIds: [],
    unitId: '', //单位ID
    buildingId: null,
    floorId: null,
    id: '', // 主键
    regionalMsg: '',
    foreignKeyRegionalList: [],
  };
  form.unitId = unitId?.value;
  return form;
}

const checkUserOptions = ref<any>([]);
const planeRef = ref<any>();
const buildData = ref<Record<string, any>[]>([]);
const floorData = ref<Record<string, any>[]>([]);
const selectArea = ref<Record<string, any>[]>([]);
const finishSelectAreaInfo = ref<IForeignKeyRegional[]>([]);
const areaName = computed(() => {
  const build = buildData.value.find((item) => item.buildingId === regionalForm.value.buildingId);
  const buildName = build?.buildingName;
  const floor = floorData.value.find((item) => item.floorId === regionalForm.value.floorId);
  const floorName = floor?.floorName;
  return floorName ? `${buildName}/${floorName}` : buildName;
});

function getBuild() {
  getBuildingListByUnitId(unitId.value).then((res) => {
    buildData.value = res.data || [];
  });
}

function getFloor(buildingId: string) {
  if (!buildingId) return (floorData.value = []);
  getFloorListByUnitIdAndBuilding(unitId.value, buildingId).then((res) => {
    floorData.value = res.data || [];
  });
}

function changeBuild(val: string) {
  regionalForm.value.floorId = null;
  floorData.value = [];
  selectArea.value = [];
  getFloor(val);
}

function changeFloor() {
  selectArea.value = [];
}

function addArea() {
  if (!regionalForm.value.buildingId && !regionalForm.value.floorId) return $toast.warning('请先选择区域');
  if (!selectArea.value.length) {
    const buildId = cloneDeep(regionalForm.value.buildingId);
    const floorId = cloneDeep(regionalForm.value.floorId);
    const name = cloneDeep(areaName.value);

    finishSelectAreaInfo.value.push({
      adminCode: '',
      buildId,
      floorId,
      gridNo: '',
      foreignKeyId: floorId || buildId,
      regionalName: name,
      regionalType: floorId ? 2 : 1,
    });
    finishSelectAreaInfo.value = uniqBy(finishSelectAreaInfo.value, 'foreignKeyId');
  } else {
    planeRef.value.addAreaData();
  }
}

function finishAdd(val: IForeignKeyRegional) {
  const data = {
    ...val,
    buildId: cloneDeep(regionalForm.value.buildingId),
    floorId: cloneDeep(regionalForm.value.floorId),
  };
  finishSelectAreaInfo.value.push(data);
  finishSelectAreaInfo.value = uniqBy(finishSelectAreaInfo.value, 'foreignKeyId');
}

function deleteArea(id: string | null) {
  finishSelectAreaInfo.value = finishSelectAreaInfo.value.filter((item) => item.foreignKeyId !== id);
}

function updateArea(val: Record<string, any>[]) {
  selectArea.value = val;
}

function handleOrgChange(value: string, option: any) {
  regionalForm.value.controlDept = value ? option.text : '';
}

function selectedUser(val: any) {
  if (val) {
    checkUserOptions.value = val.map((e: any) => {
      return { checkUserIdArray: e.id, checkUserName: e.userName, userDeptId: e.userDeptId };
    });
    regionalForm.value.controlDeptDirectorIds = checkUserOptions.value.map((item: any) => item.checkUserIdArray);
    regionalForm.value.controlDeptDirector = checkUserOptions.value.map((item: any) => item.checkUserName).join(',');
  }
}

function reset() {
  regionalForm.value = initForm();
}

function handleSubmit() {
  return new Promise((resolve, reject) => {
    formRef.value?.validate(async (errors: any) => {
      if (!errors) {
        const params = Object.assign({}, regionalForm.value);
        params.foreignKeyRegionalList = cloneDeep(finishSelectAreaInfo.value);
        params.controlDeptDirectorId = regionalForm.value.controlDeptDirectorIds?.join(',') || '';
        addRegionalManagement(params)
          .then(() => {
            resolve('submitted');
            reset();
          })
          .catch(reject);
      } else {
        if (errors.length) {
          try {
            const first = errors[0][0];
            if (first.message) {
              $toast.error(first.message);
            }
          } catch (e) {}
        }
        reject(errors);
      }
    });
  });
}

onMounted(() => {
  watch(
    () => unitId?.value,
    (val: string) => {
      regionalForm.value.unitId = val;
    }
  );
});

getBuild();
defineExpose({ handleSubmit });
defineOptions({ name: 'RegionalAdd' });
</script>
<style lang="scss" scoped>
.region-form {
  height: calc(100vh - 150px);
}

.select-box {
  display: flex;
  align-items: center;
  margin-top: 10px;

  .select-item {
    height: 36px;
    line-height: 36px;
    margin: 4px 4px 0 0;
    padding: 0 8px;
    background-color: #f1f3f9;
    border-radius: 4px;
  }
}

.add-btn {
  display: flex;
  justify-content: end;
  align-items: center;
  margin: 10px 0 20px;

  .add-icon {
    margin-right: 6px;
    width: 24px;
    height: 24px;
    background: url('@/assets/add.png') no-repeat center;
    cursor: pointer;
  }
}

.area-item {
  margin-bottom: 10px;
  padding: 10px 14px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #333;
  background: #f5f5f5;
  border-radius: 3px;

  div {
    width: calc(100% - 30px);
  }

  .delete-btn {
    display: none;
    cursor: pointer;
    width: 14px;
    height: 14px;
    background: url('@/assets/delete.png') no-repeat center;
  }

  &:hover {
    color: #527cff;
    background: #f1f3f9;

    .delete-btn {
      display: block;
    }
  }
}
</style>
