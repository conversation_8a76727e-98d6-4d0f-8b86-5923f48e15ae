<template>
  <!--  <div class="import_box">-->
  <ComDrawerA :autoFocus="false" :footerPaddingBottom="25" :maskClosable="false" :show-action="false"
    @handle-negative="handleClose" @handle-positive="handleSubmit" class="!w-[500px]" v-model:show="isShow">
    <!--    <Edit ref="editRef" v-if="isEdit" @submitted="handleSubmitted" />-->
    <div class="w_dialog_box">
      <div class="w_down_box">
        <div class="w_down_title">
          <div class="w_down_title_yuan">!</div>
          <div>每次上传前请下载最新模板</div>
        </div>
        <div class="download">
          <n-icon size="30" class="pl-[12px]">
            <svg xmlns="http://www.w3.org/2000/svg" t="1725271998052" class="icon" viewBox="0 0 1024 1024" version="1.1"
              p-id="20549" width="200" height="200">
              <path
                d="M682.666667 42.666667H298.666667c-25.6 0-42.666667 17.066667-42.666667 42.666666v213.333334l426.666667 213.333333 170.666666 64 170.666667-64V298.666667l-341.333333-256z"
                fill="#21A366" p-id="20550" />
              <path d="M256 298.666667h426.666667v213.333333H256z" fill="#107C41" p-id="20551" />
              <path
                d="M1024 85.333333v213.333334h-341.333333V42.666667h298.666666c21.333333 0 42.666667 21.333333 42.666667 42.666666z"
                fill="#33C481" p-id="20552" />
              <path
                d="M682.666667 512H256v426.666667c0 25.6 17.066667 42.666667 42.666667 42.666666h682.666666c25.6 0 42.666667-17.066667 42.666667-42.666666v-213.333334l-341.333333-213.333333z"
                fill="#185C37" p-id="20553" />
              <path
                d="M588.8 256H256v597.333333h324.266667c29.866667 0 59.733333-29.866667 59.733333-59.733333V307.2c0-29.866667-21.333333-51.2-51.2-51.2z"
                opacity=".5" p-id="20554" />
              <path
                d="M546.133333 810.666667H51.2C21.333333 810.666667 0 789.333333 0 759.466667V264.533333C0 234.666667 21.333333 213.333333 51.2 213.333333h499.2c25.6 0 46.933333 21.333333 46.933333 51.2v499.2c0 25.6-21.333333 46.933333-51.2 46.933334z"
                fill="#107C41" p-id="20555" />
              <path
                d="M145.066667 682.666667L256 512 153.6 341.333333h81.066667l55.466666 106.666667c8.533333 12.8 8.533333 21.333333 12.8 25.6l12.8-25.6L375.466667 341.333333h76.8l-102.4 170.666667 106.666666 170.666667h-85.333333l-64-119.466667c0-4.266667-4.266667-8.533333-8.533333-17.066667 0 4.266667-4.266667 8.533333-8.533334 17.066667L226.133333 682.666667H145.066667z"
                fill="#FFFFFF" p-id="20556" />
              <path d="M682.666667 512h341.333333v213.333333h-341.333333z" fill="#107C41" p-id="20557" />
            </svg>
          </n-icon>
          <div class="w_down" @click="getDown">下载模版</div>
        </div>
      </div>

      <div class="mt-[10px]">
        <n-form ref="formRef" :model="sendFrom" :rules="rules" label-placement="left" label-width="auto"
          require-mark-placement="left">
          <div>
            <n-form-item label="导入文件：" path="contractAttachments">
              <fileExcel :data="fileData" @update="handleUpdate" @success="handleSuccess" :max="1"></fileExcel>
            </n-form-item>
          </div>
        </n-form>
        <div class="flex">
          <div class="w_res_l">导入结果:</div>
          <div class="w_res_r">
            <n-scrollbar style="max-height: 400px">
              <div>{{ sendFrom.result }}</div>
              <div v-for="(item, index) in sendFrom.messList" class="text-red-500" :key="index">
                {{ item }}
              </div>
            </n-scrollbar>
          </div>
        </div>
      </div>
      <div class="w_btn_bom">
        <n-button @click="handleClose">取消</n-button>
        <n-button class="w_btn_r" type="primary" :disabled="disableBtn" @click="handleSubmitted">确认</n-button>
      </div>
    </div>
  </ComDrawerA>
  <!--  </div>-->
</template>

<script lang="ts" setup>
import ComDrawerA from '@/components/drawer/ComDrawerB.vue';
import { ACTION, PROVIDE_KEY } from '../../constant.ts';
import { computed, useAttrs, inject, Ref, ref } from 'vue';
import type { FormInst, FormRules } from 'naive-ui';
import { FileUpload, fileExcel } from '@/components/upload';
import { IUploadRes } from '@/components/upload/type';
import { exportTep, saveTenant } from '../../fetchData.ts';
import { fileDownloader } from '@/utils/fileDownloader';
import { $toast } from '@/common/shareContext/useToastCtx.ts';

const fileData = ref<any[]>([]);
// 按钮置灰
const disableBtn = ref<boolean>(false);
// 弹框是否弹出
const isShow = ref<boolean>(false);
const emits = defineEmits(['action']);
const attrs = useAttrs();
const show = computed(() => !!attrs.show);

// const currentAction = inject(PROVIDE_KEY.currentAction) as Ref<IActionData>; // inject
// const isEdit = computed(() => currentAction.value.action === ACTION.EDIT);
const formRef = ref<FormInst | null>(null);

const sendFrom = ref<any>({
  contractAttachments: '', // 上传合同附件
  result: '',
  messList: [],
});

const rules: FormRules = {
  contractAttachments: {
    required: true,
    message: '请上传附件',
    trigger: ['input', 'blur'],
  },
};

function handleSubmit() {
  // editRef.value?.handleSubmit();
  saveTenant(sendFrom.value).then((res: any) => {
    console.log(res, 'add--');
  });
}

function handleSuccess(data: any) {
  if (data) {
    sendFrom.value.result = data.data.mes;
    if (data.data.mesAll.length > 0) {
      sendFrom.value.messList = data.data.mesAll;
    }
    if (data.code != 'success') {
      disableBtn.value = true;
    }
  }

  console.log('chenggong', data);
  // handleClose();
}

function handleUpdate(res: IUploadRes[]) {
  // inUpload.value = bol;
  console.log(res, '************');
  console.log(fileData.value, '++++++++++');
}

function handleSubmitted() {
  emits('action', { action: ACTION.SEARCH });
  handleClose();
}

function handleClose() {
  isShow.value = false;
}

function getDown() {
  fileDownloader(exportTep(), {
    filename: `模版下载` + '.xlsx',
  });
}

function showDialog() {
  sendFrom.value.result = '';
  sendFrom.value.messList = [];
  disableBtn.value = false;
  fileData.value = [];
  isShow.value = true;
}

defineExpose({
  showDialog,
});
defineOptions({ name: 'serverAddDialogIndex' });
</script>

<style scoped lang="scss">
//.n-drawer .n-drawer-content .n-drawer-body-content-wrapper{
//  height: 100% !important;
//  background: yellow;
//}

.w_dialog_box {
  //height: calc(100% - 80px) !important;
  height: 100% !important;
  //background-color: hotpink;
  width: 100%;
  padding: 20px;
  position: relative;

  .w_a_title {
    height: 32px;
    line-height: 32px;
    color: #527cff;
  }

  .w_btn_bom {
    //width: 300px;
    position: absolute;
    //background: red;
    right: 20px;
    bottom: 0;
  }
}

.w_btn_r {
  margin-left: 20px;
}

.w_down_box {
  height: 100px;
  width: 100%;
  background-color: #f1f1f1;
  padding-left: 20px;
  padding-top: 8px;

  .w_down_title {
    color: red;
    display: flex;
    align-items: center;
  }

  .w_down_title_yuan {
    height: 14px;
    width: 14px;
    border-radius: 50%;
    background-color: red;
    text-align: center;
    color: white;
    font-size: 12px;
    line-height: 16px;
    margin-right: 4px;
  }

  .download {
    //width: 30%;
    //width: 100%;
    margin-top: 5px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    //align-items: center;
    color: #2753bf;
    cursor: pointer;
  }
}

.w_down {
  height: 32px;
  line-height: 32px;
  color: #527cff;
  cursor: pointer;
}

.w_res_r {
  padding: 0 5px 5px;
  margin-left: 10px;
  width: 390px;
  min-height: 100px;
  max-height: 400px;
  //height: 500px;
  border: 1px solid #c0c4cc;
  background-color: #f1f1f1;
}
</style>
