<template>
  <div class="w-full h-full" id="planeGis"></div>
</template>

<script setup lang="ts">
import { onMounted, ref, watch } from 'vue';
import { ThreeService } from '@/views/gis/ThreeService.ts';
import useToastCtx from '@/common/shareContext/useToastCtx.ts';

interface IItem {
  buildingId: string | null;
  floorId: string | null;
  defaultArea: Record<string, any>[];
}
const props = withDefaults(defineProps<IItem>(), {
  buildingId: '',
  floorId: '',
  defaultArea: () => [], // 判断删除区域数据
});
const emits = defineEmits(['delete', 'add', 'remove']);
const deleteGridNo = ref<string[]>([]); // 删除区域gridNo数据 暂存
const toast = useToastCtx();
const selectArea = ref<Record<string, any>[]>([]); // 选中数据
// 责任管控区域(区域管理)
const gridAreaLayer = IndoorMap.GridAreaType.GridAreaLayerCZF;
let indoor: any = null;

// 初始化地图
function initThreeMap() {
  IndoorMap.init();
  ThreeService.initThreeMap();
  indoor = new IndoorMap(
    IndoorMap.Merge([window.CONST_GSCache, window.CONST_GSOptions, window.CONST_GSParams], {
      target: 'planeGis',
      tile: false, //底图是否可见
      sky: true, //开启天空
      // isVector: false,
      grid: true,
      gridLoad: true,
      gridFilter: function (ele: any) {
        // 编辑时 删除已选区域
        if (deleteGridNo.value.includes(ele.gridNo)) {
          const part = props.defaultArea.findIndex((item) => item.gridNo === ele.gridNo);
          return part !== -1;
        }

        ele.text = '已选';
      },
      gridTypeIds: gridAreaLayer,
    })
  );

  indoor.onGridAreaLoad = function () {
    const arr = selectArea.value.filter((item) => item.floorId === props.floorId);
    indoor.addGridDataVector(arr, true);
  };

  // 已选中区域点击
  indoor.onGridSelected = function (data: any) {
    if (data.gridNo) {
      const gridNos = props.defaultArea.map((item) => item.gridNo);
      if (!gridNos.includes(data.gridNo)) return toast.warning('该区域已被管控');

      // 编辑时 删除已选区域
      deleteGridNo.value.push(data.gridNo);
      indoor.showGridDataVector(undefined, undefined, true);
      emits('delete', data.gridNo);
    } else {
      selectArea.value = selectArea.value.filter((item) => item.objectId !== data.objectId);
      emits('remove', data);
      indoor.showGridDataVector(undefined, undefined, true);
    }
  };

  // 未选中区域点击
  indoor.onAreaSelected = function (data: any) {
    data.buildId = indoor.getIndoorDataState().buildId;
    data.typeColorCode = 'GAL_ZRGK_def';
    selectArea.value.push(data);
    emits('add', data);
    indoor.showGridDataVector(undefined, undefined, true);
  };
}

function addArea(item: any) {
  return new Promise((resolve) => {
    const params = {
      text: item.text,
      gridNo: true,
      typeColorCode: 'GAL_ZRGK_def',
      buildId: item.buildId,
      floorId: item.floorId,
    };
    IndoorMap.IndoorDo_InsertGridAreaSX(indoor, item.shape, undefined, gridAreaLayer, params, function () {
      const data = {
        adminCode: indoor.getIndoorDataState().adminCode,
        buildId: item.buildId,
        floorId: item.floorId,
        gridNo: params.gridNo,
        foreignKeyId: params.gridNo,
        regionalName: item.name ? item.name : item.regionalName,
        regionalType: 0,
        ...item,
      };
      resolve(data);
    });
  });
}

// 已保存后的数据变更
function deleteArea(gridNo: string) {
  if (!props.buildingId || !props.floorId) return;
  deleteGridNo.value.push(gridNo);
  indoor.showGridDataVector(undefined, undefined, true);
}

// 未保存的数据变更
function deleteNewArea(objectId: string) {
  if (!props.buildingId || !props.floorId) return;
  // 编辑时 已选区域删除
  selectArea.value = selectArea.value.filter((item) => item.objectId !== objectId);
  indoor.showGridDataVector(undefined, undefined, true);
}

async function finishAddArea(area: any[]) {
  const arr: any = [];
  area.forEach((item) => {
    arr.push(addArea(item));
  });
  return await Promise.all(arr);
}

watch([() => props.buildingId, () => props.floorId], ([v1, v2]) => {
  indoor.clearAll();
  if (!v1 || !v2) return;
  indoor.showFloorData(
    IndoorMap.ViewType.IndoorAreaVector,
    '', //单位id
    v1, //楼栋id
    v2, //楼层id
    // undefined, //图纸地址 （用于查询不到室内GIS数据时自动跳转，可缺省）
    '',
    function () {
      indoor.showGridDataVector(undefined, undefined, true);
    }
  );
});

onMounted(() => {
  initThreeMap();
});

defineExpose({ deleteArea, deleteNewArea, finishAddArea });

defineOptions({ name: 'PlaneComp' });
</script>

<style scoped lang="scss"></style>
