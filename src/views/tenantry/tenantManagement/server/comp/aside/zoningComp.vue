<template>
  <n-modal
    class="relative"
    style="width: 80%"
    :close-on-esc="false"
    :mask-closable="false"
    v-model:show="showMode"
  >
    <n-card
      style="width: 90%; background-color: #f8f8f8; color: #fff"
      class="zoning-card"
      title="选择区域"
      :bordered="false"
      size="huge"
      ref="back_box"
      preset="dialog"
      role="dialog"
      aria-modal="true"
    >
      <template #header-extra>
        <n-icon size="24" @click="delAreaFun">
          <AnOutlinedClose class="bg-[#fff]" />
        </n-icon>
      </template>

      <div class="h-[700px] relative">
        <div class="build-box">
          <div class="w-[60px]">楼栋：</div>
          <n-input v-model:value="areaName" readonly @click="showBuildBtn" />
          <div class="build-select" v-show="showBuild">
            <n-scrollbar>
              <div
                v-for="(item, index) of buildingData"
                :key="index"
                :class="[
                  'item',
                  buildingId === item.buildingId ? 'active' : ''
                ]"
                @click="handleUpdateBuild(item, true)"
              >
                <n-checkbox
                  :disabled="item.disabled"
                  :value="item.value"
                  v-model:checked="item.checked"
                  @update:checked="buildChecked($event, item)"
                ></n-checkbox>
                <p class="text truncate" @click="handleUpdateBuild(item)">
                  {{ item.buildingName }}
                </p>
              </div>
            </n-scrollbar>
          </div>
        </div>

        <div class="floor-box">
          <n-scrollbar>
            <div
              v-for="item of floorData"
              :key="item.floorId"
              :class="['item', item.floorId == floorId ? 'active' : '']"
            >
              <n-checkbox
                :disabled="item.disabled"
                :value="item.value"
                v-model:checked="item.checked"
                @update:checked="floorChecked($event, item)"
              ></n-checkbox>
              <p class="text truncate" @click="handleUpdateFloor(item)">
                {{ item.floorName }}
              </p>
            </div>
          </n-scrollbar>
        </div>

        <div class="select-area">
          <span>已选中区域：</span>
          <div class="flex-1 w-0">
            <n-ellipsis style="width: 100%">{{ defaultAreaText }} </n-ellipsis>
          </div>
          <div class="select-area-btn" @click="saveAreaFun">确认选择</div>
          <!--          <div class="select-area-btn" @click="delAreaFun">取消</div>-->
        </div>

        <n-spin
          :show="loading"
          description="loading..."
          class="w-full h-full"
          content-class="w-full h-full"
        >
          <div class="w-full h-full" @click="handleTip">
            <PlaneComp
              :class="mapDisabled ? '!pointer-events-none' : ''"
              ref="planeRef"
              :buildingId="buildingId"
              :floorId="floorId"
              :defaultArea="defaultArea"
              @add="addArea"
              @delete="deleteArea"
              @save="saveFun"
              @load="loading = true"
              @finish="finshFun"
            />
          </div>
        </n-spin>
      </div>
    </n-card>
  </n-modal>
</template>

<script setup lang="ts">
import { computed, inject, nextTick, onMounted, Ref, ref } from 'vue'
import { AnOutlinedClose } from '@kalimahapps/vue-icons'
// import {
//   queryBuildingRegionalList,
//   queryFloorRegionalList,
//   queryForeignKeyRegionalList,
//   updRegionalManagementForeignKeyList,
// } from '@/views/regionalMgr/fetchData.ts';
import PlaneComp from './planeNew.vue'
import useToastCtx from '@/common/shareContext/useToastCtx.ts'
import { sleep } from '@/utils/time.ts'
import {
  getBuildingListByUnitIdNew,
  getFloorListByUnitId,
  queryTenantManageAreaInRes,
  saveResArea
} from '@/views/tenantry/tenantManagement/server/fetchData.ts'

const $toast = useToastCtx()
const unitId = ref('')
const props = defineProps({
  regionalItem: {
    type: Object,
    default: () => {}
  }
})
const emits = defineEmits(['close', 'update'])
const planeRef = ref<any>()
const loading = ref(false)
const buildingData = ref<Record<string, any>[]>([])
const buildingId = ref('')
const showBuild = ref(false)
const areaName = ref('')
const showFloor = ref(false)
const floorData = ref<Record<string, any>[]>([])
const floorId = ref('')
const regionalForm = ref<Record<string, any>>(props.regionalItem)
const confirmArea = ref<Record<string, any>[]>([])
const saveConfirmArea = ref<Record<string, any>[]>([])
const defaultArea = ref<Record<string, any>[]>([])
const defaultAreaText = computed(() => {
  const arr = defaultArea.value.map((item) => item.foreignKeyRegionalName)
  return arr.join('、')
})
const selectFloorId = ref('')
const mapDisabled = ref(false)
const showMode = ref(false)
const formIndex = ref(0)
const resourceIndex = ref(0)
// function cancel() {
//   emits('close');
// }

const door = ref()
function finshFun(data: any) {
  loading.value = false
  door.value = data.indoor
  console.log(door.value, 'finshi保存的')
}

function addArea(data: any) {
  const part = floorData.value.find((item) => item.floorId === floorId.value)
  console.log(floorId.value)
  console.log(buildingId.value)
  console.log(part, 'part----------------')
  if (data.length > 0) {
    data[0].foreignKeyRegionalName = `${part?.buildingName}/${part?.floorName}/${data[0].text || '--'}`
    data[0].buildingName = `${part?.buildingName}`
    data[0].floorName = `${part?.floorName}`
    data[0].foreignKeyRegionalType = 0
    data[0].foreignKeyId = data[0].gridNo || ''
  }
  console.log(data, 'chufale add')
  console.log(floorData.value, 'chufale floorDataadd')
  defaultArea.value = data
  // updateArea();
  // add();
}

function deleteArea(gridNo: string) {
  defaultArea.value = defaultArea.value.filter(
    (item) => item.foreignKeyId !== gridNo
  )
  updateArea()
}

// 更新修改或者添加的区域
/**
 * 1. 查询当前资源之前有没有选中  有  没有
 * 2. 有选中 删除之前选中后  保存选中的
 * 3.  没有选中 保存选中
 */

function updateArea() {
  const params = Object.assign({}, regionalForm.value)
  params.regionalMsg = defaultArea.value
    .map((item) => item.foreignKeyRegionalName)
    .join('、')
  params.foreignKeyRegionalList = defaultArea.value.filter(
    (item: any) => !item.id
  ) // 新增区域
  // 筛选删除区域id
  const arr = defaultArea.value.map((item: any) => item.id)
  params.delForeignKeyIds = confirmArea.value
    .filter((item) => !arr.includes(item.id))
    .map((item) => item.id)
  // 剔除无用字段
  delete params.status
  delete params.createTime
  delete params.updateTime

  console.log('yayayayay')
  // updRegionalManagementForeignKeyList(params).finally(() => {
  //   getData();
  // });
}

function showBuildBtn() {
  showBuild.value = true
}

function handleUpdateBuild(
  item: Record<string, any>,
  isClearFloorId?: boolean
) {
  console.log(6778)
  if (isClearFloorId) selectFloorId.value = ''
  floorId.value = ''
  buildingId.value = item.buildingId
  areaName.value = item.buildingName
  showFloor.value = true
  showBuild.value = false
  handleLoad(item)
}

function buildChecked(val: boolean, item: any) {
  if (val) {
    defaultArea.value = [
      {
        adminCode: '',
        buildingId: item.buildingId,
        buildingName: item.buildingName,
        floorId: '',
        floorName: '',
        gridNo: '',
        foreignKeyId: item.buildingId,
        foreignKeyRegionalName: item.buildingName,
        foreignKeyRegionalType: 1
      }
    ]
  } else {
    defaultArea.value = []
  }

  // updateArea();
}

async function handleUpdateFloor(item: Record<string, any>) {
  console.log('item', item)
  floorId.value = item.floorId
  selectFloorId.value = item.floorId
  // 当有楼层被接管 不可点击地图选择房间
  const floorIds = defaultArea.value
    .filter((item) => !item.gridNo)
    .map((item) => item.floorId)
  // 当有楼栋被接管 不可点击地图选择房间
  const foreignKeyIds = defaultArea.value.map((item) => item.foreignKeyId)
  mapDisabled.value =
    foreignKeyIds.includes(buildingId.value) || item.isRegionalDivide === '1'

  if (item.isRegionalDivide === '1') {
    await sleep(300)
    planeRef.value?.floorLight(item.floorId)
  }
}

// 楼层全选or非全选事件
function floorChecked(val: boolean, item: any) {
  console.log('点击楼层', val)
  console.log('点击楼层数据', item)
  if (val) {
    defaultArea.value = [
      {
        adminCode: '',
        // buildId: item.buildingId,
        buildingId: item.buildingId,
        buildingName: item.buildingName,
        floorId: item.floorId,
        floorName: item.floorName,
        gridNo: '',
        foreignKeyId: item.floorId,
        foreignKeyRegionalName: `${item.buildingName}/${item.floorName}`,
        foreignKeyRegionalType: 2
      }
    ]
  } else {
    defaultArea.value = []
  }
  // updateArea();
}

function saveAreaFun() {
  console.log('确定选择区域了吗', defaultArea.value[0])
  console.log('确定删除saveConfirmArea了吗', saveConfirmArea.value)
  // return
  // 临时添加请求
  let params = {}
  if (
    defaultArea.value[0].buildingId &&
    defaultArea.value[0].floorId &&
    defaultArea.value[0].objectId
  ) {
    params = {
      buildingId: defaultArea.value[0].buildingId,
      buildingName: defaultArea.value[0].buildingName,
      floorId: defaultArea.value[0].floorId,
      floorName: defaultArea.value[0].floorName,
      locationGisId: defaultArea.value[0].objectId,
      regionalType: 0,
      roomName: defaultArea.value[0].foreignKeyRegionalName
    }
  } else if (defaultArea.value[0].buildingId && defaultArea.value[0].floorId) {
    params = {
      buildingId: defaultArea.value[0].buildingId,
      buildingName: defaultArea.value[0].buildingName,
      floorId: defaultArea.value[0].floorId,
      floorName: defaultArea.value[0].floorName || '',
      locationGisId: '',
      regionalType: 2,
      roomName: defaultArea.value[0].foreignKeyRegionalName
    }
  } else if (defaultArea.value[0].buildingId) {
    params = {
      buildingId: defaultArea.value[0].buildingId,
      buildingName: defaultArea.value[0].buildingName || '',
      floorId: '',
      floorName: '',
      locationGisId: '',
      regionalType: 1,
      roomName: defaultArea.value[0].foreignKeyRegionalName
    }
  } else {
    $toast.warning('请选择区域位置')
    return
  }
  // console.log(params, '接口请求的参数')
  // // saveFun(params)
  // console.log(defaultArea.value[0], '选择的数据++++++++++')
  // return
  // 如果之前保存有资源id说明是编辑不是第一次新增
  if (saveConfirmArea.value.resId && saveConfirmArea.value.regionalType == 0) {
    params.resId = saveConfirmArea.value.resId
    defaultArea.value[0].resId = saveConfirmArea.value.resId
  }

  // 如果有之前选中区域先删除之前选中区域在添加（必须保存的是房间)
  if (saveConfirmArea.value.areaId && saveConfirmArea.value.regionalType == 0) {
    console.log(saveConfirmArea.value.areaId, 'shanchuaay')
    planeRef.value?.delBeforeGis([
      {
        gridNoX: saveConfirmArea.value.areaId,
        buildingId: saveConfirmArea.value.buildingId,
        floorId: saveConfirmArea.value.floorId
      }
    ])
  }
  console.log(params, '接口请求的参数')
  // saveFun(params)
  console.log(defaultArea.value[0], '选择的数据++++++++++')
  console.log(defaultArea.value[0], '删除的数据++++++++++')
  // return
  saveResArea(params).then((res: any) => {
    console.log(res.data)
    if (res.code == 'success') {
      defaultArea.value[0].gridNo = res.data.id
      // 保存房间
      if (defaultArea.value[0].objectId) {
        planeRef.value?.addGis(defaultArea.value[0])
      } else {
        // 保存楼栋楼层
        let data = {
          ...defaultArea.value[0],
          regionalType: params.regionalType
        }
        saveFun(data)
      }
    } else {
      $toast.warning('服务器异常，选择区域失败')
      console.log('添加失败')
      // hideDialog()
    }
  })
}

// 选中保存完成
function saveFun(data: any) {
  console.log(data, '选择----------保存的,')
  data.formIndex = formIndex.value
  data.resourceIndex = resourceIndex.value
  emits('update', data)
  hideDialog()
}

function delAreaFun() {
  hideDialog()
  // let delData = [{ gridNo: '3b6a8d8f6b3c49ed89228a14b2683865' }];
  // planeRef.value?.delBeforeGis(delData);
}

// function floorChecked(val: boolean, item: any) {
//   console.log("点击楼层",val)
//   console.log("点击楼层数据",item)
//   if (val) {
//     defaultArea.value.push({
//       adminCode: '',
//       buildId: item.buildingId,
//       floorId: item.floorId,
//       gridNo: '',
//       foreignKeyId: item.floorId,
//       foreignKeyRegionalName: `${item.buildingName}/${item.floorName}`,
//       foreignKeyRegionalType: 2,
//     });
//   } else {
//     defaultArea.value = defaultArea.value.filter((sItem) => sItem.foreignKeyId !== item.floorId);
//   }
//   updateArea();
// }

function handleTip() {
  if (mapDisabled.value) $toast.warning('该楼层已被接管')
}

// 获取楼层列表
function handleLoad(option: any) {
  getFloorListByUnitId({
    buildingId: option.buildingId,
    unitId: unitId.value
  }).then((res) => {
    const data = res.data || []
    floorData.value = data.map((item) => {
      return {
        ...item,
        buildingId: option.buildingId,
        buildingName: option.buildingName,
        label: item.floorName,
        value: item.floorId,
        disabled: item.disable == 1,
        checked: item.disable == 1
        // disabled: item.isBelong === '0' && (item.isRegionalDivide === '1' || item.isRegionalDivide === '2'),
        // checked: item.isRegionalDivide === '1' || item.isRegionalDivide === '2',
      }
    })
    console.log('selectFloorId.value', selectFloorId.value)
    if (selectFloorId.value) {
      const part = floorData.value.find(
        (item) => item.floorId === selectFloorId.value
      )
      console.log('222', part)
      if (part) handleUpdateFloor(part)
    } else {
      const floorIds = defaultArea.value
        .map((item) => item.floorId)
        .filter((item) => item)
      const part = floorData.value.find((item) => item.floorId === floorIds[0])
      console.log('111', part)
      handleUpdateFloor(part ? part : floorData.value[0])
    }
  })
}

function getRegionalData(obj) {
  // 查询区域

  if (obj.buildingId) {
    buildingId.value = obj.buildingId
    const part = buildingData.value.find(
      (item) => item.buildingId === buildingId.value
    )
    areaName.value = part?.buildingName
  }
  if (obj.floorId) {
    floorId.value = obj.floorId
  }
  if (obj.areaId) {
    defaultArea.value = [{ ...obj }]
  }

  // queryForeignKeyRegionalList(regionalForm.value.id).then((res) => {
  //   defaultArea.value = res.data || [];
  //   confirmArea.value = res.data || [];
  //   planeRef.value?.updateArea();
  //
  //   if (buildingId.value) {
  //     const part = buildingData.value.find((item) => item.buildingId === buildingId.value);
  //     areaName.value = part?.buildingName;
  //     if (part) handleUpdateBuild(part);
  //   } else {
  //     const part = defaultArea.value.length
  //       ? buildingData.value.find((item) => item.buildingId === defaultArea.value[0].buildId)
  //       : buildingData.value[0];
  //     areaName.value = part?.buildingName;
  //     if (part) handleUpdateBuild(part);
  //   }
  // });
}

function getData() {
  console.log('???unitId???????', unitId.value)
  // return
  getBuildingListByUnitIdNew({ unitId: unitId.value })
    .then((res) => {
      const data = res.data || []
      buildingData.value = data.map((item) => {
        return {
          ...item,
          label: item.buildingName,
          value: item.buildingId,
          isLeaf: false,
          // disabled: item.isBelong === '0' && (item.isRegionalDivide === '1' || item.isRegionalDivide === '2'),
          // checked: item.isRegionalDivide === '1' || item.isRegionalDivide === '2',
          disabled: item.disable == 1,
          checked: item.disable == 1
        }
      })
      if (saveConfirmArea.value.buildingId) {
        buildingId.value = saveConfirmArea.value.buildingId
        const part = buildingData.value.find(
          (item) => item.buildingId === buildingId.value
        )
        areaName.value = part?.buildingName
      }
      if (saveConfirmArea.value.floorId && saveConfirmArea.value.buildingId) {
        handleLoad({
          buildingId: buildingId.value,
          buildingName: areaName.value
        })
      }
    })
  // .finally(() => getRegionalData(data))
}

function showDialog(obj: any) {
  console.log('obj----', obj)
  // 设置unitId
  if (obj.unitId) {
    unitId.value = obj.unitId
  }
  areaName.value = ''
  buildingData.value = []
  floorData.value = []
  showBuild.value = false
  defaultArea.value = []
  buildingId.value = ''
  floorId.value = ''
  formIndex.value = obj.formIndex
  resourceIndex.value = obj.resourceIndex
  saveConfirmArea.value = obj
  selectFloorId.value = obj.floorId
  if (obj.areaId) {
    defaultArea.value = [{ ...obj }]
  }
  getData()
  showMode.value = true
  nextTick(() => {
    // saveConfirmArea.value = obj
    // if (obj.buildingId) {
    //   buildingId.value = obj.buildingId
    // }
    // if (obj.floorId) {
    //   floorId.value = obj.floorId
    // }
    // if (obj.areaId) {
    //   defaultArea.value = [{ ...obj }]
    // }

    console.log('?hddddahah ', defaultArea.value)
    console.log('delahah ', saveConfirmArea.value)
  })
}

function hideDialog() {
  console.log(planeRef.value, 'indoor')
  showMode.value = false
}

// 第一次新增资源取消弹框删除之前临时选中的gis数据
function delGridNoListData(delGridNoData: any) {
  console.log('需要删除的++++++++++', delGridNoData)
  console.log('需要删除的+dom+++++++++', planeRef.value)
  if (delGridNoData.length > 0 && planeRef.value) {
    delGridNoData.forEach((item: any) => {
      delGis2(item.gridNoX, item.item.floorId, item.item.buildingId)
    })
  }
}
const gridAreaLayer = IndoorMap.GridAreaType.GridAreaLayerCZF

// 调用gis 方法删除
function delGis(gridNoX: string) {
  console.log('调用了删除接口呀gis', gridNoX)
  IndoorMap.IndoorDo_DeleteGridAreaByGridNoSX(
    door.value,
    gridNoX,
    gridAreaLayer,
    function () {
      console.log('删除完成了')
    }
  )
}

// 调用gis 方法删除需要知道楼栋楼层
function delGis2(gridNoX: string, floorIdX: string, buildIdX: string) {
  console.log('调用了2除接口呀gis', gridNoX, floorIdX, buildIdX)
  IndoorMap.IndoorDo_DeleteGridAreaByGridNoSX2(
    door.value,
    gridNoX,
    gridAreaLayer,
    floorIdX,
    buildIdX,
    function () {
      console.log('删除22完成了')
    }
  )
}
// onMounted(() => {
//   getData();
// });
defineExpose({ showDialog, hideDialog, delGridNoListData })
defineOptions({ name: 'ZoningComp' })
</script>

<style scoped lang="scss">
.zoning-card {
  background: url('../assets/pop.png') no-repeat !important;
  background-size: 100% 100% !important;

  .build-box {
    @apply absolute top-0 left-0 z-[999] w-[360px] p-[20px] flex items-center;

    .n-input {
      @apply w-0 flex-1;
    }

    .build-select {
      @apply absolute top-[60px] right-[20px] w-[260px] h-[400px] bg-[#fff] rounded-[4px];

      .item {
        @apply h-[35px] flex items-center pl-[10px] text-[#000] cursor-pointer;

        .text {
          @apply w-0 flex-1 pl-[10px];
        }

        &:hover {
          @apply rounded-[4px];
          background-color: rgb(250, 250, 252);
        }

        &.active {
          @apply text-[#fff] rounded-[4px] outline-0;
          background: linear-gradient(169deg, #16436f 21%, #1d9df2 86%);
        }
      }
    }
  }

  .floor-box {
    @apply absolute top-[100px] right-[6px] z-[999] h-[360px] bg-[#fff] rounded-[4px] text-[#000];

    .item {
      @apply h-[42px] px-[6px] flex items-center cursor-pointer;

      .text {
        @apply pl-[6px];
      }

      &.active {
        @apply text-[#fff] outline-0 rounded-[4px];
        background: linear-gradient(169deg, #16436f 21%, #1d9df2 86%);
      }
    }
  }

  .select-area {
    @apply absolute bottom-0 left-[6px] z-[999] w-[calc(100%-12px)] h-[42px] px-[10px] flex items-center text-[16px] text-[#fff] truncate;
    background-color: rgb(0, 181, 120);
    .select-area-btn {
      @apply w-[120px] h-[32px] cursor-pointer;
      background-color: rgb(250, 250, 252);
      color: black;
      text-align: center;
      line-height: 32px;
    }
  }
}
</style>
