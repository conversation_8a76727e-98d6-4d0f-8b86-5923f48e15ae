<template>
  <div class="w-full h-full" id="planeGis"></div>
</template>

<script setup lang="ts">
import { nextTick, onMounted, ref, watch } from 'vue'
import { ThreeService } from '@/views/gis/ThreeService.ts'
// import { useToastCtx } from '@/common/shareContext';
import useToastCtx from '@/common/shareContext/useToastCtx.ts'

interface IItem {
  buildingId: string | null
  floorId: string | null
  defaultArea: Record<string, any>[]
}
const props = withDefaults(defineProps<IItem>(), {
  buildingId: '',
  floorId: '',
  defaultArea: () => [] // 判断删除区域数据
})
const toast = useToastCtx()
const emits = defineEmits(['load', 'finish', 'delete', 'add', 'remove', 'save'])
const deleteGridNo = ref<string[]>([]) // 删除区域gridNo数据 暂存
// 责任管控区域(区域管理)
const gridAreaLayer = IndoorMap.GridAreaType.GridAreaLayerCZF
// const gridAreaLayer = IndoorMap.GridAreaType.GridAreaLayerCZF;
let indoor: any = null
const areaArr = ref<Record<string, any>[]>([])

const selectArea = ref<Record<string, any>[]>([]) // 选中数据

// 初始化地图
function initThreeMap() {
  IndoorMap.init()
  ThreeService.initThreeMap()
  indoor = new IndoorMap(
    IndoorMap.Merge(
      [window.CONST_GSCache, window.CONST_GSOptions, window.CONST_GSParams],
      {
        target: 'planeGis',
        tile: false, //底图是否可见
        sky: true, //开启天空
        // isVector: false,
        gridLoadViewTypes: IndoorMap.ViewType.toViewTypeArray(), // 打开网格
        // gridTypeIds: [IndoorMap.GridAreaType.GridAreaLayerZRGK, IndoorMap.GridAreaType.GridAreaLayerGGQY],
        gridTypeIds: gridAreaLayer,
        grid: true,
        gridLoad: true,
        gridFilter: function (ele: any) {
          // 编辑时 删除已选区域
          // if (ele.typeId !== IndoorMap.GridAreaType.GridAreaLayerCZF) {
          //   ele.typeColorCode = 'GAL_GGQY_def';
          //   // ele.typeColorCode = 'GAL_CZF_def';
          //   return;
          // }
          ele.text = '已选'
        }
      }
    )
  )

  // 已选中区域点击
  indoor.onGridSelected = function (data: any) {
    console.log('点击', data)
    if (data.typeId !== IndoorMap.GridAreaType.GridAreaLayerCZF) {
      addArea(data, true)
    } else {
      console.log('选择已选中区域点击', props.defaultArea)
      const gridNos = props.defaultArea.map((item) => item.gridNo)
      if (!gridNos.includes(data.gridNo)) return toast.warning('该区域已被管控')
      // 编辑时 删除已选区域
      // deleteGridNo.value.push(data.gridNo);
      // emits('delete', data.gridNo);
    }
  }

  // 未选中区域点击
  indoor.onAreaSelected = function (data: any) {
    console.log('data==> 未选中区域点击', data)
    // data.buildId = indoor.getIndoorDataState().buildId;
    data.buildingId = indoor.getIndoorDataState().buildId
    // data.typeColorCode = 'GAL_ZRGK_def';
    data.typeColorCode = 'GAL_CZF_def'
    const arr = indoor.getGeoGridAreasByGeoFieldNV(
      data.shape,
      window.GISShare.SMap.Data.SpatialRelationshipType.eIntersects,
      'typeId',
      IndoorMap.GridAreaType.GridAreaLayerCZF
    ) //网格相交（仅 IndoorMap 有效）
    areaArr.value = []
    console.log('arr.length', arr)
    if (arr.length) {
      arr.forEach((item: any) => {
        areaArr.value.push(item.gsData)
      })
      areaArr.value.push(data)
      console.log('areaArr.value', areaArr.value)
      const isSelected =
        areaArr.value.filter(
          (item) => item.text && item.text.includes('已选') && item.gridNo
        ).length > 0
      if (isSelected) toast.warning('该区域和其他已选区域重合')
    } else {
      console.log('点击为选中的', data)
      addArea(data)
    }
  }

  // indoor.onGridAreaLoad = function (data: any, sender: any) {
  //   const floorId = sender.getIndoorDataState().floorId;
  //   const _item = props.defaultArea.find((item) => item.foreignKeyId === floorId);
  //   if (_item) floorLight(floorId);
  // };

  indoor.onGridAreaLoad = function () {
    // const floorId = sender.getIndoorDataState().floorId;
    // const _item = props.defaultArea.find((item) => item.foreignKeyId === floorId);
    // if (_item) floorLight(floorId);
    console.log(selectArea.value, '???????????')
    const arr = selectArea.value.filter(
      (item) => item.floorId === props.floorId
    )
    console.log(arr, 'hah刷新的吗hahah')
    indoor.addGridDataVector(arr, true)
  }
}

function updateArea() {
  console.log('楼层楼栋变化')
  indoor.showGridDataVector(undefined, undefined, true)
}

function addArea(item: any, isCustom?: boolean) {
  let params: Record<string, any> = {}
  if (isCustom) {
    params = {
      ...item,
      typeColorCode: 'GAL_CZF_def',
      buildingId: indoor.getIndoorDataState().buildId
    }
  } else {
    params = {
      ...item,
      // gridNo: true,
      typeColorCode: 'GAL_CZF_def'
    }
  }

  console.log('addAREA参数++', params)

  selectArea.value = [params]
  indoor.showGridDataVector(undefined, undefined, true)
  emits('add', selectArea.value)
}

function addGis(data: any) {
  let params = {
    text: data.text,
    gridNo: data.gridNo,
    typeColorCode: 'GAL_CZF_def',
    buildingId: data.buildId,
    floorId: data.floorId,
    objectId: data.objectId
  }
  console.log('addgis=====', data)
  console.log('addgis=========', params)
  // emits('save', data);
  IndoorMap.IndoorDo_InsertGridAreaSX(
    indoor,
    data.shape,
    undefined,
    gridAreaLayer,
    params,
    function () {
      const gisData = {
        adminCode: indoor.getIndoorDataState().adminCode,
        buildingId: indoor.getIndoorDataState().buildId,
        floorId: data.floorId,
        gridNo: params.gridNo,
        foreignKeyId: params.gridNo,
        buildingName: data.buildingName || '',
        floorName: data.floorName || '',
        foreignKeyRegionalName: data.foreignKeyRegionalName || '',
        foreignKeyRegionalType: 0,
        regionalType: 0,
        objectId: params.objectId
      }
      emits('save', gisData)
      console.log('添加完成了')
    }
  )
}

// 删除之前临时保存gis保存数据
function delBeforeGis(data: any) {
  console.log('临时2删除gis', data)
  data.forEach((item: any) => {
    // delGis(item)
    delGis2(item.gridNoX, item.floorIdX, item.buildIdX)
  })
}

// 调用gis 方法删除
function delGis(gridNoX: string) {
  console.log('调用了删除接口呀gis')
  IndoorMap.IndoorDo_DeleteGridAreaByGridNoSX(
    indoor,
    gridNoX,
    gridAreaLayer,
    function () {
      console.log('删除完成了')
    }
  )
}

// IndoorMap.IndoorDo_DeleteGridAreaByGridNoSX2(indoor, gridNoX, typeIdX, floorIdX, buildIdX, callback)
function delGis2(gridNoX: string, floorIdX: string, buildIdX: string) {
  console.log('调用了2除接口呀gis', gridNoX, floorIdX, buildIdX)
  IndoorMap.IndoorDo_DeleteGridAreaByGridNoSX2(
    indoor,
    gridNoX,
    gridAreaLayer,
    floorIdX,
    buildIdX,
    function () {
      console.log('删除22完成了')
    }
  )
}

// 楼层高亮
function floorLight(floorId: string) {
  indoor.getBaseFloorInfo(floorId, function (result: any[]) {
    result.forEach((item) => {
      item.height = 0
      // item.typeColorCode = 'GAL_ZRGK_def';
      item.typeColorCode = 'GAL_CZF_def'
      item.text = '已选'
    })
    console.log('result', result)
    indoor.addGridDataVector(result)
  })
}

watch([() => props.buildingId, () => props.floorId], ([v1, v2]) => {
  indoor.clearAll()
  console.log('ss+++++++++++++++++++', v1, v2)
  if (!v1 || !v2) return
  emits('load')
  indoor.showFloorData(
    IndoorMap.ViewType.IndoorAreaVector,
    '', //单位id
    v1, //楼栋id
    v2, //楼层id
    // undefined, //图纸地址 （用于查询不到室内GIS数据时自动跳转，可缺省）
    '',
    function () {
      updateArea()
      emits('finish', { indoor: indoor })
    }
  )
})

onMounted(() => {
  nextTick(() => {
    initThreeMap()
  })
})

defineExpose({ updateArea, floorLight, addGis, delBeforeGis })
defineOptions({ name: 'PlaneComp' })
</script>

<style scoped lang="scss"></style>
