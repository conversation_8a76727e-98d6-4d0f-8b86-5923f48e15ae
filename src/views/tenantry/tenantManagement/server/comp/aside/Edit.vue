<template>
  <ComDrawerA
    :autoFocus="false"
    :footerPaddingBottom="25"
    :maskClosable="false"
    :show-action="true"
    @handle-negative="handleClose"
    @handle-positive="handleSubmit"
    class="!w-[500px]"
    v-model:show="isShowEdit"
  >
    <div class="w_dialog_box">
      <div>
        <n-form
          ref="formRef"
          :model="sendFrom"
          :rules="rules"
          label-placement="left"
          label-width="auto"
          require-mark-placement="right-hanging"
        >
          <div>
            <div class="w_a_title">客户基本信息：</div>
            <n-form-item label="客户名称" path="tenantryName">
              <n-input
                v-model:value="sendFrom.tenantryName"
                placeholder="请输入客户名称"
                maxlength="20"
                show-count
                clearable
              />
            </n-form-item>
            <n-form-item label="统一社会信用代码：" path="socialCode">
              <n-input
                v-model:value="sendFrom.socialCode"
                placeholder="请输入统一社会信用代码"
                maxlength="50"
                show-count
                disabled
              />
            </n-form-item>
            <n-form-item label="客户负责人身份证号码：" r>
              <n-input
                v-model:value="sendFrom.tenantryManagerIdNo"
                placeholder="请输入客户负责人身份证号码"
                maxlength="20"
                show-count
                clearable
                @blur="getIdNameFun"
              />
            </n-form-item>
            <n-form-item label="客户负责人手机号：" path="tenantryManagerPhone">
              <n-input
                v-model:value="sendFrom.tenantryManagerPhone"
                placeholder="请输入客户负责人手机号"
              />
            </n-form-item>
            <n-form-item label="客户负责人：" path="tenantryManager">
              <n-input
                v-model:value="sendFrom.tenantryManager"
                placeholder="请输入客户负责人"
                maxlength="20"
                show-count
                clearable
              />
            </n-form-item>
            <n-form-item label="客户性质：">
              <n-select
                v-model:value="sendFrom.tenantryNatureCode"
                placeholder="请选择客户性质"
                :options="tenantryNatureOptions"
                label-field="paramValue"
                value-field="paramCode"
                clearable
              />
            </n-form-item>
            <n-form-item label="客户来源：">
              <n-select
                v-model:value="sendFrom.tenantrySourceCode"
                placeholder="请选择客户来源"
                :options="tenantrySourceOptions"
                label-field="paramValue"
                value-field="paramCode"
                clearable
              />
            </n-form-item>
            <n-form-item label="项目经营单位：" path="projectManagementUnitId">
              <n-select
                v-model:value="sendFrom.projectManagementUnitId"
                placeholder="请选择项目经营单位"
                :options="projectManagementOptions"
                label-field="orgName"
                value-field="orgCode"
                clearable
                disabled
              />
            </n-form-item>
          </div>
        </n-form>
      </div>
    </div>
  </ComDrawerA>
</template>

<script lang="ts" setup>
import { $toast } from '@/common/shareContext/useToastCtx.ts'
import ComDrawerA from '@/components/drawer/ComDrawerA.vue'
import { useAuthStore } from '@/store/modules'
import { isMobilePhone, stringCheck } from '@/utils/inputValidate.ts'
import type { FormInst, FormRules } from 'naive-ui'
import { onBeforeUnmount, onMounted, ref, watch } from 'vue'
import { ACTION } from '../../constant'
import {
  getDetailByIdCard,
  getUnitList,
  getUnitTenantDetail,
  queryDictList,
  saveTenant
} from '../../fetchData.ts'

const showBuild = ref(false)
const areaInputRef = ref<any>()
const mapDisabled = ref(false)
const store = useAuthStore()
const isShowEdit = ref(false)

const emits = defineEmits(['action'])
const formRef = ref<FormInst | null>(null)
const fDate = ref<any>(null) // 回显日期
const planeRef = ref<any>()
const buildData = ref<Record<string, any>[]>([])
const floorData = ref<Record<string, any>[]>([])
const selectArea = ref<Record<string, any>[]>([])
const finishSelectAreaInfo = ref<any[]>([])

const fileUpList = ref<any>([])
const isNotLocalBool = ref(false)
const sendFrom = ref<any>(init())

const resourceTypeOptions = ref([])
const tenantryNatureOptions = ref([])

const tenantrySourceOptions = ref([])

const projectManagementOptions = ref([])

// 计租模式
const rentCalculationModeOptions = ref([])

const erecordUnitId = ref('')

const rules: FormRules = {
  tenantryName: [
    {
      required: true,
      message: '请输入客户名称',
      trigger: ['input', 'blur']
    },
    {
      message: '请输入不包含非法字符',
      trigger: ['input', 'blur'],
      validator(rule: any, val: any) {
        return stringCheck(val)
      }
    }
  ],
  socialCode: [
    {
      required: true,
      message: '请输入统一社会信用代码',
      trigger: ['input', 'blur']
    },
    {
      message: '请输入不包含非法字符',
      trigger: ['input', 'blur'],
      validator(rule: any, val: any) {
        return stringCheck(val)
      }
    }
  ],
  tenantryManagerIdNo: {
    required: true,
    message: '请输入身份证号码',
    trigger: ['input', 'blur']
  },
  tenantryManagerPhone: [
    {
      required: true,
      message: '请输入客户负责人手机号',
      trigger: ['input', 'blur']
    },
    {
      message: '请输入正确手机号',
      trigger: ['input', 'blur'],
      validator(rule: any, val: any) {
        return isMobilePhone(val)
      }
    }
  ],
  tenantryManager: [
    {
      required: true,
      message: '请输入客户负责人',
      trigger: ['input', 'blur']
    },
    {
      message: '请输入不包含非法字符',
      trigger: ['input', 'blur'],
      validator(rule: any, val: any) {
        return stringCheck(val)
      }
    }
  ],
  projectManagementUnitId: {
    required: true,
    message: '请选择项目经营单位',
    trigger: ['change', 'blur']
  }
}

function init() {
  return {
    tenantryId: '',
    tenantryName: '', //客户名称 承租方名称
    socialCode: '', //统一社会信用代码
    tenantryManagerIdNo: '', //客户负责人身份证号码
    tenantryManagerPhone: '', //客户负责人手机号
    tenantryManager: '', //客户负责人
    tenantryNatureCode: '', //客户性质
    tenantrySourceCode: null, // 客户来源
    projectManagementUnitId: '' // 项目经营单位id
  }
}

function handleSubmit() {
  return new Promise((resolve, reject) => {
    formRef.value?.validate(async (errors: any) => {
      if (!errors) {
        let params = Object.assign({}, sendFrom.value)
        // 剔除无用字段
        delete params.status
        delete params.createTime
        delete params.updateTime
        saveTenant(params)
          .then((res: any) => {
            console.log(res, 'add--')
            handleClose()
            emits('action', { action: ACTION.BLACK })
            resolve('submitted')
            reset()
          })
          .catch(reject)
      } else {
        if (errors.length) {
          try {
            const first = errors[0][0]
            if (first.message) {
              $toast.error(first.message)
            }
          } catch (e) {}
        }
        reject(errors)
      }
    })
  })
}

async function getDetail(id: string) {
  getUnitTenantDetail({ tenantryId: id }).then((res: any) => {
    if (res.code != 'success') return
    sendFrom.value.tenantryId = id
    Object.keys(sendFrom.value).forEach((key) => {
      if (res.data[key] as string) {
        sendFrom.value[key] = res.data[key]
      }
    })
  })
}

// 客户性质
async function getTenantryNatureOpt() {
  queryDictList({ dictType: 'khxz' }).then((res: any) => {
    if (res.code != 'success') return
    if (res.data.length > 0) {
      tenantryNatureOptions.value = res.data
      tenantryNatureOptions.value.forEach((item: any) => {
        item.paramCode = item.paramCode + ''
      })
    }
  })
}

// 客户来源
async function getTenantrySourceOpt() {
  queryDictList({ dictType: 'czfly' }).then((res: any) => {
    if (res.code != 'success') return
    if (res.data.length > 0) {
      tenantrySourceOptions.value = res.data
      tenantrySourceOptions.value.forEach((item: any) => {
        item.paramCode = item.paramCode + ''
      })
    }
  })
}

// 项目经营单位
async function getProjectManagementOpt() {
  getUnitList({ unitId: store.userInfo.unitId }).then((res: any) => {
    if (res.code != 'success') return
    if (res.data.length > 0) {
      projectManagementOptions.value = res.data
    }
  })
}

function handleClose() {
  isShowEdit.value = false
}

function reset() {
  sendFrom.value = init()
}

async function showDialog(id: string) {
  isNotLocalBool.value = false
  selectArea.value = []
  fileUpList.value = []
  floorData.value = []
  buildData.value = []
  finishSelectAreaInfo.value = []
  sendFrom.value = init()

  await getTenantryNatureOpt() // 客户性质
  await getTenantrySourceOpt() // 客户来源
  await getProjectManagementOpt() // 项目经营单位
  await getDetail(id)
  isShowEdit.value = true
}

// 点击其他地方 关闭下拉选择框
function onClick(e: any) {
  if (areaInputRef.value?.contains(e.target)) return
  showBuild.value = false
}

onBeforeUnmount(() => {
  document.removeEventListener('click', onClick)
})

onMounted(() => {
  document.addEventListener('click', onClick)
})

function getIdNameFun() {
  // console.log(sendFrom.value.tenantryManagerIdNo,'发送请求')
  getDetailByIdCard({ idCard: sendFrom.value.tenantryManagerIdNo }).then(
    (res: any) => {
      if (res.code != 'success') return
      sendFrom.value.tenantryManagerPhone = res.data.tenantryManagerPhone || ''
      sendFrom.value.tenantryManager = res.data.tenantryManager || ''
    }
  )
}

watch(
  () => finishSelectAreaInfo,
  () => {
    if (finishSelectAreaInfo.value && finishSelectAreaInfo.value.length > 0) {
      isNotLocalBool.value = false
    } else {
      console.log('出发了')
      isNotLocalBool.value = true
    }
  },
  {
    deep: true, // 监听深层属性的变化
    immediate: false
  }
)

defineExpose({
  showDialog
})

// getFloorUnitId();
defineOptions({ name: 'serverEditDialogIndex' })
</script>

<style scoped lang="scss">
.w_dialog_box {
  height: calc(100% - 200px) !important;
  //background-color: hotpink;
  width: 100%;
  padding: 20px;

  .w_a_title {
    height: 32px;
    line-height: 32px;
    color: #527cff;
  }
}

.select-box {
  display: flex;
  align-items: center;
  margin-top: 10px;

  .select-item {
    height: 36px;
    line-height: 36px;
    margin: 4px 4px 0 0;
    padding: 0 8px;
    background-color: #f1f3f9;
    border-radius: 4px;
  }
}

.add-btn {
  display: flex;
  justify-content: end;
  align-items: center;
  margin: 10px 0 20px;

  .add-icon {
    margin-right: 6px;
    width: 24px;
    height: 24px;
    background: url('@/assets/add.png') no-repeat center;
    cursor: pointer;
  }
}

.area-item {
  margin-bottom: 10px;
  padding: 10px 14px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #333;
  background: #f5f5f5;
  border-radius: 3px;

  div {
    width: calc(100% - 30px);
  }

  .delete-btn {
    display: none;
    cursor: pointer;
    width: 14px;
    height: 14px;
    background: url('@/assets/delete.png') no-repeat center;
  }

  &:hover {
    color: #527cff;
    background: #f1f3f9;

    .delete-btn {
      display: block;
    }
  }
}

.area-name {
  width: 180px;
  height: 34px;
  line-height: 34px;
  padding: 0 10px;
  background-color: #fff;
  border: 1px solid rgb(224, 224, 230);
  border-radius: 3px;
}

.build-ul {
  position: absolute;
  top: 36px;
  left: 0;
  width: 360px;
  height: 200px;
  padding: 6px 0;
  line-height: 34px;
  //background-color: #fff;
  border-radius: 4px;
  z-index: 9;

  .build-box {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 180px;
    background-color: #fff;
  }

  .floor-box {
    position: absolute;
    top: 0;
    left: 180px;
    height: 100%;
    width: 180px;
    background-color: #fff;
  }

  .item {
    display: flex;
    align-items: center;
    padding-left: 10px;
    cursor: pointer;

    &:hover {
      background-color: #f5f4f4;
    }

    .text {
      position: relative;
      flex: 1;
      width: 0;
      margin-left: 6px;

      &:after {
        content: '';
        position: absolute;
        top: 50%;
        right: 6px;
        transform: translateY(-50%);
        width: 12px;
        height: 12px;
        background: url('../assets/expand.png');
        background-size: cover;
      }
    }
  }
}
</style>
