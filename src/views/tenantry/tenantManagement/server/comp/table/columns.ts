import { DataTableColumn, NButton } from 'naive-ui';
import { h, toRaw } from 'vue';
import { ACTION } from '@/views/tenantry/tenantManagement/server/constant.ts';

export const cols: DataTableColumn[] = [
  {
    title: '序号',
    key: 'index',
    width: 65,
    align: 'center',
    render: (_: any, index: number) => {
      return index + 1;
    },
  },
  {
    title: '客户名称',
    key: 'tenantryName',
    align: 'center',
    width: 170,
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '管理员账号',
    key: 'tenantryAdminLogin',
    align: 'center',
    width: 170,
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '客户性质',
    key: 'tenantryNatureCodeName',
    width: 150,
    align: 'center',
  },
  {
    title: '客户负责人',
    key: 'tenantryManager',
    align: 'center',
    width: 150,
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '客户负责人手机号',
    key: 'tenantryManagerPhone',
    align: 'center',
    width: 150,
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '项目经营单位',
    key: 'projectManagementUnitName',
    align: 'center',
    width: 150,
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '创建人',
    key: 'createUserName',
    align: 'center',
    width: 150,
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '创建时间',
    key: 'createTime',
    align: 'center',
    width: 150,
    ellipsis: {
      tooltip: true,
    },
  },
];
