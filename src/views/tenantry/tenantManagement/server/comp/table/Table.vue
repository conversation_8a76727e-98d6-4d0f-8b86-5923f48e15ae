<template>
  <div class="h-full w-full">
    <n-data-table class="h-full" remote striped :columns="columns" :data="tableData" :bordered="false"
      :flex-height="true" :loading="loading" :pagination="pagination" :render-cell="useEmptyCell" :scroll-x="2450"
      virtual-scroll />
    <dialogCom v-model:show="isSHowDialog" title="加入黑名单" width="500px">
      <n-form ref="formRef" :model="formValue" :rules="rules" label-placement="left">
        <n-form-item label="加入黑名单的原因" path="backlistLastReason">
          <n-input v-model:value="formValue.backlistLastReason" placeholder="请输入加入黑名单的原因" :autosize="{
            minRows: 3,
            maxRows: 5
          }" type="textarea" maxlength="100" show-count clearable />
        </n-form-item>
      </n-form>
      <div class="flex justify-center mt-[16px]">
        <n-button class="!mr-[16px]" @click="handleClose">取消</n-button>
        <n-button type="primary" @click="handleSubmit">保存</n-button>
      </div>
    </dialogCom>
  </div>
</template>

<script setup lang="ts">
import { h, ref, VNode, toRaw, watch } from 'vue'
import { DataTableColumns, NButton, NDropdown } from 'naive-ui'
import { cols } from './columns'
import { ACTION } from '../../constant'
import { useRouter } from 'vue-router'
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts'
import { useNaivePagination } from '@/common/hooks/useNaivePagination.ts'
import { useActionDivider } from '@/common/hooks/useActionDivider.ts'
import { IObj } from '@/types'
import { useEmptyCell } from '@/common/hooks/useEmptyCell.ts'
import type { FormInst } from 'naive-ui'
import dialogCom from '@/components/dialog/ComDialog.vue'
import {
  getTenantList,
  queryBlackTenant,
  saveTenant
} from '@/views/tenantry/tenantManagement/server/fetchData.ts'

const props = defineProps({
  id: {
    type: String,
    default: ''
  }
})
const router = useRouter()
const emits = defineEmits(['action'])
const [loading, search] = useAutoLoading(false)
const { pagination, updateTotal } = useNaivePagination(getTableData)
const columns = ref<DataTableColumns>([])
const tableData = ref<any[]>([])

const formRef = ref<FormInst | null>(null)

const isBalck = ref(false)
const isSHowDialog = ref(false)

const formValue = ref({
  backlistLastReason: '',
  tenantryId: '',
  type: 1
})

const rules = {
  backlistLastReason: {
    required: true,
    message: '请输入加入黑名单原因',
    trigger: ['input']
  }
}

function setColumns() {
  columns.value.push(...cols)
  // 添加操作栏 action
  columns.value.push({
    title: '操作',
    key: 'actions',
    align: 'center',
    fixed: 'right',
    width: 250,
    render(row) {
      return getActionBtn(row)
    }
  })
}
setColumns()

function showDialog() {
  console.log('xianshi++++')
  formValue.value.backlistLastReason = ''
  isSHowDialog.value = true
}

function handleClose() {
  formValue.value.backlistLastReason = ''
  isSHowDialog.value = false
}

function getActionBtn(row: any) {
  const acList: [VNode, boolean?][] = [
    [
      h(
        NButton,
        {
          size: 'small',
          type: 'primary',
          ghost: true,
          class: 'com-action-button',
          onClick: () => router.push({ name: 'lesseeManage', params: { tenantryId: row.tenantryId } }),
          // onClick: () => router.push({ name: 'lesseeManage', params: { id: row.tenantryId } }),
        },
        { default: () => '项目详情' }
      )
    ],
    [
      h(
        NDropdown,
        {
          trigger: 'hover',
          options: [
            {
              label: '单位信息',
              key: 'EDIT',
              onClick: () => {
                emits('action', {
                  action: ACTION.EDIT,
                  data: { ...toRaw(row), type: 'unit' }
                })
              }
            },
            {
              label: '承租信息',
              key: 'LESSON_EDIT',
              onClick: () => {
                emits('action', {
                  action: ACTION.LESSON_EDIT,
                  data: { ...toRaw(row), type: 'tenant' }
                })
              }
            }
          ],
          onSelect: (key: string) => {
            emits('action', {
              action: key as keyof typeof ACTION,
              data: { ...toRaw(row), type: key }
            })
          }
        },
        {
          default: () =>
            h(
              NButton,
              {
                size: 'small',
                type: 'primary',
                ghost: true,
                class: 'com-action-button'
              },
              { default: () => '编辑' }
            )
        }
      )
    ],
    isBalck.value
      ? [
        h(
          NButton,
          {
            size: 'small',
            type: 'primary',
            ghost: true,
            class: 'com-action-button'
          },
          { default: () => '已加入黑名单' }
        )
      ]
      : [
        h(
          NButton,
          {
            size: 'small',
            type: 'primary',
            ghost: true,
            class: 'com-action-button',
            onClick: () => {
              console.log('kaishi点击', row.tenantryId)
              formValue.value.tenantryId = row.tenantryId
              showDialog()
            }
          },
          { default: () => '加入黑名单' }
        )
      ],
    [
      h(
        NButton,
        {
          size: 'small',
          type: 'primary',
          ghost: true,
          class: 'com-action-button',
          onClick: () =>
            emits('action', { action: ACTION.DELETE, data: toRaw(row) })
        },
        { default: () => '删除' }
      )
    ]
  ]
  return useActionDivider(acList)
}

let filterData: IObj<any> = {} // 搜索条件
function getTableData() {
  const params = {
    pageNo: pagination.page,
    pageSize: pagination.pageSize,
    ...filterData,
    projectManagementUnitId: props.id
  }

  search(getTenantList(params)).then((res) => {
    tableData.value = res.data.rows || []
    updateTotal(res.data.total || 0)
  })
}

// 加入黑名单
function handleSubmit() {
  // editRef.value?.handleSubmit();
  formRef.value?.validate((errors) => {
    if (!errors) {
      queryBlackTenant(formValue.value).then((res: any) => {
        console.log(res, 'add--')
        isSHowDialog.value = false
        emits('action', { action: ACTION.BLACK })
      })
    }
  })
}

function getTableDataWrap(data: IObj<any>) {
  filterData = Object.assign({}, data) || {}
  pagination.page = 1
  getTableData()
}

watch(
  () => props.id,
  (newVal: string) => {
    if (newVal) {
      getTableData()
    }
  },
  { immediate: true }
)
defineOptions({ name: 'checkServerTableComp' })
defineExpose({ getTableDataWrap, getTableData })
</script>
<style scoped lang="scss">
.w_text_r {
  color: red;
}
</style>
