<template>
  <n-form :show-feedback="false" label-placement="left">
    <div class="flex justify-between">
      <div class="flex">
        <n-form-item label="资源类型:">
          <n-select
            class="!w-[220px] mr-5"
            v-model:value="filterForm.resourceTypeCode"
            :options="resourceOpt"
            placeholder="请选择"
            label-field="paramValue"
            value-field="paramCode"
            clearable
          />
        </n-form-item>
        <n-form-item label="搜索查询:">
          <n-input placeholder="请输入搜索查询关键词" v-model:value="filterForm.searchQuery" clearable />
        </n-form-item>
      </div>

      <div class="w-[20%] flex justify-end">
        <n-button type="primary" @click="doHandle(ACTION.ADD)">
          <!--          <IconAdd />-->
          {{ ACTION_LABEL.ADD }}
        </n-button>
        <n-button type="primary" @click="doImport(ACTION.IMPORT)" class="!ml-[20px]">
          <!--          {{ ACTION_LABEL.IMPORT }}-->
          导入
        </n-button>
      </div>
    </div>
  </n-form>
</template>

<script setup lang="ts">
import { onMounted, ref, watch } from 'vue';
import { FlFilledAdd as IconAdd } from '@kalimahapps/vue-icons';
import { ACTION, ACTION_LABEL } from '@/views/tenantry/tenantManagement/server/constant.ts';
import { trimObjNull } from '@/utils/obj.ts';
import { getUnitList, queryDictList } from '@/views/tenantry/tenantManagement/server/fetchData.ts';
import { useAuthStore } from '@/store/modules';
const emits = defineEmits(['action']);
const store = useAuthStore();
const deTeOptions = ref([]);

const resourceOpt = ref([]);

const filterForm = ref(initForm());

function initForm() {
  return { resourceTypeCode: null, projectManagementUnitId: '', searchQuery: '', blacklistCode: 0 };
}

function getResourceOpt() {
  queryDictList({ dictType: 'zylx' }).then((res: any) => {
    if (res.code != 'success') return;
    if (res.data && res.data.length > 0) {
      resourceOpt.value = res.data;
    }
  });
}

function getProjectManagementOpt() {
  getUnitList({ unitId: store.userInfo.unitId }).then((res: any) => {
    if (res.code != 'success') return;
    if (res.data.length > 0) {
      deTeOptions.value = res.data;
    }
  });
}

function doHandle(action: ACTION) {
  emits('action', {
    action: action,
    data: trimObjNull(filterForm.value),
  });
}

function doImport(action: ACTION) {
  emits('action', {
    action: action,
  });
}

watch(filterForm.value, () => {
  doHandle(ACTION.SEARCH);
});

onMounted(() => {
  getResourceOpt();
  getProjectManagementOpt();
  doHandle(ACTION.SEARCH);
});

defineOptions({ name: 'checkServerTempFilterComp' });
</script>
<style module lang="scss"></style>
