<template>
  <div class="w_table_box">
    <Filter class="com-table-filter" @action="actionFn" />
    <TableList ref="tableCompRef" @action="actionFn" :id="props.id" />
  </div>
  <AsideComp ref="addRef" title="新增" @action="actionFn" :id="props.id" />
  <!-- 承租信息编辑 -->
  <LessonEdit
    ref="lessonEditRef"
    title="编辑承租信息"
    @action="actionFn"
    :id="currentAction.data?.tenantryId"
  />
  <ImportComp title="导入" @action="actionFn" ref="importRef" />
  <EditComp ref="editRef" title="编辑单位信息" @action="actionFn" />
</template>

<script setup lang="ts">
import Filter from './comp/Filter.vue'
import TableList from './comp/table/Table.vue'
import { $dialog } from '@/common/shareContext/useDialogCtx.ts'
import { ACTION } from './constant'
import { deleteTenant } from './fetchData'
import { IActionData, ICheckTempRow } from './type'
import { IObj } from '@/types'
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import AsideComp from './comp/aside/index.vue'
import ImportComp from './comp/aside/import.vue'
import EditComp from './comp/aside/Edit.vue'
import LessonEdit from './comp/aside/LessonEdit.vue'
import { ACTION_LABEL } from '@/views/configure-mgr/jurisdiction/constant.ts'

const props = defineProps({
  id: {
    type: String,
    default: ''
  }
})
const isShowImport = ref(false)

const editRef = ref()
const addRef = ref()
const lessonEditRef = ref()
const importRef = ref()

const currentAction = ref<IActionData>({ action: ACTION.ADD, data: {} })

const router = useRouter()
function actionFn(val: IActionData) {
  currentAction.value = val
  if (val.action === ACTION.ADD) {
    addRef.value.showDialog()
    // return (isShowAside.value = true);
  } else if (val.action === ACTION.SEARCH) {
    return handleSearch(val.data)
  } else if (val.action === ACTION.DELETE) {
    return handleDelete(val.data as ICheckTempRow)
  } else if (val.action === ACTION.BLACK) {
    return handleSearch()
  } else if (val.action === ACTION.IMPORT) {
    importRef.value.showDialog()
    // return (isShowImport.value = true);
  } else if (val.action === ACTION.EDIT) {
    console.log(val, '++++++++++++++')
    editRef.value.showDialog(val.data.tenantryId)
  } else if (val.action === ACTION.LESSON_EDIT) {
    lessonEditRef.value.showDialog(
      val.data.tenantryId,
      val.data.projectManagementUnitId
    )
  }
}

const tableCompRef = ref()
function handleSearch(data?: IObj<any>) {
  if (data) {
    tableCompRef.value?.getTableDataWrap(data)
  } else {
    tableCompRef.value?.getTableData()
  }
}
// 删除
function handleDelete(data: ICheckTempRow) {
  $dialog.error({
    title: '删除',
    content: `确定要删除吗？`,
    positiveText: '确定',
    transformOrigin: 'center',
    onPositiveClick: async () => {
      deleteTenant({ id: data.tenantryId }).then(() => {
        // deleteTenant({ id: data.id }).then(() => {
        handleSearch()
      })
    }
  })
}

defineOptions({ name: 'serverIndexComp' })
</script>
<style scoped lang="scss">
.w_table_box {
  height: calc(100% - 124px);
}

.w-com-g-row-a1 {
  height: 100%;
  width: calc(100% - 264px);
  background-color: red;
}
</style>
