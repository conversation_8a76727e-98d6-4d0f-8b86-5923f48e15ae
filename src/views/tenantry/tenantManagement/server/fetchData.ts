// import { ICheckTempPageRes, ICheckItem, ICheckCategoryTree, IFormData } from './type';
import { IFormData } from './type';
import { IObj } from '@/types';
import { api } from '@/api';
import { $http } from '@tanzerfe/http';

// 项目列表
export function getTenantList(data: IObj<any>) {
  const url = api.getUrl(api.type.lease, api.name.lease.tenantPageList);
  return $http.post<any>(url, {
    data: { _cfg: { showTip: true }, ...data },
  });
}

// 删除
export function deleteTenant(query: IObj<any>) {
  const url = api.getUrl(api.type.lease, api.name.lease.delTenant, query);
  return $http.get(url, { data: { _cfg: { showTip: true, showOkTip: true } } });
}

// 创建
export function saveTenant(data: IObj<any>) {
  const url = api.getUrl(api.type.lease, api.name.lease.saveUnitTenant);
  return $http.post<IFormData>(url, { data: { _cfg: { showTip: true, showOkTip: true }, ...data } });
}

// 详情--单位信息
export function getUnitTenantDetail(query: IObj<any>) {
  const url = api.getUrl(api.type.lease, api.name.lease.getUnitTenantDetail, query);
  return $http.post<IFormData>(url, { data: { _cfg: { showTip: true } } });
}

// 编辑
export function queryUpdateTenant(data: IObj<any>) {
  const url = api.getUrl(api.type.lease, api.name.lease.updataTenant);
  return $http.post<IFormData>(url, { data: { _cfg: { showTip: true, showOkTip: true }, ...data } });
}

// 详情--承租信息
export function getTenantDetail(query: IObj<any>) {
  const url = api.getUrl(api.type.lease, api.name.lease.getTenantDetail, query);
  return $http.post<any>(url, { data: { _cfg: { showTip: true } } });
}

// 拉黑-取消黑
export function queryBlackTenant(data: IObj<any>) {
  const url = api.getUrl(api.type.lease, api.name.lease.blackTenant);
  return $http.post<IFormData>(url, { data: { _cfg: { showTip: true, showOkTip: true }, ...data } });
}

export function getUnitList(query: IObj<any>) {
  const url = api.getUrl(api.type.lease, api.name.lease.getUnitList, query);
  return $http.get(url, { data: { _cfg: { showTip: true } } });
}

export function queryDictList(query: IObj<any>) {
  const url = api.getUrl(api.type.lease, api.name.lease.queryDictList, query);
  return $http.get(url, { data: { _cfg: { showTip: true } } });
}

export function queryDictPageList(data: IObj<any>) {
  const url = api.getUrl(api.type.lease, api.name.lease.queryDictPageList);
  return $http.post(url, { data: { _cfg: { showTip: true }, ...data } });
}

export function exportTep() {
  const url = api.getUrl(api.type.lease, api.name.lease.downTep);
  return url;
}

export function getDetailByIdCard(query: IObj<any>) {
  const url = api.getUrl(api.type.lease, api.name.lease.getDetailByIdCard, query);
  return $http.post(url, { data: { _cfg: { showTip: true } } });
}

/**
 * 获取楼层
 */
// export function getFloorListByUnitIdAndBuilding(unitId: string, buildId: string) {
//   const url = api.getUrl(api.type.area, api.name.area.getFloorListByUnitIdAndBuilding, { unitId, buildId });
//   return $http.post<any[]>(url, { data: { _cfg: { showTip: true } } });
// }
//
// export function queryFloorRegionalList(unitId: string, buildingId: string, regionalId: string) {
//   const url = api.getUrl(api.type.area, api.name.area.queryFloorRegionalList, { unitId, buildingId, regionalId });
//   return $http.get<any[]>(url, { data: { _cfg: { showTip: true } } });
// }

export function getBuildingListByUnitIdNew(query: IObj<any>) {
  const url = api.getUrl(api.type.lease, api.name.lease.getBuildingListByUnitId, query);
  return $http.post<any[]>(url, { data: { _cfg: { showTip: true } } });
}

export function getFloorListByUnitId(query: IObj<any>) {
  const url = api.getUrl(api.type.lease, api.name.lease.getFloorListByUnitId, query);
  return $http.post<any[]>(url, { data: { _cfg: { showTip: true } } });
}

// 获取承租方下的所有区域
export function queryTenantManageArea(query: IObj<any>) {
  const url = api.getUrl(api.type.lease, api.name.lease.queryTenantManageArea, query);
  return $http.get<any[]>(url, { data: { _cfg: { showTip: true } } });
}

// 临时添加gis 数据
export function saveResArea(data: IObj<any>) {
  const url = api.getUrl(api.type.lease, api.name.lease.saveResArea);
  return $http.post(url, { data: { _cfg: { showTip: true }, ...data } });
}

// 获取资源下的所有区域
export function queryTenantManageAreaInRes(query: IObj<any>) {
  const url = api.getUrl(api.type.lease, api.name.lease.queryTenantManageAreaInRes, query);
  return $http.get<any[]>(url, { data: { _cfg: { showTip: true } } });
}

// 获取当前列表单位erecordUnitId
export function getErecordUnitId(query: IObj<any>) {
  const url = api.getUrl(api.type.lease, api.name.lease.getErecordUnitId, query);
  return $http.get(url, { data: { _cfg: { showTip: true } } });
}
