<template>
  <div class="w_table_box">
    <Filter class="com-table-filter" @action="actionFn" />
    <TableList ref="tableCompRef" @action="actionFn" :id="props.id" />
  </div>
</template>

<script setup lang="ts">
import Filter from './comp/Filter.vue';
import TableList from './comp/table/Table.vue';
import { $dialog } from '@/common/shareContext/useDialogCtx.ts';
import { ACTION } from './constant';
import { IActionData } from './type';
import { ICheckTempRow } from './type';
import { IObj } from '@/types';
import { ref } from 'vue';
import { useRouter } from 'vue-router';
const props = defineProps({
  id: {
    type: String,
    default: '',
  },
});

const currentAction = ref<IActionData>({ action: ACTION.ADD, data: {} });

const router = useRouter();
function actionFn(val: IActionData) {
  currentAction.value = val;
  if (val.action === ACTION.SEARCH) {
    return handleSearch(val.data);
  } else if (val.action === ACTION.BLACK) {
    return handleSearch();
  }
}

const tableCompRef = ref();
function handleSearch(data?: IObj<any>) {
  if (data) {
    tableCompRef.value?.getTableDataWrap(data);
  } else {
    tableCompRef.value?.getTableData();
  }
}

defineOptions({ name: 'checkTemplateComp' });
</script>
<style scoped lang="scss">
.w_table_box {
  height: calc(100% - 124px);
}
</style>
