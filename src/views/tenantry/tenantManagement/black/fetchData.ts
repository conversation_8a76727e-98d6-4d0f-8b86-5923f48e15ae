import { ICheckTempPageRes, ICheckItem, ICheckCategoryTree, IFormData } from './type';
import { IObj } from '@/types';
import { api } from '@/api';
import { $http } from '@tanzerfe/http';

// 黑名单列表
export function getTenantList(data: IObj<any>) {
  const url = api.getUrl(api.type.lease, api.name.lease.tenantPageList);
  return $http.post<any>(url, {
    data: { _cfg: { showTip: true, showOkTip: false }, ...data },
  });
}

// 拉黑-取消黑
export function queryBlackTenant(data: IObj<any>) {
  const url = api.getUrl(api.type.lease, api.name.lease.blackTenant);
  return $http.post<IFormData>(url, { data: { _cfg: { showTip: true, showOkTip: false }, ...data } });
}
