import { DataTableColumn } from 'naive-ui';
import { h } from 'vue';

export const cols: DataTableColumn[] = [
  {
    title: '序号',
    key: 'index',
    width: 65,
    align: 'center',
    render: (_: any, index: number) => {
      return index + 1;
    },
  },
  {
    title: '客户名称',
    key: 'tenantryName',
    align: 'center',
    width: 170,
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '管理员账号',
    key: 'tenantryAdminLogin',
    align: 'center',
    width: 150,
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '客户性质',
    key: 'tenantryNatureCodeName',
    align: 'center',
    width: 150,
  },
  {
    title: '客户负责人',
    key: 'tenantryManager',
    align: 'center',
    width: 150,
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '客户负责人手机号',
    key: 'tenantryManagerPhone',
    align: 'center',
    width: 150,
  },
  {
    title: '项目经营单位',
    key: 'projectManagementUnitName',
    align: 'center',
    width: 150,
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '项目名称',
    key: 'projectName',
    align: 'center',
    width: 150,
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '资源类型',
    key: 'resourceTypeCodeName',
    align: 'center',
    width: 150,
  },
  {
    title: '合同到期天数',
    key: 'contractExpirationDays',
    align: 'center',
    width: 150,
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '合同到期预警',
    key: 'contractExpirationWarningCode',
    align: 'center',
    width: 150,
    ellipsis: {
      tooltip: true,
    },
    render: (row: any) => {
      // // 判断 cardFileAttachment 是否为有效数组
      // const attachments = row.cardFileAttachment;
      // if (!attachments || (Array.isArray(attachments) && attachments.length === 0)) {
      //   return h('span', '--');
      // }
      let textSpan = '';
      if (row.contractExpirationWarningCod == 1) {
        textSpan = '即将过期';
      } else if (row.contractExpirationWarningCod == 2) {
        textSpan = '过期';
      } else {
        textSpan = '正常';
      }

      return h(
        'span',
        {
          text: true,
          class: row.contractExpirationWarningCode == 1 ? 'w_text_r' : '',
        },
        { default: () => textSpan }
      );
    },
  },
  {
    title: '创建人',
    key: 'createUserName',
    align: 'center',
    width: 150,
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '创建时间',
    key: 'createTime',
    align: 'center',
    width: 150,
    ellipsis: {
      tooltip: true,
    },
  },
];
