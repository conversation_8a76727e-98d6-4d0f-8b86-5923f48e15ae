<template>
  <div class="h-full w-full">
    <n-data-table
      class="h-full"
      remote
      striped
      :columns="columns"
      :data="tableData"
      :bordered="false"
      :flex-height="true"
      :loading="loading"
      :pagination="pagination"
      :render-cell="useEmptyCell"
      :scroll-x="2450"
    />

    <dialogCom v-model:show="isSHowDialog" title="取消黑名单" width="500px">
      <n-form ref="formRef" :model="formValue" :rules="rules" label-placement="left">
        <n-form-item label="" path="backlistLastReason">
          <n-input
            v-model:value="formValue.backlistLastReason"
            :autosize="{
              minRows: 3,
              maxRows: 5,
            }"
            type="textarea"
            maxlength="100"
            show-count
            disabled
          />
        </n-form-item>
      </n-form>
      <div class="flex justify-center mt-[16px]">
        <n-button class="!mr-[16px]" @click="handleClose">取消</n-button>
        <n-button type="primary" @click="handleSubmit">保存</n-button>
      </div>
    </dialogCom>
  </div>
</template>

<script setup lang="ts">
import { h, ref, VNode, toRaw } from 'vue';
import { DataTableColumns, NButton } from 'naive-ui';
import { cols } from './columns';
import { ACTION } from '../../constant.ts';
import { useRouter } from 'vue-router';
import { getTempPageList } from '@/views/configure-mgr/checklist-conf/check-template/fetchData';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import { useNaivePagination } from '@/common/hooks/useNaivePagination.ts';
import { useActionDivider } from '@/common/hooks/useActionDivider.ts';
import { IObj } from '@/types';
import { useEmptyCell } from '@/common/hooks/useEmptyCell.ts';
import dialogCom from '@/components/dialog/ComDialog.vue';
import { getTenantList, queryBlackTenant } from '@/views/tenantry/tenantManagement/black/fetchData.ts';

const props = defineProps({
  id: {
    type: String,
    default: '',
  },
});
const router = useRouter();
const emits = defineEmits(['action']);

const [loading, search] = useAutoLoading(true);
const { pagination, updateTotal } = useNaivePagination(getTableData);

const isSHowDialog = ref(false);
const columns = ref<DataTableColumns>([]);
const tableData = ref<any[]>([]);

const formValue = ref({
  backlistLastReason: '',
  tenantryId: '2',
  type: 0,
});

const rules = {
  backlistLastReason: {
    required: true,
    message: '请输入加入黑名单原因',
    trigger: ['input'],
  },
};

function setColumns() {
  columns.value.push(...cols);
  // 添加操作栏 action
  columns.value.push({
    title: '操作',
    key: 'actions',
    align: 'center',
    fixed: 'right',
    width: 200,
    render(row) {
      return getActionBtn(row);
    },
  });
}
setColumns();

function showDialog() {
  isSHowDialog.value = true;
}

function handleClose() {
  isSHowDialog.value = false;
}

// 取消黑名单
function handleSubmit() {
  // editRef.value?.handleSubmit();
  queryBlackTenant(formValue.value).then((res: any) => {
    console.log(res, 'add--');
    isSHowDialog.value = false;
    emits('action', { action: ACTION.BLACK });
  });
}

function getActionBtn(row: any) {
  const acList: [VNode, boolean?][] = [
    [
      h(
        NButton,
        {
          size: 'small',
          type: 'primary',
          ghost: true,
          class: 'com-action-button',
          onClick: () => router.push({ name: 'lesseeManage', params: { tenantryId: row.tenantryId } }),
        },
        { default: () => '项目详情' }
      ),
    ],
    [
      h(
        NButton,
        {
          size: 'small',
          type: 'primary',
          ghost: true,
          class: 'com-action-button',
          onClick: () => {
            formValue.value.tenantryId = row.tenantryId;
            formValue.value.backlistLastReason = row.backlistLastReason;
            showDialog();
          },
        },
        { default: () => '取消黑名单' }
      ),
    ],
  ];
  return useActionDivider(acList);
}

let filterData: IObj<any> = {}; // 搜索条件
function getTableData() {
  const params = {
    pageNo: pagination.page,
    pageSize: pagination.pageSize,
    ...filterData,
    projectManagementUnitId: props.id,
  };

  search(getTenantList(params)).then((res) => {
    tableData.value = res.data.rows || [];
    // tableData.value = [{}];
    updateTotal(res.data.total || 0);
  });
}

function getTableDataWrap(data: IObj<any>) {
  filterData = Object.assign({}, data) || {};
  pagination.page = 1;
  getTableData();
}
defineOptions({ name: 'checkTampTableComp' });
defineExpose({ getTableDataWrap, getTableData });
</script>
<style scoped lang="scss">
.w_text_r {
  color: red;
}
</style>
