import { ACTION } from './constant';
import { IObj, IPageRes } from '@/types';

export interface IActionData {
  action: ACTION;
  data: IObj<any>;
}

/**
 * 检查模板
 */
export interface ICheckTempRow {
  /**
   * 创建时间
   */
  createTime: string;
  /**
   * 创建人名称
   */
  createUser: string;
  /**
   * 消防监督检查模版
   */
  id: string;
  /**
   * 创建机构
   */
  orgCode: string;
  /**
   * 创建机构名称
   */
  orgName: string;
  /**
   * 模版名称
   */
  templateName: string;
  /**
   * 模版使用场景
   */
  templateScene: string;
  /**
   * 单位名称
   */
  unitId: string;
  /**
   * 单位名称
   */
  unitName: string;
  /**
   * 修改时间
   */
  updateTime: string;
  /**
   * 修改人
   */
  updateUser: string;
}

export type ICheckTempPageRes = IPageRes<ICheckTempRow>;

/**
 * 检查类别数
 */
export interface ICheckCategoryTree {
  attributes: { [key: string]: any };
  checked: boolean;
  children: ICheckCategoryTree[];
  hasChildren: boolean;
  hasParent: boolean;
  id: string;
  parentId: string;
  state: { [key: string]: any };
  text?: string;
  categoryId?: string;
  categoryName?: string;
  itemCategoryId?: string;
  itemCategoryName?: string;
}

/**
 * 检查项
 */
export interface ICheckItem {
  /**
   * 检查项分类id-大类
   */
  categoryId: string;
  categoryName: string;
  /**
   * 检查项分类id
   */
  itemCategoryId: string;
  itemCategoryName: string;
  /**
   * 检查项id
   */
  itemId: string;
  /**
   * 检查项名称
   */
  itemName?: string;
  /**
   * 检查项类型(1-单选，2-填空)
   */
  itemType?: string;
  /**
   * 选项信息类型
   */
  optionVoList?: ICheckOption[];
}

/**
 * 选项
 */
export interface ICheckOption {
  /**
   * 检查项分类id
   */
  itemCategoryId: string;
  /**
   * 关键选项（1-是 0-不是）
   */
  keyOption: string;
  /**
   * 选项类型（1-选择 2-填空）
   */
  keyType: string;
  /**
   * 选项标题
   */
  optionTitle: string;
}

export interface IFormTempItem {
  /**
   * 检查分类-大类
   */
  categoryId: string;
  /**
   * 检查分类-小类
   */
  itemCategoryId: string;
  /**
   * 检查项id
   */
  itemId: string;

  /**
   * 检查模版子表id
   */
  id?: string;
  /**
   * 检查项名称内容
   */
  itemName?: string;
  /**
   * 模版id
   */
  templateId?: string;
}

export interface IFormData {
  /**
   * 模版名称
   */
  templateName: string;
  /**
   * 模版使用场景
   */
  templateScene: string;

  /**
   * 检查项列表
   */
  templateItemList?: IFormTempItem[];

  /**
   * 消防监督检查模版
   */
  id?: string;
  /**
   * 创建机构
   */
  orgCode?: string;
  /**
   * 创建机构名称
   */
  orgName?: string;
  /**
   * 单位名称
   */
  unitId?: string;
  /**
   * 单位名称
   */
  unitName?: string;

  categoryCount?: number;
  itemCount?: number;
}
