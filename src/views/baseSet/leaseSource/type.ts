import { ACTION } from '../leaseResources/constant';
import { IObj, IPageRes } from '@/types';

export interface IActionData {
  action: ACTION;
  data: IObj<any>;
}

/**
 * 设备维护列表
 */
export interface DeviceMaintainItem {
  /**
   * 设备地址
   */
  deviceAddress: string;
  /**
   * 设备名称
   */
  deviceName: string;
  /**
   * 设备状态
   */
  deviceStatus: string;
  /**
   * 设备类型名称
   */
  deviceTypeName: string;
  /**
   * 上次维护时间
   */
  lastMaintainTime: string;
  /**
   * 生产日期
   */
  productDate: string;
  /**
   * 任务编号
   */
  taskId: string;
}
export type ISbwhPageListRes = IPageRes<DeviceMaintainItem>;
