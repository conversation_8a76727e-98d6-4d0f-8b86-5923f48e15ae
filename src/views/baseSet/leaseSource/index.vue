<template>
  <div class="table-wrap">
    <filter-comp class="com-table-filter" @action="actionFn" />
    <TableList class="com-table-container" ref="tableCompRef" @action="actionFn" />
    <!-- add or edit -->
    <CompDia v-model:show="isShowAside" :title="actionLabel" @action="actionFn" :data="rowData" />
    <!-- delete -->
    <DeleteComp v-model:show="isShowDelete" :title="actionLabel" @action="actionFn" :data="rowData" />
  </div>
</template>

<script setup lang="ts">
import TableList from './comp/table/Table.vue';
import FilterComp from './comp/Filter.vue';
import CompDia from './comp/aside/index.vue';
import DeleteComp from './comp/aside/delete.vue';
import { ACTION, PROVIDE_KEY, ACTION_LABEL } from '../leaseResources/constant.ts';
import { IActionData } from './type.ts';
import { IObj } from '@/types';
import { computed, ref, provide, Ref, onMounted } from 'vue';
import { editBaseSetting, operateState, deleteBaseSetting } from '../leaseResources/fetchData.ts';
import { $toast } from '@/common/shareContext/useToastCtx.ts';
import { $dialog } from '@/common/shareContext/useDialogCtx.ts';

const currentAction = ref<IActionData>({ action: ACTION.ADD, data: {} });
const actionLabel = computed(() => ACTION_LABEL[currentAction.value.action] + '承租方来源');
const isShowAside = ref<boolean>(false);
const isShowDelete = ref<boolean>(false);
const rowData = ref<{ name: string }>({ name: '' });

// provide
provide<Ref<IActionData>>(PROVIDE_KEY.currentAction, currentAction);

function actionFn(val: IActionData) {
  currentAction.value = val;
  if (val.action === ACTION.ADD || val.action === ACTION.EDIT) {
    rowData.value = JSON.parse(JSON.stringify(val.action === ACTION.ADD ? { name: '' } : (val.data as any)));
    isShowAside.value = true;
  }
  if (val.action === ACTION.ENABLE || val.action === ACTION.UNABLED) {
    const params = {
      id: val.data.id,
      status: val.action === ACTION.ENABLE ? 1 : 0,
    };
    operateState(params).then((res: any) => {
      console.log('res', res);
      if (res && res.code === 'success') {
        $toast.success(res.message);
        handleSearch();
      }
    });
  }
  if (val.action === ACTION.DELETE || val.action === ACTION.DELETECONFIRM) {
    rowData.value = val.data as any;
    // isShowDelete.value = val.action === ACTION.DELETE;
    // if (val.action === ACTION.DELETECONFIRM) {
    // }
    const params = {
      id: val.data.id,
    };
    $dialog.error({
      title: '删除',
      content: `确定要删除吗？`,
      positiveText: '确定',
      negativeText: '取消',
      transformOrigin: 'center',
      onPositiveClick: () => {
        deleteBaseSetting(params).then((res: any) => {
          if (res && res.code === 'success') {
            $toast.success(res.message);
            handleSearch();
          }
        });
      },
    });
  }
  // if (val.action === ACTION.DELETECONFIRM) {
  //   console.log('val', val);
  //   // isShowDelete.value = false;
  // }
  if (val.action === ACTION.UPDATE) {
    const data = {
      id: val.data.id ? val.data.id : '',
      type: 2,
      name: val.data.name,
    };
    editBaseSetting(data).then((res: any) => {
      $toast.success(res.message);
      handleSearch();
    });
  }
}

const tableCompRef = ref();
function handleSearch(data?: IObj<any>) {
  if (data) {
    tableCompRef.value?.getTableDataWrap(data);
  } else {
    tableCompRef.value?.getTableData();
  }
}

onMounted(() => {
  handleSearch();
});

defineOptions({ name: 'LeaseResourcesComp' });
</script>
<style module lang="scss"></style>
