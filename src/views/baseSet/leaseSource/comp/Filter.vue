<template>
  <n-form :show-feedback="false" label-placement="left">
    <ComHeaderC title="承租方来源设定" :activeIcon="true">
      <template #right>
        <n-flex :size="[20, 10]">
          <div class="flex-1 flex items-center justify-end">
            <n-button type="primary" @click="doHandle(ACTION.ADD)">
              <IconAdd />
              {{ ACTION_LABEL.ADD }}
            </n-button>
          </div>
        </n-flex>
      </template>
    </ComHeaderC>
  </n-form>
</template>

<script lang="ts" setup>
import { ACTION, ACTION_LABEL } from '../../leaseResources/constant';
import { computed, onMounted, ref, watch } from 'vue';
import { trimObjNull } from '@/utils/obj.ts';
import ComHeaderC from '@/components/header/ComHeaderC.vue';
import { BySearch as Search, FlFilledAdd as IconAdd } from '@kalimahapps/vue-icons';
// import { showAddBtn } from '@/views/device-mgr/sever.ts';
import { NIcon } from 'naive-ui';
import { api } from '@/api';

const emits = defineEmits(['action']);

function doHandle(action: ACTION) {
  emits('action', {
    action: action,
  });
}

onMounted(() => {});

defineOptions({ name: 'LeaseResourcesFilter' });
</script>

<style module lang="scss">
:deep(.com-table-filter) {
  padding: 0 24px !important;
}
</style>
