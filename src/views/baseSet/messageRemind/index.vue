<template>
  <div class="les-bottom">
    <n-form :show-feedback="false" label-placement="left" class="com-table-filter">
      <ComHeaderC title="消息提醒设定" :activeIcon="true"> </ComHeaderC>
      <div class="mt-[20px] text-[16px]">
        <span class="text-[#0249B1]">【合同即将到期提醒】：</span>
        当合同距离到期时间小于等于 30天时，消息提醒频率为每
        <div class="inline-block w-[105px]">
          <n-input-number
            v-model:value="remindCount"
            clearable
            :max="30"
            :min="1"
            @blur="handleRemindCount"
            placeholder=""
          />
        </div>
        天/次 ； 当合同距离到期时间小于等于
        <div class="inline-block w-[105px]">
          <n-input-number
            v-model:value="contractDay"
            clearable
            :max="30"
            :min="1"
            @blur="handleContractDay"
            placeholder=""
          />
        </div>
        天时，消息提醒频率为每1天/次 。
      </div>
    </n-form>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import ComHeaderC from '@/components/header/ComHeaderC.vue';
import { getResourceInfo, editBaseSetting } from '../leaseResources/fetchData';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import { PageModelBaseConfigDto } from '../leaseResources/type';

const remindCount = ref<number>(5);
const [loading, search] = useAutoLoading(false);
const setId = ref<string>('');
const contractId = ref<string>('');
const contractDay = ref<number>(5);

// 获取设置天数
function getSetData() {
  const data = {
    type: 4,
  };

  search(getResourceInfo(data)).then((res: PageModelBaseConfigDto) => {
    remindCount.value = +res.data.rows[0].name;
    contractDay.value = +res.data.rows[1].name;
    setId.value = res.data.rows[0].id;
    contractId.value = res.data.rows[1].id;
  });
}

function handleRemindCount() {
  if (!remindCount.value) remindCount.value = 5;
  const data = {
    id: setId.value,
    name: remindCount.value,
  };
  editBaseSetting(data).then((res: any) => {
    if (res && res.code === 'success') {
    }
  });
}
function handleContractDay() {
  if (!contractDay.value) contractDay.value = 5;
  const data = {
    id: contractId.value,
    name: contractDay.value,
  };
  editBaseSetting(data).then((res: any) => {
    if (res && res.code === 'success') {
    }
  });
}

onMounted(() => {
  getSetData();
});
defineOptions({ name: 'MiddleComp' });
</script>

<style lang="scss" scoped>
.les-bottom {
  // height: 419px;
  background: #eef7ff;

  .les-middle-title {
    height: 56px;
    background: #dce4f4;
    border-radius: 9px 9px 0px 0px;
    font-weight: 500;
    font-size: 16px;
    color: #222222;
    line-height: 56px;
    padding-left: 25px;
  }

  .les-middle-content {
    // height: 441px;
    padding: 20px 23px;
    display: flex;
    justify-content: space-between;
  }
}
</style>
