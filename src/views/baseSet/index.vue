<template>
  <div class="com-g-row">
    <ComBread :data="breadData" />
    <n-scrollbar>
      <div class="base-set">
        <LeaseResources />
        <LeaseSource />
        <LeaseType />
        <!--        <MessageRemind />-->
      </div>
    </n-scrollbar>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, Ref } from 'vue';
import ComBread from '@/components/breadcrumb/ComBread.vue';
import LeaseResources from './leaseResources/index.vue';
import LeaseSource from './leaseSource/index.vue';
import LeaseType from './leaseType/index.vue';
import MessageRemind from './messageRemind/index.vue';
import { ITabItem } from '@/components/tab/type';
import { IBreadData } from '@/components/breadcrumb/type.ts';
import { useRoute, useRouter } from 'vue-router';

const router = useRouter();
const route = useRoute();

// tabs
const tabList: Array<ITabItem & { comp: any }> = [
  { name: 'leaseResources', label: '承租资源类型设定', comp: LeaseResources },
  { name: 'leaseSource', label: '承租方来源设定', comp: LeaseSource },
  { name: 'leaseType', label: '计租模式设定', comp: LeaseType },
];

const curTab = ref<string>((route.query?.tab as string) || 'leaseResources');
function handleChange(name: string) {
  curTab.value = name;
  router.push({
    query: { tab: name },
  });
}

const currentComp = computed(() => {
  return tabList.find((item) => item.name === curTab.value)?.comp || LeaseResources;
});

const breadData: Ref<IBreadData[]> = computed(() => [{ name: '基础配置' }]);

defineOptions({ name: 'HomeIndex' });
</script>

<style scoped lang="scss">
.com-g-row {
  display: grid;
  grid-template-rows: 45px 1fr 10px;
}

.base-set {
  display: grid;
  //grid-template-rows: 1fr 1fr 1fr 140px;
  grid-template-rows: 1fr 1fr 1fr;
  gap: 60px;

  :deep(.com-table-filter) {
    padding: 16px 24px 0px 24px;
  }
}
</style>
