import { ACTION } from '../leaseResources/constant';
import { IObj, IPageRes } from '@/types';

export interface IActionData {
  action: ACTION;
  data: IObj<any>;
}

/**
 * 基础配置
 *
 * BaseConfigDto
 */
export interface BaseConfigDto {
  /**
   * 创建人
   */
  createdName?: null | string;
  /**
   * 创建时间
   */
  createdTime?: null | string;
  /**
   * 主键
   */
  id?: null | string;
  /**
   * 名称
   */
  name?: null | string;
  /**
   * 状态：0-禁用，1-启用
   */
  status?: number | null;
  /**
   * 状态名称
   */
  statusName?: null | string;
  [property: string]: any;
}
export type ZYPageListRes = IPageRes<BaseConfigDto>;

export interface ICategory {
  id: string;
  name: string;
  type: string;
  parentId: string | null;
  status: string;
  createTime: string;
  updateTime: string;
  createUser: string;
  updateUser: string;
  delFlag: string;
}

/**
 * PageModel«BaseConfigDto»
 */
export interface PageModelBaseConfigDto {
  pageNo?: number | null;
  pages?: number | null;
  pageSize?: number | null;
  rows?: BaseConfigDto[] | null;
  total?: number | null;
  [property: string]: any;
}

/**
 * ResponseModelObject
 */
export interface Response {
  code?: string;
  data?: { [key: string]: any };
  dataType?: string;
  message?: { [key: string]: any };
  status?: string;
  token?: string;
  [property: string]: any;
}
