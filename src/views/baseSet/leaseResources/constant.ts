export const enum PROVIDE_KEY {
  currentAction = 'currentAction',
}

export const enum ACTION {
  SEARCH = 'SEARCH',
  EDIT = 'EDIT',
  ENABLE = 'ENABLE',
  DELETE = 'DELETE',
  UNABLED = 'UNABLED',
  ADD = 'ADD',
  UPDATE = 'UPDATE',
  DELETECONFIRM = 'DELETECONFIRM',
}

export const ACTION_LABEL: { [key in ACTION]: string } = {
  [ACTION.SEARCH]: '搜索',
  [ACTION.EDIT]: '编辑',
  [ACTION.ENABLE]: '启用',
  [ACTION.DELETE]: '删除',
  [ACTION.UNABLED]: '禁用',
  [ACTION.ADD]: '新增',
  [ACTION.UPDATE]: '更新',
  [ACTION.DELETECONFIRM]: '确定删除',
};
