<template>
  <ComDialog v-model:show="dialogShow" width="400px" :mask-closable="false" @after-leave="handleClose">
    <div class="text-center text-lg">确认删除吗？</div>
    <div class="flex justify-end mt-[16px]">
      <n-button class="!mr-[16px]" @click="handleClose">取消</n-button>
      <n-button type="primary" @click="handleSubmit">确定</n-button>
    </div>
  </ComDialog>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import ComDialog from '@/components/dialog/ComDialog.vue';
import { ACTION } from '../../constant';

const porps = defineProps({
  data: {
    type: Object,
    default: () => ({}),
  },
});
const emits = defineEmits(['action', 'update:show']);
const dialogShow = ref(false);

function handleSubmit() {
  emits('action', {
    action: ACTION.DELETECONFIRM,
    data: porps.data,
  });
  handleClose();
}

function handleClose() {
  emits('update:show' as any, false); // any-屏蔽组件内透传的emit类型
}

defineOptions({ name: 'LeaseResourcesAside' });
</script>

<style module lang="scss"></style>
