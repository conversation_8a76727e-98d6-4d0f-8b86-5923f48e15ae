<template>
  <ComDialog v-model:show="dialogShow" title="新增物资" width="400px" :mask-closable="false" @after-leave="handleClose">
    <n-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-placement="left"
      label-align="right"
      label-width="140"
      require-mark-placement="left"
    >
      <n-form-item label="承租资源类型：" path="name">
        <n-input v-model:value="formData.name" maxlength="50" placeholder="请输入承租资源类型" @input="filterInput" />
      </n-form-item>
    </n-form>
    <div class="flex justify-end mt-[16px]">
      <n-button class="!mr-[16px]" @click="handleClose">取消</n-button>
      <n-button type="primary" @click="handleSubmit">保存</n-button>
    </div>
  </ComDialog>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue';
import ComDialog from '@/components/dialog/ComDialog.vue';
import { ACTION } from '../../constant';
import { FormInst, FormRules } from 'naive-ui';

const porps = defineProps({
  data: {
    type: Object,
    default: () => ({}),
  },
});
const emits = defineEmits(['action', 'update:show']);
const dialogShow = ref(false);
const formRef = ref<FormInst | null>(null);
const formData = ref({ name: '' });

const validateName = (rule, value, callback) => {
  if (value && value.length > 50) {
    callback(new Error('姓名不能超过50个字符'));
  } else {
    callback(); // 校验通过
  }
};
const rules: FormRules = {
  name: [
    { required: true, trigger: ['blur', 'change'], message: '请输入承租资源类型' },
    { validator: validateName, trigger: ['input', 'blur'] },
  ],
};

function filterInput(value: string) {
  // 只允许汉字和字母数字
  const validInput = value.replace(/[^a-zA-Z0-9\u4e00-\u9fa5]/g, '');
  formData.value.name = validInput;
}

function handleSubmit() {
  formRef.value?.validate((errors) => {
    if (!errors) {
      emits('action', {
        action: ACTION.UPDATE,
        data: formData.value,
      });
      handleClose();
    }
  });
}

function handleClose() {
  emits('update:show' as any, false); // any-屏蔽组件内透传的emit类型
}

watch(
  () => porps.data,
  (val: { name: string }) => {
    formData.value = val;
  },
  { immediate: true }
);
defineOptions({ name: 'LeaseResourcesAside' });
</script>

<style module lang="scss"></style>
