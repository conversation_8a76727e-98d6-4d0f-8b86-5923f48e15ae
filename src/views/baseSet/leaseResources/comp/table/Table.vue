<template>
  <n-data-table
    class="h-full"
    remote
    striped
    :columns="columns"
    :data="tableData"
    :bordered="false"
    :flex-height="true"
    :loading="loading"
    :pagination="pagination"
    :render-cell="useEmptyCell"
    style="min-height: 400px"
  />
</template>

<script setup lang="ts">
import { h, ref, VNode, toRaw } from 'vue';
import { DataTableColumns, NButton } from 'naive-ui';
import { cols } from './columns.ts';
import { ACTION } from '../../constant.ts';
import { useRouter } from 'vue-router';
import { getResourceInfo } from '../../fetchData.ts';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import { useNaivePagination } from '@/common/hooks/useNaivePagination.ts';
import { useActionDivider } from '@/common/hooks/useActionDivider.ts';
import { IObj } from '@/types';
import { useEmptyCell } from '@/common/hooks/useEmptyCell.ts';
import { BaseConfigDto, PageModelBaseConfigDto } from '../../type.ts';

const router = useRouter();
const emits = defineEmits(['action']);

const [loading, search] = useAutoLoading(false);
const { pagination, updateTotal } = useNaivePagination(getTableData);

const columns = ref<DataTableColumns>([]);
const tableData = ref<BaseConfigDto[]>([]);
function setColumns() {
  columns.value.push(...cols);
  // 添加操作栏 action
  columns.value.push({
    title: '操作',
    key: 'actions',
    align: 'center',
    width: 350,
    render(row) {
      return getActionBtn(row);
    },
  });
}
setColumns();

function getActionBtn(row: any) {
  const acList: [VNode, boolean?][] =
    row.status === 0
      ? [
          [
            h(
              NButton,
              {
                type: 'primary',
                ghost: true,
                class: 'com-action-button',
                onClick: () => emits('action', { action: ACTION.EDIT, data: toRaw(row) }),
              },
              { default: () => '编辑' }
            ),
          ],
          [
            h(
              NButton,
              {
                type: 'primary',
                ghost: true,
                class: 'com-action-button',
                onClick: () => emits('action', { action: ACTION.ENABLE, data: toRaw(row) }),
              },
              { default: () => '启用' }
            ),
          ],
          [
            h(
              NButton,
              {
                type: 'error',
                ghost: true,
                class: 'com-action-button_error',
                onClick: () => emits('action', { action: ACTION.DELETE, data: toRaw(row) }),
              },
              { default: () => '删除' }
            ),
          ],
        ]
      : [
          [
            h(
              NButton,
              {
                type: 'error',
                ghost: true,
                class: 'com-action-button_error unabled',
                onClick: () => emits('action', { action: ACTION.UNABLED, data: toRaw(row) }),
              },
              { default: () => '禁用' }
            ),
          ],
        ];
  return useActionDivider(acList);
}

let filterData: IObj<any> = {}; // 搜索条件
function getTableData() {
  const data = {
    pageNo: pagination.page,
    pageSize: pagination.pageSize,
    // ...filterData,
    type: 1,
  };

  search(getResourceInfo(data)).then((res: PageModelBaseConfigDto) => {
    tableData.value = res.data.rows || [];
    updateTotal(res.data.total || 0);
  });
}

function getTableDataWrap(data: IObj<any>) {
  // filterData = Object.assign({}, data) || {};
  pagination.page = 1;
  getTableData();
}
defineOptions({ name: 'EquipmentMaintenanceTableComp' });
defineExpose({ getTableDataWrap, getTableData });
</script>
<style module lang="scss"></style>
<style scoped lang="scss">
:deep(.unabled) {
  // float: right;
  // margin-right: 60px;
}
</style>
