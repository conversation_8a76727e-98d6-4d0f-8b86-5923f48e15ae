import { DataTableColumn, NButton } from 'naive-ui';
import { h } from 'vue';

export const cols: DataTableColumn[] = [
  {
    title: '序号',
    key: 'index',
    align: 'center',
    width: 65,
    render: (_: any, index: number) => {
      return index + 1;
    },
  },
  {
    title: '承租资源名称',
    key: 'name',
    align: 'center',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '创建人',
    key: 'createdName',
    align: 'center',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '创建时间',
    key: 'createdTime',
    align: 'center',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '状态',
    key: 'status',
    align: 'center',
    render(row: any) {
      return h(
        NButton,
        {
          style: {
            // marginRight: '6px',
            width: '60px',
            height: '30px',
            // display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            cursor: 'text',
            backgroundColor: Number(row.status) === 0 ? '#DC2828' : '#50B616',
          },
          type: 'primary',
          bordered: false,
        },
        {
          default: () => row.statusName,
        }
      );
    },
  },
];
