import { ICategory } from './type';
import { IObj } from '@/types';
import { api } from '@/api';
import { $http } from '@tanzerfe/http';

// 更新检查模板
// export function updateTemplate(data: IObj<any>) {
//   const url = api.getUrl(api.type.demo, api.name.demo.updateTemplate, data);
//   return $http.post<IFormData>(url, { data: { _cfg: { showTip: true, showOkTip: true }, ...data } });
// }
// 查询资源类型分页
export function getResourceInfo(data: IObj<any>) {
  const url = api.getUrl(api.type.lease, api.sct.resourcePageList);
  return $http.post<ICategory>(url, { data: { _cfg: { showTip: true }, ...data } });
}

// 新增 编辑修改配置
export function editBaseSetting(data: IObj<any>) {
  const url = api.getUrl(api.type.lease, api.sct.addOrUpdate);
  return $http.post<ICategory>(url, { data: { _cfg: { showTip: true }, ...data } });
}

// 操作启用/禁用
export function operateState(query: IObj<any>) {
  const url = api.getUrl(api.type.lease, api.sct.operateState, query);
  return $http.get(url, { data: { _cfg: { showTip: true } } });
}

// 删除基础配置
export function deleteBaseSetting(query: IObj<any>) {
  const url = api.getUrl(api.type.lease, api.sct.deleteBaseSetting, query);
  return $http.get(url, { data: { _cfg: { showTip: true } } });
}
