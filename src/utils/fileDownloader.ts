import type { OptionalParams as fileOptionalParams } from 'js-file-downloader';
import JsFileDownloader from 'js-file-downloader';
// import JsF from ''
/**
 * 文件下载
 * @param url
 * @param options
 */
export function fileDownloader(url: string, options: fileOptionalParams = {}) {
  const defaultOpt: fileOptionalParams = {
    timeout: 2 * 60 * 1000,
    contentTypeDetermination: 'header',
    headers: [],
  };

  return new JsFileDownloader({
    url,
    ...defaultOpt,
    ...options,
    nameCallback: (name: string) => {
      return options.filename || name;
    },
  });
}
