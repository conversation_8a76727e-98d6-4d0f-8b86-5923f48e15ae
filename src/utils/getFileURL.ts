const isAbsoluteURL = (url: string) => {
  const absoluteURLRegex = /^https?:\/\//;
  return absoluteURLRegex.test(url);
};

// 检查原图路径是否以 ".jpg", ".png", ".jpeg" 等常见图片格式结尾
const imageExtension = /\.(jpeg|jpg|png|gif|bmp|tiff|webp)$/i;

const getFileURL = (filePath: string, preview = false) => {
  // 如果是绝对路径，就直接使用
  if (isAbsoluteURL(filePath)) return filePath;
  const isImg = filePath.match(imageExtension);
  const baseUrl = filePath.includes('img1') ? location.origin + '/' : window.$SYS_CFG.apiImageURL;
  return baseUrl + (preview && isImg ? replaceWithThumbnailPath(filePath, '_160X160.jpg') : filePath);
};

/**
 * 替换原图路径为缩略图路径
 * @param {string} originalImagePath - 原图的完整路径
 * @param {string} thumbnailSuffix - 缩略图路径的后缀或参数（例如 "_80X160.jpg" 或 "?thumbnail=true"）
 * @returns {string} - 缩略图的完整路径
 */
function replaceWithThumbnailPath(originalImagePath: string, thumbnailSuffix: string) {
  const originalExtension = originalImagePath.match(imageExtension);
  if (!originalExtension) {
    throw new Error('原图路径没有有效的图片扩展名');
  }

  // 如果缩略图后缀包含文件扩展名（例如 "_80X160.jpg"），则直接使用它替换原图的扩展名
  // 否则，将缩略图后缀添加到原图的扩展名之前
  if (thumbnailSuffix.includes('.')) {
    // 缩略图后缀包含点，表示它有自己的扩展名，直接替换原图的扩展名
    return originalImagePath.replace(imageExtension, thumbnailSuffix);
  } else {
    // 缩略图后缀不包含点，表示它是一个参数或简单的后缀，添加到原图的扩展名之前
    return originalImagePath.replace(imageExtension, `${thumbnailSuffix}${originalExtension[0]}`);
  }
}

// // 示例用法
// const originalImagePath = 'https://example.com/original/img2/capture/987654XYZ321987654321/event/2025-03-15/4561237890123456789.jpg';
// const thumbnailSuffix = '_100X200.jpg'; // 或者 '?thumbnail=true'，根据实际情况调整
// const thumbnailImagePath = replaceWithThumbnailPath(originalImagePath, thumbnailSuffix);

export default getFileURL;
