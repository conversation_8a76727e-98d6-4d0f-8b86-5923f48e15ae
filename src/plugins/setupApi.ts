import { $Config } from '@tanzerfe/http';
import useToastCtx from '@/common/shareContext/useToastCtx.ts';
import { useAuthStore } from '@/store/modules';

export function setupApi() {
  const store = useAuthStore();
  // const store = useStore();

  // $Config.getToken = () => store.userInfo.userToken;
  $Config.getToken = () => store.userInfo.token;
  $Config.$toastDark = useToastCtx({ theme: 'dark' });
  $Config.$toastLight = useToastCtx({ theme: 'light' });
  $Config.requestErrorHandler = function (error) {
    if (error.code === 'ERR_NETWORK') location.reload();
  };
}
