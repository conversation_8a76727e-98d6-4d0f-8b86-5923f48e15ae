export default {
  warnList: '/index/v1/warnInfo', // 查询预警信息
  itemDistribution: '/index/v1/itemDistribution', // 承租方项目分布
  checkTaskList: '/index/v1/execInfo', //检查任务执行情况
  hazardGivernInfo: '/index/v1/hazardGivernInfo', // 隐患治理情况
  typeDistribution: '/index/v1/typeDistribution', // 承租方类型分布
  resourcePageList: '/config/v1/pageList', // 基础配置-资源类型分页
  addOrUpdate: '/config/v1/addOrUpdate', // 新增或修改基础配置
  operateState: '/config/v1/operateState', // 操作基础配置状态
  deleteBaseSetting: '/config/v1/delete', // 删除基础配置

  queryToken: '/goLogin/queryToken', // 统一登录获取token
  queryIsAdmin: '/goLogin/queryIsAdmin', // 判断是否是管理员

  getOrgTreeList: '/common/v1/getOrgListTree', // 获取机构树-列表专用
};
