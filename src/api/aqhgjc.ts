/**
 * 此文件接口为示例代码需要，仅供参考，后期需删除
 */
export default {
  demo: {
    safeComplianceCheckPage: '/safeComplianceCheck/page', //安全合规检查-分页
    hazardDisposeSupervisePageEvent: '/hazardDisposeSupervise/pageEvent', //隐患整改督办-分页
    hazardDisposeSuperviseStatistics: '/hazardDisposeSupervise/statistics', //隐患整改督办-隐患数统计
    hazardDisposeSuperviseLevelStatistics: '/hazardDisposeSupervise/levelStatistics', //隐患整改督办-隐患事件等级统计
    hazardDisposeSuperviseLevelGradeList: '/hazardDisposeSupervise/gradeList', //隐患分级-列表

    hazardDisposeSuperviseLevelQueryUserTenantInfoList: '/hazardDisposeSupervise/queryUserTenantInfoList', //查询隐患单位下拉
    hazardDisposeSuperviseLevelTimeoutPage: '/hazardDisposeSupervise/timeoutPage', //获取隐患超期时间分页-查询
    hazardDisposeSuperviseSaveUrgeRecord: '/hazardDisposeSupervise/saveUrgeRecord', //催促记录-保存
    hazardDisposeSuperviseRecord: '/hazardDisposeSupervise/record', //处置记录-查询
    hazardDisposeSuperviseQueryEventDetail: '/hazardDisposeSupervise/queryEventDetail', //隐患详情
    hazardDisposeSuperviseQueryDictList: '/dict/v1/queryDictList', //资源类型
    hazardDisposeSuperviseGetOrgTree: '/common/v1/getOrgTree', //获取机构树
  },
  file: {
    uploadFile: '/file/uploadfile', // 上传文件
  },
};
