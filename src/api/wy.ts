export default {
  lease: {
    tenantPageList: '/tenant/manage/pageList', // 项目列表
    delTenant: '/tenant/manage/del', // 项目删除
    saveUnitTenant: '/tenant/manage/addRTenantry/BaseInfo', // 新增承租方-单位信息
    getUnitTenantDetail: '/tenant/manage/getDetail', // 查看详情-单位信息
    updataTenant: '/tenant/manage/addRTenantryInfo', // 新增/更新-承租信息
    getTenantDetail: '/tenant/manage/getRTenantryInfoDetail', // 查看详情-承租信息
    blackTenant: '/tenant/manage/black', // 拉黑-取消黑/tenant/manage/getContractList
    getContractList: '/tenant/manage/getContractList', // 获取承租方合同
    getRTenantryInfoDetail: '/tenant/manage/getRTenantryInfoDetail', // 查看详情-承租信息

    pageUserList: '/tenant/app/pageUserList', // 人员列表
    addUser: '/tenant/app/addUser', // 承租方-人员新增
    updateUser: '/tenant/app/updateUser', // 承租方-人员编辑
    detailUser: '/tenant/app/detailUser', // 承租方-人员详情
    delUser: '/tenant/app/delUser', // 承租方-人员删除
    saveTenantFile: '/tenant/app/saveTenantFile', // 承租方附件修改
    getUnitList: '/common/v1/getUnitList', // 获取下级单位列表
    queryDictList: '/dict/v1/queryDictList', // 字典查询
    queryDictPageList: '/config/v1/pageList', // 字典查询2
    downTep: '/tenant/manage/exportTenantryTemplete', // 下载模版
    importTenantry: '/tenant/manage/importTenantry', // 导入
    getBuildingListByUnitId: '/areaManage/v1/getBuildingListByUnitId', // 根据单位ID获取楼栋
    getFloorListByUnitId: '/areaManage/v1/getFloorListByUnitId', // 根据单位ID楼栋id获取楼栋楼层
    queryTenantManageArea: '/areaManage/v1/queryTenantManageArea', // 获取承租方下的所有区域
    queryTenantManageAreaInRes: '/areaManage/v1/queryTenantManageAreaInRes', // 获取资源下的所有区域
    getDetailByIdCard: '/tenant/manage/getDetailByIdCard', //查看详情-根据用户身份证号
    saveResArea: '/areaManage/v1/saveResArea', // 保存获取资源区域-临时
    getErecordUnitId: '/tenant/manage/getOrgToErecord' // 获取记录单位id
  },

  file: {
    uploadFiles: '/file/upload', // 上传文件
  },

  area: {
    getBuildingListByUnitId: '/regionalManagement/getBuildingListByUnitId',
    getFloorListByUnitIdAndBuilding: '/regionalManagement/getFloorListByUnitIdAndBuilding',
    queryForeignKeyRegionalList: '/regionalManagement/queryForeignKeyRegionalList',
    updRegionalManagement: '/regionalManagement/updRegionalManagement',
    queryFloorRegionalList: '/regionalManagement/getFloorListByUnitIdAndBuilding',
  },
};
