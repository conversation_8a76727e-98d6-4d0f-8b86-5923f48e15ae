export default {
  getCheckTaskExecuteCondition: '/hazardEssentialFactorAnalyse/getCheckTaskExecuteCondition', // 检查任务执行情况
  getHazardEssentialFactorCondition: '/hazardEssentialFactorAnalyse/getHazardEssentialFactorCondition', // 隐患治理情况
  getPlanTaskPageList: '/hazardEssentialFactorAnalyse/getPlanTaskPageList', // 隐患治理情况
  getHazardPlanDetail: '/hazardEssentialFactorAnalyse/getHazardPlanDetail',
  getHazardStatistics: '/hazardEssentialFactorAnalyse/hazardPlanTaskEvent/statistics',
  unfinishedHazardStatistics: '/hazardEssentialFactorAnalyse/hazardRecord/statistics',
  unfinishedGetHazardPageList: '/hazardEssentialFactorAnalyse/hazardRecord/pageEvent',
  getDispsoseNodeRecord: '/hazardEssentialFactorAnalyse/dispose/record', // 隐患分析-任务详情-处置记录查询
  qeuryEventDetail: '/hazardEssentialFactorAnalyse/hazardEvent/queryEventDetail', // 隐患分析-任务详情-隐患详情
  hazardTaskList: '/hazardTask/page', // 隐患任务列表
  exportTaskList: '/hazardTask/exportTaskList', // 隐患任务列表导出
  hazardPlanTaskEventList: '/hazardEssentialFactorAnalyse/hazardPlanTaskEvent/page',
  getTaskClockInList: '/hazardEssentialFactorAnalyse/hazardTask/getTaskClockInList',
  getHazardPageList: '/hazardRecord/getHazardPageList',
  getHazardInventory: '/hazardRecord/pageEvent', // 任务详情-隐患清单列表
  getAllUnit: '/ehsUpms/getAllUnit', // 获取隐患单位列表
};
