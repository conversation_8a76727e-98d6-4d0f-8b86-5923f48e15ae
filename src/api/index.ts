import { stringify } from 'querystringify';
import { merge } from 'lodash-es';
// api file
import zyx from './zyx';
import ww from './ww';
import sct from './sct';
import wy from './wy';
import aqhgjc from './aqhgjc.ts';
export const api = {
  type: {
    nil: '',
    file: '/ehs-clnt-tenant-service', // 文件上传服务
    demo: 'resource-server', // demo服务-供参考用，新的服务请在下面接着写
    // lessee: '', //接口服务前缀
    lease: '/ehs-clnt-tenant-service',
    area: '/ehs-clnt-unit-area-service',
  },
  name: merge(zyx, ww, sct, wy, aqhgjc),
  ww,
  sct,

  /**
   * 组装请求地址
   * @param serviceType
   * @param apiName
   * @param query
   */
  getUrl(serviceType: string, apiName: string, query?: any): string {
    let url = window.$SYS_CFG.apiBaseURLTZ;

    // 匹配示例接口
    if (import.meta.env.DEV && serviceType === api.type.demo) {
      url = window.$SYS_CFG.apiBaseURLDemo;
    }

    // 公司测试调用
    // if (import.meta.env.DEV && serviceType === api.type.lessee) {
    //   url = window.$SYS_CFG.apiBaseURL;
    // }

    const paramsStr = query ? `?${stringify(query)}` : '';
    const _apiName = apiName.indexOf('/') === 0 ? apiName : '/' + apiName;
    // const _serviceType = serviceType ? '/' + serviceType : '';

    return `${url}${serviceType}${_apiName}${paramsStr}`;
  },
};
