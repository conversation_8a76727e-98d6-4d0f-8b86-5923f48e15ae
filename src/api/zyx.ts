/**
 * 此文件接口为示例代码需要，仅供参考，后期需删除
 */
export default {
  demo: {
    jurisdictionPageList: '/config/jurisdiction/v1/pageList', // 管辖范围-列表
    jurisdictionDetail: '/config/jurisdiction/v1/detail', // 管辖范围-详情
    jurisdictionUpdate: '/config/jurisdiction/v1/update', // 管辖范围-编辑-POST
    getSysDictByType: '/sysDict/v1/getDictDataByTypeFromCache', // 根据字典类型获取字典
    getTempPageList: '/config/template/v1/pageList', // 检查模板列表
    tempDelete: '/config/template/v1/delete', // 删除模板
    queryCategoryTree: '/config/inspect/v1/queryCategoryTree', // 获取检查类别树
    queryInspectItems: '/config/inspect/v1/queryInspectItems', // 获取检查模板项
    saveTemplate: '/config/template/v1/saveTemplate', // 保存检查模板
    queryDetailTemplate: '/config/template/v1/queryDetailTemplate', // 模板详情
    detailCategoryTree: '/config/template/v1/templateDetailCategoryTree', // 模板详情类别树
    queryTemplateDetailItems: '/config/template/v1/queryInspectTemplateItems', // 模板详情类别树
    queryUpdateTemplateDetail: '/config/template/v1/queryUpdateTemplateDetail', // 模板详情
    updateTemplate: '/config/template/v1/updateTemplate', // 更新检查模板
    statisUnitFirePatrol: '/firepatrol/v1/statisUnitFirePatrol', // 防火巡查统计
    statisUnitFireInspect: '/fireinspect/v1/statisUnitFireInspect', // 防火检查统计
    getUnitInfo: '/unit/v1/getUnitInfo', // 获取单位信息
    queryUnitDutyCalendar: '/firectrroom/duty/v1/queryUnitDutyCalendar', // 获取单位值班日历
    listDutyTask: '/firectrroom/duty/v1/listDutyTask', // 获取值班任务列表
    pageDutyManList: '/firectrroom/dutyman/v1/pageList', // 分页查询消控室值班人员列表
    queryParentCategory: '/config/inspect/v1/queryParentCategory', // 获取检查大类类别
    saveInspectItem: '/config/inspect/v1/saveInspectItem', // 保存检查项
    delInspectCategoryTree: '/config/inspect/v1/delInspectCategoryTree', // 删除检查类别信息
    saveCategory: '/config/inspect/v1/saveCategory', // 创建检查类别
    updateCategory: '/config/inspect/v1/updateCategory', // 编辑检查类别
    queryCategoryInfoById: '/config/inspect/v1/queryCategoryInfoById', // 根据ID查询检查类别信息
    queryInspectItemDetail: '/config/inspect/v1/queryInspectItemDetail', // 获取检查项信息详情
    updateItem: '/config/inspect/v1/updateItem', // 更新检查项
  },
  file: {
    uploadFile: '/file/uploadfile', // 上传文件
  },
};
