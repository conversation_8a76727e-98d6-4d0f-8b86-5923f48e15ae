<template>
  <div class="w-full pt-[5px]">
    <n-upload
      response-type="json"
      name="file"
      :action="actionURL"
      :default-file-list="defaultFileList"
      :on-finish="handleUploadFinish"
      :on-update:file-list="handleUpdate"
      :on-before-upload="handleBeforeUpload"
      :accept="props.accept"
      :max="max"
      :headers="headers"
    >
      <n-button type="primary">点击上传</n-button>
      <div class="mt-[10px] text-[#999999]" v-if="props.tips">{{ props.tips }}</div>
    </n-upload>
  </div>
</template>

<script lang="ts" setup>
import { computed } from 'vue';
import { UploadFileInfo, useMessage } from 'naive-ui';
import { IUploadRes } from '@/components/upload/type';
import { api } from '@/api';
import { $toast } from '@/common/shareContext/useToastCtx.ts';
import { useAuthStore } from '@/store/modules';
// import { useMndCtx } from '@/store/shareContext/useMndCtx';
const store = useAuthStore();
// import { useMessage } from 'naive-ui';
defineOptions({ name: 'fileExcel' });

const headers = {
  Token: store.userInfo.token,
};

// const message = useMessage();
interface IItem {
  fjMc: string;
  fjCflj: string;
  fjTywysbm: string;
}
interface Props {
  data: IItem[];
  mode?: string; // detail为展示图片 其他为上传图片
  max?: number; // 限制上传数量
  size?: number; // 限制文件大小
  accept?: string; // 文件类型
  tips?: string; // 上传提示
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: '',
  data: () => [],
  max: 6,
  accept: '',
});
const emits = defineEmits(['update', 'success', 'error']);
const valueRef = computed<any[]>(() => props.data);
const actionURL = api.getUrl(api.type.lease, api.name.lease.importTenantry);

// 默认文件列表(用于编辑时回显已上传文件)
const defaultFileList = computed<UploadFileInfo[]>(() => {
  const ret: UploadFileInfo[] = [];

  for (const item of valueRef.value) {
    ret.push({
      id: item.fjTywysbm,
      name: item.fjMc,
      url: getFileURL(item.fjCflj),
      status: 'finished',
    });
    fileResMap.set(item.fjTywysbm, item);
  }
  return ret;
});
const fileResMap: Map<any, IUploadRes> = new Map(); // 上传结果map

// 上传前校验(文件大小、类型)
function handleBeforeUpload(options: { file: UploadFileInfo }) {
  const { file } = options;
  if (!file.file) return false;
  const fileExt = file.name.slice(file.name.lastIndexOf('.') + 1);
  if (props.accept && !props.accept.includes(fileExt)) {
    $toast.error(`请上传 ${props.accept} 类型的文件!`);
    return false;
  } else if (props.size && file.file.size / 1024 / 1024 > props.size) {
    $toast.error(`文件不能超过 ${props.size} MB，请重新上传!`);
    return false;
  }
  return true;
}

// 上传完成
function handleUploadFinish(options: { file: UploadFileInfo; event?: ProgressEvent }) {
  console.log(options, 'shangcayayay');

  const { file, event } = options;
  console.log(event, 'event');
  const data = (<any>event?.target)?.response?.data || {};
  fileResMap.set(file.id, data);
  // message.error(11111111111111);
  // console.log(message)
  console.log(event.srcElement.response, 'lalalall');
  if (event.srcElement.response.code == '500') {
    $toast.error(event.srcElement.response.message);
    return;
  }
  // if (event.srcElement.response.code != 'success') {
  //   $toast.error(event.srcElement.response.message);
  //   // message.error(event.srcElement.response.message);
  // } else {
  //   $toast.success(event.srcElement.response.message);
  // }
  // console.log();
  emits('success', event.srcElement.response);
}

// 当文件数组改变时触发的回调函数
function handleUpdate(val: UploadFileInfo[]) {
  const finishedList = val.filter((item) => item.status === 'finished');

  const res: any[] = [];
  const data: any[] = [];
  finishedList.forEach((item) => {
    const _res = fileResMap.get(item.id) || null;
    res.push(_res);
    data.push(Object.assign(item, { res: _res }));
  });

  return emits('update', res, data);
}

function getFileURL(filePath: string) {
  return window.$SYS_CFG.apiServiceFileURL + filePath;
}
</script>

<style scoped></style>
