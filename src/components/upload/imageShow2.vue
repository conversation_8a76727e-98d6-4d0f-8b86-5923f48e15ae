<template>
  <div class="w-full pt-[15px]" :key="defaultFileList[0]?.id">
    <n-upload
      response-type="json"
      name="file"
      :action="actionURL"
      :default-file-list="defaultFileList"
      :on-finish="handleUploadFinish"
      :on-update:file-list="handleUpdate"
      :on-before-upload="handleBeforeUpload"
      :accept="props.accept"
      :max="max"
      @preview="handlePreview"
    >
      <n-button type="primary">点击上传</n-button>
      <div class="mt-[10px] text-[#999999]" v-if="props.tips">{{ props.tips }}</div>
      <!--      <IconAdd />-->
      <!--      <n-modal v-model:show="showModal" preset="card" style="width: 600px">-->
      <!--        <img :src="previewImageUrl" style="width: 100%" />-->
      <!--      </n-modal>-->
    </n-upload>
  </div>
</template>

<script lang="ts" setup>
import { computed, ref, watch } from 'vue';
import { UploadFileInfo } from 'naive-ui';
import { IUploadRes } from '@/components/upload/type';
import { $toast } from '@/common/shareContext/useToastCtx.ts';
import { api } from '@/api';
import { FlFilledAdd as IconAdd } from '@kalimahapps/vue-icons';
import { trimObjNull } from '@/utils/obj.ts';

defineOptions({ name: 'ImgUpload' });

interface IItem {
  fileName: string;
  path: string;
  id: string;
}
interface Props {
  data: IItem[];
  mode?: string; // detail为展示图片 其他为上传图片
  max?: number; // 限制上传数量
  size?: number; // 限制文件大小
  accept?: string; // 文件类型
  tips?: string; // 上传提示
  type: string; // 上传项
  auto: string;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: '',
  data: () => [],
  max: 6,
  accept: '.jpg,.jpeg,.png,.webp,.gif,.bmp,.svg',
  type: '1',
  auto: '',
});
const emits = defineEmits(['update', 'automatic']);
const valueRef = computed<any[]>(() => props.data);
const actionURL = api.getUrl(api.type.file, api.name.file.uploadFiles);
// 查看图片
const showModal = ref(false);
const previewImageUrl = ref('');
function handlePreview(file: UploadFileInfo) {
  console.log('file', file);
  const { url } = file;
  previewImageUrl.value = url as string;
  showModal.value = true;
}

// 默认文件列表(用于编辑时回显已上传文件)
// const defaultFileList = ref<UploadFileInfo[]>([]);
const defaultFileList = computed<UploadFileInfo[]>(() => {
  const ret: UploadFileInfo[] = [];

  for (const item of props.data) {
    ret.push({
      id: item.id,
      name: item.fileName,
      url: getFileURL(item.path),
      status: 'finished',
    });
    fileResMap.set(item.id, item);
  }
  return ret;
});
const fileResMap: Map<any, IUploadRes> = new Map(); // 上传结果map

// 上传前校验(文件大小、类型)
function handleBeforeUpload(options: { file: UploadFileInfo }) {
  console.log(options, 'opppppppppppppppp');
  const { file } = options;
  // getCherck(file.file);
  if (!file.file) return false;
  const fileExt = file.name.slice(file.name.lastIndexOf('.') + 1);
  if (!props.accept.includes(fileExt)) {
    $toast.error(`请上传 ${props.accept} 类型的文件!`);
    return false;
  } else if (props.size && file.file.size / 1024 / 1024 > props.size) {
    $toast.error(`文件不能超过 ${props.size} MB，请重新上传!`);
    return false;
  }
  return true;
}

// 上传完成
function handleUploadFinish(options: { file: UploadFileInfo; event?: ProgressEvent }) {
  const { file, event } = options;
  console.log(options, 'options----');
  console.log(file, 'file----');
  console.log(event, 'file----');
  // getCherck(file);
  if (props.auto) {
    getCherck(file.file);
  }
  const data = (<any>event?.target)?.response?.data || {};
  fileResMap.set(file.id, data);
}

// 当文件数组改变时触发的回调函数
function handleUpdate(val: UploadFileInfo[]) {
  const finishedList = val.filter((item) => item.status === 'finished');

  const res: any[] = [];
  const data: any[] = [];
  finishedList.forEach((item) => {
    const _res = fileResMap.get(item.id) || null;
    res.push(_res);
    data.push(Object.assign(item, { res: _res }));
  });
  console.log('res', res);
  return emits('update', res, props.type);
}

function getFileURL(filePath: string) {
  return window.$SYS_CFG.apiImageURL + filePath;
}

async function getCherck(file: any) {
  const formData = new FormData(); // 创建FormData对象
  const aBlob = new Blob([file], { type: file.type });
  formData.append('file', aBlob);
  formData.append('type_of_card', 1);
  // let url = 'https://agjp.tanzervas.com/third/ocr';
  let url = '/third/ocr';
  try {
    const response = await fetch(url, {
      // 替换为你的上传API地址
      method: 'POST',
      body: formData,
    });

    if (response.ok) {
      // 处理上传成功的结果
      // console.log('File uploaded successfully');
      // console.log(response);
      response.json().then((res) => {
        emits('automatic', {
          data: res,
          auto: props.auto,
        });
      });
    } else {
      // 处理上传失败的结果
      console.error('Upload failed');
    }
  } catch (error) {
    console.error('Error:', error);
  }
}

watch(
  () => props.data,
  (val) => {
    // console.log('val', val);
    // for (const item of props.data) {
    //   defaultFileList.value.push({
    //     id: item.id,
    //     name: item.fileName,
    //     url: getFileURL(item.path),
    //     status: 'finished',
    //   });
    //   fileResMap.set(item.id, item as any);
    // }
    // console.log('defaultFileList', defaultFileList);
  },
  { immediate: true }
);
</script>

<style scoped></style>
