/**
 * OrgTree
 */
export interface IOrgTree {
  /**
   * 节点属性
   */
  attributes: { [key: string]: any };
  /**
   * 点是否被选中
   */
  checked: boolean;
  /**
   * 节点的子节点
   */
  children: IOrgTree[];
  hasChildren: boolean;
  hasParent: boolean;
  /**
   * 主键id
   */
  id: string;
  /**
   * 层级
   */
  level: number;
  /**
   * 层级编号
   */
  levelCode: string;
  /**
   * 父ID
   */
  parentId: string;
  state: string;
  /**
   * 节点名称
   */
  text: string;
  /**
   * 树名
   */
  treeName: string;
  /**
   * 节点类型
   */
  type: string;
  /**
   * 节点id
   */
  typeId: string;
  [property: string]: any;
}
