<!--
 * @Author: fang<PERSON><PERSON> <EMAIL>
 * @Date: 2024-09-13 11:53:14
 * @LastEditors: fangweiwei <EMAIL>
 * @LastEditTime: 2024-10-17 10:48:46
 * @FilePath: \ehs-risk-mgr\src\components\tree\index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div :class="[$style.treeInput]">
    <n-input v-model:value="pattern" placeholder="搜索" clearable>
      <template #suffix>
        <BySearch :class="$style['icon']" />
      </template>
    </n-input>
  </div>
  <div :class="[$style.comTree]" class="h-[calc(100%-100px)] overflow-auto">
    <n-scrollbar content-class="h-full">
      <n-tree class="tree-cont" :show-irrelevant-nodes="false" :pattern="pattern" :data="data" block-line
        :override-default-node-click-behavior="override" :render-prefix="renderPrefix"
        :default-expanded-keys="defaultExpandedkeys" :default-selected-keys="defaultSelectedkeys"
        :default-expand-all="true" :render-label="renderLabel" key-field="id" label-field="text" size="large" style="
          --n-node-content-height: 44px;
          --n-line-height: 44px;
          --n-node-border-radius: 4px;
          --n-node-color-hover: rgba(82, 124, 255, 0.1);
        " />
    </n-scrollbar>
  </div>
</template>

<script lang="ts" setup>
// import { changeGisUnitId } from '@/utils/linkage';
import dept from '@/assets/dept.png';
import Icon2 from '@/assets/menu-child.png';
import Icon1 from '@/assets/menu-father.png';
import { useAuthStore } from '@/store/modules';
import { BySearch } from '@kalimahapps/vue-icons';
import type { TreeOption } from 'naive-ui';
import { NInput } from 'naive-ui';
import { h, ref, watch } from 'vue';

const emits = defineEmits(['action']);
const ui = useAuthStore();
const pattern = ref('');
const defaultExpandedkeys = ref<string[]>([]);
const defaultSelectedkeys = ref<string[]>([]);
type listT = {
  data: TreeOption[];
};
const props = defineProps<listT>();

watch(
  () => props.data,
  (nv) => {
    console.log('nv', nv);
    if (props.data.length) {
      let curKey = props.data[0].id as string;
      defaultExpandedkeys.value = [curKey];
      defaultSelectedkeys.value.push(curKey);
      // 本地有就不能选择unitID
      // const _st = localStorage.getItem('_riskUnitID');
      // if (_st) {
      //   defaultSelectedkeys.value.push(_st);
      // } else {
      //   calleArr(props.data);
      // }
    }
  },
  { immediate: true }
);

function calleArr(array: Array<any>) {
  var data = array;
  // if (data[0].children?.length) {
  //   calleArr(data[0].children);
  // } else {
  // }
  defaultSelectedkeys.value.push(data[0].id);
  localStorage.setItem('_riskUnitID', data[0].id);
  localStorage.setItem('_riskUnitName', data[0].text);
  emits('action', data[0]);
}

function override({ option }: { option: TreeOption }) {
  const op: any = option;
  // 集团不能新增风险点， 选择了有child就不做改变
  // if (op?.children?.length) return 'toggleExpand';
  localStorage.setItem('_riskUnitID', op.id);
  localStorage.setItem('_riskUnitName', op.text);
  // changeGisUnitId(op.id);
  emits('action', option);
}

function getIcon() {
  return ui.userInfo.zhLogoUrl + ui.userInfo.zhLogo + '.png';
}

function renderPrefix({ option }: { option: any }) {
  let img = option.attributes.orgType === '2' ? Icon1 : option.attributes.orgType === '1' ? Icon2 : dept;
  if (option.parentId === '-1') img = getIcon();

  return h('img', { src: img, class: 'w-[20px] h-[20px]' });
}

function renderLabel({ option }: { option: any }) {
  return h('div', { class: 'w-full truncate', title: option.text }, option.text);
}

defineOptions({ name: 'ComRadioTabE' });
</script>

<style module lang="scss">
.treeInput {
  margin: 16px 0 12px 0;

  .icon {
    color: #c0c4cc;
  }
}

.comTree {
  .n-tree .n-tree-node {
    padding-left: 10px !important;
  }
}
</style>
<style scoped lang="scss">
.tree-cont {
  &.n-tree {
    .n-tree-node {
      padding-left: 20px !important;
    }
  }
}

:deep(.n-tree-node-content__text),
:deep(.line) {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  // width: calc(100% - 20px);
  // display: inline-block;
}

:global(.tree-cont .n-tree-node-switcher:first-child) {
  display: none;
}

:global(.tree-cont .n-tree-node-content__text) {
  @apply flex-1 w-0;
}

/* 定制滚动条整体 */
::-webkit-scrollbar {
  width: 4px;
  /* 宽度 */
}

/* 定制滚动条轨道 */
::-webkit-scrollbar-track {
  background-color: #f1f1f1;
  /* 轨道颜色 */
}

/* 定制滚动条滑块 */
::-webkit-scrollbar-thumb {
  background-color: #888;
  /* 滑块颜色 */
  border-radius: 6px;
  /* 圆角 */
}

/* 滑块在鼠标悬停时改变颜色 */
::-webkit-scrollbar-thumb:hover {
  background-color: #555;
}
</style>
