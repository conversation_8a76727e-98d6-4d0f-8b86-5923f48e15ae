import { ITaskState2 } from '@/views/lessee-manage/components/HazardAnalyze/type';

import { defineComponent, h } from 'vue';

const bg = {
  [ITaskState2.已停用]: '#F56C6C',
  [ITaskState2.待开始]: '#E6A23C',
  [ITaskState2.进行中]: '#527CFF',
  [ITaskState2.已完成]: '#67C23A',
};

export const taskState = defineComponent({
  name: 'taskState',
  props: {
    row: {
      type: Object,
      default: () => ({}),
    },
    field: {
      type: String,
      default: 'taskState',
    },
  },
  setup(props) {
    const key = props.field;
    console.log(props.row);
    console.log(key);
    console.log(props.row[key]);
    console.log(bg[props.row[key]]);

    return () =>
      h(
        'div',
        {
          className: 'inline-block w-[66px] leading-[30px] h-[30px]  text-center rounded-[4px] text-white',
          style: `background-color: ${bg[props.row[key] as ITaskState2]}`,
        },
        // { default: () => ITaskState2[props.row[key]] }
        { default: () => props.row.taskStateName }
      );
  },
});
