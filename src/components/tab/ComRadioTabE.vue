<template>
  <ul :class="$style['radio-tab']">
    <li
      v-for="tabItem in tabList"
      :key="tabItem.name"
      :class="[$style['radio-tab-nav'], tab === tabItem.name ? $style['active'] : '']"
      @click="handleUpdateValue(tabItem.name)"
    >
      <span>{{ tabItem.label }}</span>
    </li>
  </ul>
</template>

<script setup lang="ts">
import type { ITabItem } from './type';

interface Props {
  tab: string;
  tabList: ITabItem[];
}

const props = defineProps<Props>();
const emits = defineEmits(['change']);

function handleUpdateValue(name: string) {
  const data = props.tabList.find((v) => v.name === name);
  emits('change', name, data);
}

defineOptions({ name: 'ComRadioTabE' });
</script>

<style module lang="scss">
.radio-tab {
  @apply w-full flex h-[42px] z-0;
}

.radio-tab-nav {
  @apply relative h-[50px] leading-[42px] text-[16px] px-[12px] pb-[8px] mr-[24px] cursor-pointer;
  background: var(--com-container-bg);

  &:first-child {
    margin-left: 10px;

    &:before {
      transform: skew(0);
      left: -10px;
    }
  }

  &:before,
  &:after {
    content: '';
    @apply absolute top-0 w-[20px] h-full bg-[#f4f9ff] rounded-t-[16px];
  }

  &:before {
    transform: skew(-14deg);
    left: -14px;
  }

  &:after {
    z-index: 1;
    transform: skew(14deg);
    right: -14px;
    box-shadow: 10px 0 10px -4px rgba(0, 0, 0, 0.1);
  }

  &.active {
    @apply text-white bg-[#527CFF];

    &:before,
    &:after {
      @apply bg-[#527CFF] z-[2];
    }
  }
}
</style>
