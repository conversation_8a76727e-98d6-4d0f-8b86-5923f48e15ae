<template>
  <div class="header flex justify-between items-center p-3" style="width: 100%">
    <div class="flex justify-start items-center">
      <slot name="h-icon" v-if="hasIcon">
        <img :src="activeColor ? activeIcon : normalIcon" alt="" />
      </slot>
      <div class="title">{{ title }}</div>
    </div>
    <slot name="right"> </slot>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import activeIcon from './assets/icon-title-arrow2.png';
import arrow3Icon from './assets/icon-title-arrow3.png';
import titleArrowIcon from './assets/icon-title-arrow.png';

interface Props {
  title: string;
  activeColor?: boolean;
  blueIcon?: boolean;
  hasIcon?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  title: '',
  activeColor: false,
  blueIcon: true,
  hasIcon: true,
});

const normalIcon = computed(() => {
  if (props.blueIcon) {
    return arrow3Icon;
  } else {
    return titleArrowIcon;
  }
});

defineOptions({ name: 'ComHeaderB' });
</script>

<style lang="scss" scoped>
.header {
  --bgc: rgba(82, 124, 255, 0.15);
}
::v-deep.header {
  width: 100%;
  height: 44px;
  img {
    width: 17px;
    height: 12px;
    margin-right: 20px;
  }
  background: var(--bgc);
  border-radius: 4px 4px 4px 4px;

  .n-input,
  .n-input .n-input__input-el,
  .n-button {
    height: 32px;
    line-height: 32px;
  }
}

.title {
  font-weight: 700;
  font-size: 16px;
  color: #527cff;
  &:hover {
    color: #fff;
  }
}
</style>
