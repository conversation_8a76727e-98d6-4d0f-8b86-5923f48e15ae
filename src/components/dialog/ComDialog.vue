<template>
  <n-modal v-bind="props" transform-origin="center" ref="dialogRef">
    <div class="custom-dialog-wrapper" :style="{ width: width }">
      <div class="custom-dialog-header">
        <span class="custom-dialog-title" :class="{ 'title-center': center }">{{ title }}</span>
        <n-icon v-if="showClose" class="custom-dialog-headerBtn">
          <GlClose @click="closeHandle" />
        </n-icon>
      </div>
      <div class="custom-dialog-content">
        <slot />
      </div>
    </div>
  </n-modal>
</template>

<script setup lang="ts">
import { GlClose } from '@kalimahapps/vue-icons';
import { ModalProps } from 'naive-ui';
import { ref } from 'vue';

interface CustomDialogProps extends /* @vue-ignore */ ModalProps {
  title: string;
  width?: string;
  center?: boolean;
  showClose?: boolean;
}

const props = withDefaults(defineProps<CustomDialogProps>(), {
  title: '标题',
  width: '50%',
  center: false,
  showClose: true,
});

const dialogRef: any = ref(null);
const closeHandle = () => {
  dialogRef.value.doUpdateShow(false);
  dialogRef.value.handleAfterLeave();
};

defineOptions({ name: 'ComDialog' });
</script>

<style lang="scss" scoped>
.custom-dialog-wrapper {
  @apply rounded-[8px] box-border bg-skin-bg1;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);

  .custom-dialog-header {
    @apply relative h-[52px] pl-[24px] flex items-center justify-between rounded-t-[8px];
    border-bottom: 1px solid #ebeef5;

    &:before {
      @apply absolute left-[12px] w-[3px] h-[16px] bg-[#527CFF] rounded-[2px];
      content: '';
    }

    .custom-dialog-title {
      @apply block w-full text-[#222222] text-[16px] leading-[24px];
    }

    .title-center {
      @apply text-center;
    }

    .custom-dialog-headerBtn {
      @apply absolute top-1/2 right-[18px] text-[18px] text-black cursor-pointer;
      transform: translateY(-50%);
    }
  }

  .custom-dialog-content {
    @apply p-[24px];
  }
}
</style>
